import * as vscode from "vscode";

export enum LogLevel {
	DEBUG = 0,
	INFO = 1,
	WARN = 2,
	ERROR = 3,
}

export class Logger {
	private static outputChannel: vscode.OutputChannel | undefined;
	private static logLevel: LogLevel = LogLevel.INFO;

	/**
	 * Initialize the logger with an output channel
	 * @param channelName Name of the output channel
	 */
	static initialize(channelName: string = "Terminal Manager"): void {
		this.outputChannel = vscode.window.createOutputChannel(channelName);
	}

	/**
	 * Set the minimum log level
	 * @param level Minimum log level to display
	 */
	static setLogLevel(level: LogLevel): void {
		this.logLevel = level;
	}

	/**
	 * Log a debug message
	 * @param message Message to log
	 * @param data Optional additional data
	 */
	static debug(message: string, data?: any): void {
		this.log(LogLevel.DEBUG, message, data);
	}

	/**
	 * Log an info message
	 * @param message Message to log
	 * @param data Optional additional data
	 */
	static info(message: string, data?: any): void {
		this.log(LogLevel.INFO, message, data);
	}

	/**
	 * Log a warning message
	 * @param message Message to log
	 * @param data Optional additional data
	 */
	static warn(message: string, data?: any): void {
		this.log(LogLevel.WARN, message, data);
	}

	/**
	 * Log an error message
	 * @param message Message to log
	 * @param data Optional additional data
	 */
	static error(message: string, data?: any): void {
		this.log(LogLevel.ERROR, message, data);
	}

	/**
	 * Show the output channel
	 */
	static show(): void {
		this.outputChannel?.show();
	}

	/**
	 * Hide the output channel
	 */
	static hide(): void {
		this.outputChannel?.hide();
	}

	/**
	 * Dispose of the logger resources
	 */
	static dispose(): void {
		this.outputChannel?.dispose();
		this.outputChannel = undefined;
	}

	/**
	 * Internal logging method
	 * @param level Log level
	 * @param message Message to log
	 * @param data Optional additional data
	 */
	private static log(level: LogLevel, message: string, data?: any): void {
		if (level < this.logLevel) {
			return;
		}

		const timestamp = new Date().toISOString();
		const levelName = LogLevel[level];
		const logMessage = `[${timestamp}] [${levelName}] ${message}`;

		// Log to console for development
		switch (level) {
			case LogLevel.DEBUG:
				console.debug(logMessage, data || "");
				break;
			case LogLevel.INFO:
				console.info(logMessage, data || "");
				break;
			case LogLevel.WARN:
				console.warn(logMessage, data || "");
				break;
			case LogLevel.ERROR:
				console.error(logMessage, data || "");
				break;
		}

		// Log to output channel if available
		if (this.outputChannel) {
			const outputMessage = data ? `${logMessage} ${JSON.stringify(data)}` : logMessage;
			this.outputChannel.appendLine(outputMessage);
		}
	}
}
