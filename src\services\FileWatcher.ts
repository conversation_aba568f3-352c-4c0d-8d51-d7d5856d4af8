import * as vscode from "vscode";
import * as chokidar from "chokidar";
import * as path from "path";
import { Logger } from "./logging/Logger";

export class FileWatcher implements vscode.Disposable {
	private watcher: chokidar.FSWatcher | undefined;
	private reloadTimeout: NodeJS.Timeout | undefined;
	private readonly debounceDelay = 2000; // 2 seconds debounce
	private isReloading = false;

	constructor(private context: vscode.ExtensionContext) {
		this.initialize();
	}

	private async initialize() {
		try {
			const workspaceFolders = vscode.workspace.workspaceFolders;
			if (!workspaceFolders || workspaceFolders.length === 0) {
				Logger.warn("No workspace folders found, auto-reload disabled");
				return;
			}

			const rootPath = workspaceFolders[0].uri.fsPath;
			const srcPath = path.join(rootPath, "src");

			// Watch source files for changes
			this.watcher = chokidar.watch("**/*.{ts,js,json}", {
				cwd: srcPath,
				ignored: [
					"**/*.test.ts",
					"**/*.spec.ts",
					"**/node_modules/**",
					"**/out/**",
					"**/.git/**",
					"**/coverage/**"
				],
				ignoreInitial: true,
				persistent: true
			});

			this.watcher.on("change", (filePath) => {
				this.scheduleReload(filePath, "changed");
			});

			this.watcher.on("add", (filePath) => {
				this.scheduleReload(filePath, "added");
			});

			this.watcher.on("unlink", (filePath) => {
				this.scheduleReload(filePath, "deleted");
			});

			this.watcher.on("error", (error) => {
				Logger.error("File watcher error", error);
			});

			// Also watch package.json for dependency changes
			const packageJsonPath = path.join(rootPath, "package.json");
			const packageWatcher = chokidar.watch(packageJsonPath, {
				ignoreInitial: true
			});

			packageWatcher.on("change", () => {
				this.scheduleReload("package.json", "changed");
			});

			Logger.info(`File watcher initialized, watching: ${srcPath}`);

		} catch (error) {
			Logger.error("Failed to initialize file watcher", error);
		}
	}

	private scheduleReload(filePath: string, action: string) {
		if (this.isReloading) {
			Logger.debug(`Reload already in progress, skipping for: ${filePath}`);
			return;
		}

		// Clear existing timeout
		if (this.reloadTimeout) {
			clearTimeout(this.reloadTimeout);
		}

		// Schedule reload with debounce
		this.reloadTimeout = setTimeout(() => {
			this.reloadExtension(filePath, action);
		}, this.debounceDelay);

		Logger.debug(`Scheduled extension reload for: ${filePath} (${action})`);
	}

	private async reloadExtension(triggerFile: string, action: string) {
		if (this.isReloading) {
			return;
		}

		this.isReloading = true;
		Logger.info(`Reloading extension triggered by: ${triggerFile} (${action})`);

		try {
			// Show reload notification
			const reloadMessage = `File ${action}: ${path.basename(triggerFile)}. Reloading extension...`;
			
			const result = await vscode.window.showInformationMessage(
				reloadMessage,
				{ modal: false },
				"Reload Now",
				"Cancel Auto-Reload"
			);

			if (result === "Reload Now") {
				await this.performReload();
			} else if (result === "Cancel Auto-Reload") {
				await this.disableAutoReload();
			} else {
				// Auto-reload after a short delay if no user interaction
				setTimeout(async () => {
					await this.performReload();
				}, 1000);
			}

		} catch (error) {
			Logger.error("Extension reload failed", error);
			vscode.window.showErrorMessage(`Extension reload failed: ${error}`);
		} finally {
			this.isReloading = false;
		}
	}

	private async performReload() {
		try {
			Logger.info("Performing extension reload...");
			
			// Show progress
			await vscode.window.withProgress({
				location: vscode.ProgressLocation.Notification,
				title: "Reloading Extension...",
				cancellable: false
			}, async (progress) => {
				progress.report({ message: "Stopping extension..." });
				
				// Execute the reload window command
				await vscode.commands.executeCommand("workbench.action.reloadWindow");
			});

		} catch (error) {
			Logger.error("Failed to reload extension", error);
			throw error;
		}
	}

	private async disableAutoReload() {
		try {
			const config = vscode.workspace.getConfiguration("terminalManager");
			await config.update("autoReload", false, vscode.ConfigurationTarget.Workspace);
			
			vscode.window.showInformationMessage("Auto-reload disabled for this workspace");
			Logger.info("Auto-reload disabled by user");
			
			this.dispose();
			
		} catch (error) {
			Logger.error("Failed to disable auto-reload", error);
			vscode.window.showErrorMessage(`Failed to disable auto-reload: ${error}`);
		}
	}

	/**
	 * Manually trigger extension reload
	 */
	async manualReload() {
		if (this.isReloading) {
			vscode.window.showWarningMessage("Extension reload already in progress");
			return;
		}

		const result = await vscode.window.showWarningMessage(
			"Are you sure you want to reload the extension?",
			{ modal: true },
			"Reload",
			"Cancel"
		);

		if (result === "Reload") {
			await this.performReload();
		}
	}

	dispose() {
		if (this.reloadTimeout) {
			clearTimeout(this.reloadTimeout);
		}
		
		if (this.watcher) {
			this.watcher.close();
		}
		
		Logger.info("File watcher disposed");
	}
}
