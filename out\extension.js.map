{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,sDAA6D;AAC7D,gEAA6D;AAC7D,0DAAuD;AACvD,wDAAqD;AACrD,yCAA8C;AAE9C,IAAI,eAAgC,CAAC;AACrC,IAAI,YAA0B,CAAC;AAC/B,IAAI,WAAwB,CAAC;AAE7B,SAAgB,QAAQ,CAAC,OAAgC;IACxD,oBAAoB;IACpB,eAAM,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC;IAEtC,0CAA0C;IAC1C,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,KAAK,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC;IACjF,eAAM,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,iBAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAQ,CAAC,IAAI,CAAC,CAAC;IAEnE,eAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;IAE3D,IAAI;QACH,2BAA2B;QAC3B,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;QAExC,sCAAsC;QACtC,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;QACpE,MAAM,kBAAkB,GAAG,MAAM,CAAC,GAAG,CAAU,aAAa,EAAE,IAAI,CAAC,CAAC;QAEpE,IAAI,kBAAkB,EAAE;YACvB,YAAY,GAAG,IAAI,2BAAY,CAAC,OAAO,CAAC,CAAC;YACzC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SACzC;QAED,0CAA0C;QAC1C,MAAM,iBAAiB,GAAG,MAAM,CAAC,GAAG,CAAU,YAAY,EAAE,IAAI,CAAC,CAAC;QAElE,IAAI,iBAAiB,IAAI,aAAa,EAAE;YACvC,WAAW,GAAG,IAAI,yBAAW,CAAC,OAAO,CAAC,CAAC;YACvC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SACxC;QAED,oBAAoB;QACpB,IAAA,2BAAgB,EAAC,OAAO,EAAE,eAAe,CAAC,CAAC;QAE3C,mCAAmC;QACnC,MAAM,sBAAsB,GAAG,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,EAAE;YAC9E,IAAI,CAAC,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,EAAE;gBAC9C,yBAAyB,CAAC,OAAO,CAAC,CAAC;aACnC;QACF,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAEnD,eAAM,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;QAEjE,2CAA2C;QAC3C,IAAI,aAAa,EAAE;YAClB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,+DAA+D,CAAC,CAAC;SACtG;KAED;IAAC,OAAO,KAAK,EAAE;QACf,eAAM,CAAC,KAAK,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;QACrE,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wCAAwC,KAAK,EAAE,CAAC,CAAC;KAChF;AACF,CAAC;AArDD,4BAqDC;AAED,SAAgB,UAAU;IACzB,eAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;IAE7D,IAAI;QACH,qBAAqB;QACrB,IAAI,YAAY,EAAE;YACjB,YAAY,CAAC,OAAO,EAAE,CAAC;SACvB;QAED,IAAI,WAAW,EAAE;YAChB,WAAW,CAAC,OAAO,EAAE,CAAC;SACtB;QAED,eAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;KACnE;IAAC,OAAO,KAAK,EAAE;QACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;KACjD;YAAS;QACT,eAAM,CAAC,OAAO,EAAE,CAAC;KACjB;AACF,CAAC;AAnBD,gCAmBC;AAED,SAAS,yBAAyB,CAAC,OAAgC;IAClE,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;IACpE,MAAM,kBAAkB,GAAG,MAAM,CAAC,GAAG,CAAU,aAAa,EAAE,IAAI,CAAC,CAAC;IACpE,MAAM,iBAAiB,GAAG,MAAM,CAAC,GAAG,CAAU,YAAY,EAAE,IAAI,CAAC,CAAC;IAClE,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,KAAK,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC;IAEjF,qCAAqC;IACrC,IAAI,kBAAkB,IAAI,CAAC,YAAY,EAAE;QACxC,YAAY,GAAG,IAAI,2BAAY,CAAC,OAAO,CAAC,CAAC;QACzC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACzC,eAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;KACxC;SAAM,IAAI,CAAC,kBAAkB,IAAI,YAAY,EAAE;QAC/C,YAAY,CAAC,OAAO,EAAE,CAAC;QACvB,eAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;KACzC;IAED,+DAA+D;IAC/D,IAAI,iBAAiB,IAAI,aAAa,IAAI,CAAC,WAAW,EAAE;QACvD,WAAW,GAAG,IAAI,yBAAW,CAAC,OAAO,CAAC,CAAC;QACvC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACxC,eAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;KACnC;SAAM,IAAI,CAAC,CAAC,iBAAiB,IAAI,CAAC,aAAa,CAAC,IAAI,WAAW,EAAE;QACjE,WAAW,CAAC,OAAO,EAAE,CAAC;QACtB,eAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;KACpC;AACF,CAAC"}