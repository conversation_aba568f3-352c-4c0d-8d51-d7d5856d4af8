[["vs/base/browser/ui/actionbar/actionViewItems", ["titleLabel"]], ["vs/base/browser/ui/button/button", ["button dropdown more actions"]], ["vs/base/browser/ui/dialog/dialog", ["ok", "dialogInfoMessage", "dialogErrorMessage", "dialogWarningMessage", "dialogPendingMessage", "dialogClose"]], ["vs/base/browser/ui/dropdown/dropdownActionViewItem", ["moreActions"]], ["vs/base/browser/ui/findinput/findInput", ["defaultLabel"]], ["vs/base/browser/ui/findinput/findInputToggles", ["caseDescription", "wordsDescription", "regexDescription"]], ["vs/base/browser/ui/findinput/replaceInput", ["defaultLabel", "label.preserveCaseToggle"]], ["vs/base/browser/ui/hover/hoverWidget", ["acessibleViewHint", "acessibleViewHintNoKbOpen"]], ["vs/base/browser/ui/icons/iconSelectBox", ["iconSelect.placeholder", "iconSelect.noResults"]], ["vs/base/browser/ui/inputbox/inputBox", ["alertErrorMessage", "alertWarningMessage", "alertInfoMessage", "history.inputbox.hint.suffix.noparens", "history.inputbox.hint.suffix.inparens", "clearedInput"]], ["vs/base/browser/ui/keybindingLabel/keybindingLabel", ["unbound"]], ["vs/base/browser/ui/menu/menubar", ["mAppMenu", "mMore"]], ["vs/base/browser/ui/selectBox/selectBoxCustom", ["selectBox"]], ["vs/base/browser/ui/splitview/paneview", ["viewSection"]], ["vs/base/browser/ui/toolbar/toolbar", ["moreActions"]], ["vs/base/browser/ui/tree/abstractTree", ["type to search", "close", "type to search", "not found", "replFindNoResults", "foundResults", "type to filter", "type to search", "filter", "fuzzySearch"]], ["vs/base/browser/ui/tree/asyncDataTree", ["type to filter", "type to search"]], ["vs/base/browser/ui/tree/treeDefaults", ["collapse all"]], ["vs/base/common/actions", ["submenu.empty"]], ["vs/base/common/date", ["date.fromNow.in", "date.fromNow.now", "date.fromNow.seconds.singular.ago.fullWord", "date.fromNow.seconds.singular.ago", "date.fromNow.seconds.plural.ago.fullWord", "date.fromNow.seconds.plural.ago", "date.fromNow.seconds.singular.fullWord", "date.fromNow.seconds.singular", "date.fromNow.seconds.plural.fullWord", "date.fromNow.seconds.plural", "date.fromNow.minutes.singular.ago.fullWord", "date.fromNow.minutes.singular.ago", "date.fromNow.minutes.plural.ago.fullWord", "date.fromNow.minutes.plural.ago", "date.fromNow.minutes.singular.fullWord", "date.fromNow.minutes.singular", "date.fromNow.minutes.plural.fullWord", "date.fromNow.minutes.plural", "date.fromNow.hours.singular.ago.fullWord", "date.fromNow.hours.singular.ago", "date.fromNow.hours.plural.ago.fullWord", "date.fromNow.hours.plural.ago", "date.fromNow.hours.singular.fullWord", "date.fromNow.hours.singular", "date.fromNow.hours.plural.fullWord", "date.fromNow.hours.plural", "date.fromNow.days.singular.ago", "date.fromNow.days.plural.ago", "date.fromNow.days.singular", "date.fromNow.days.plural", "date.fromNow.weeks.singular.ago.fullWord", "date.fromNow.weeks.singular.ago", "date.fromNow.weeks.plural.ago.fullWord", "date.fromNow.weeks.plural.ago", "date.fromNow.weeks.singular.fullWord", "date.fromNow.weeks.singular", "date.fromNow.weeks.plural.fullWord", "date.fromNow.weeks.plural", "date.fromNow.months.singular.ago.fullWord", "date.fromNow.months.singular.ago", "date.fromNow.months.plural.ago.fullWord", "date.fromNow.months.plural.ago", "date.fromNow.months.singular.fullWord", "date.fromNow.months.singular", "date.fromNow.months.plural.fullWord", "date.fromNow.months.plural", "date.fromNow.years.singular.ago.fullWord", "date.fromNow.years.singular.ago", "date.fromNow.years.plural.ago.fullWord", "date.fromNow.years.plural.ago", "date.fromNow.years.singular.fullWord", "date.fromNow.years.singular", "date.fromNow.years.plural.fullWord", "date.fromNow.years.plural", "today", "yesterday", "duration.ms.full", "duration.ms", "duration.s.full", "duration.s", "duration.m.full", "duration.m", "duration.h.full", "duration.h", "duration.d"]], ["vs/base/common/errorMessage", ["stackTrace.format", "nodeExceptionMessage", "error.defaultMessage", "error.defaultMessage", "error.moreErrors", "error.defaultMessage"]], ["vs/base/common/jsonErrorMessages", ["error.invalidSymbol", "error.invalidNumberFormat", "error.propertyNameExpected", "error.valueExpected", "error.colonExpected", "error.commaExpected", "error.closeBraceExpected", "error.closeBracketExpected", "error.endOfFileExpected"]], ["vs/base/common/keybindingLabels", ["ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "windowsKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "superKey", "ctrlKey.long", "shiftKey.long", "optKey.long", "cmdKey.long", "ctrlKey.long", "shiftKey.long", "altKey.long", "windowsKey.long", "ctrlKey.long", "shiftKey.long", "altKey.long", "superKey.long"]], ["vs/base/node/zip", ["invalid file", "incompleteExtract", "notFound"]], ["vs/code/electron-main/app", ["confirmOpenMessageWorkspace", "confirmOpenMessageFolder", "confirmOpenMessageFileOrFolder", "open", "cancel", "confirmOpenDetail", "doNotAskAgainLocal", "doNotAskAgainRemote"]], ["vs/code/electron-main/main", ["mainLog", "secondInstanceAdmin", "secondInstanceAdminDetail", "secondInstanceNoResponse", "secondInstanceNoResponseDetail", "statusWarning", "startupDataDirError", "startupUserDataAndExtensionsDirErrorDetail", "close"]], ["vs/code/electron-sandbox/processExplorer/processExplorerMain", ["name", "cpu", "pid", "memory", "killProcess", "forceKillProcess", "copy", "copyAll", "debug"]], ["vs/code/electron-utility/sharedProcess/sharedProcessMain", ["sharedLog", "sharedLog", "networkk"]], ["vs/code/node/cliProcessMain", ["cli"]], ["vs/editor/browser/controller/editContext/native/screenReaderSupport", ["editor"]], ["vs/editor/browser/controller/editContext/screenReaderUtils", ["accessibilityModeOff", "accessibilityOffAriaLabel", "accessibilityOffAriaLabelNoKb", "accessibilityOffAriaLabelNoKbs"]], ["vs/editor/browser/controller/editContext/textArea/textAreaEditContext", ["editor"]], ["vs/editor/browser/coreCommands", ["stickydesc", "stickydesc", "removedCursor"]], ["vs/editor/browser/editorExtensions", ["miUndo", "undo", "undo", "miRedo", "redo", "redo", "miSelectAll", "selectAll", "selectAll"]], ["vs/editor/browser/gpu/viewGpuContext", ["editor.dom.render"]], ["vs/editor/browser/services/hoverService/hoverWidget", ["hoverhint"]], ["vs/editor/browser/services/hoverService/updatableHoverWidget", ["iconLabel.loading"]], ["vs/editor/browser/widget/codeEditor/codeEditorWidget", ["cursors.maximum", "goToSetting"]], ["vs/editor/browser/widget/diffEditor/commands", ["toggleCollapseUnchangedRegions", "toggleShowMovedCodeBlocks", "toggleUseInlineViewWhenSpaceIsLimited", "diffEditor", "switchSide", "exitCompareMove", "collapseAllUnchangedRegions", "showAllUnchangedRegions", "revert", "accessibleDiffViewer", "editor.action.accessibleDiffViewer.next", "editor.action.accessibleDiffViewer.prev"]], ["vs/editor/browser/widget/diffEditor/components/accessibleDiffViewer", ["accessibleDiffViewerInsertIcon", "accessibleDiffViewerRemoveIcon", "accessibleDiffViewerCloseIcon", "label.close", "aria<PERSON><PERSON><PERSON>", "no_lines_changed", "one_line_changed", "more_lines_changed", "header", "blankLine", "unchangedLine", "equalLine", "insertLine", "deleteLine"]], ["vs/editor/browser/widget/diffEditor/components/diffEditorEditors", ["diff-aria-navigation-tip"]], ["vs/editor/browser/widget/diffEditor/components/diffEditorViewZones/inlineDiffDeletedCodeMargin", ["diff.clipboard.copyDeletedLinesContent.label", "diff.clipboard.copyDeletedLinesContent.single.label", "diff.clipboard.copyChangedLinesContent.label", "diff.clipboard.copyChangedLinesContent.single.label", "diff.clipboard.copyDeletedLineContent.label", "diff.clipboard.copyChangedLineContent.label", "diff.inline.revertChange.label"]], ["vs/editor/browser/widget/diffEditor/diffEditor.contribution", ["useInlineViewWhenSpaceIsLimited", "showMoves", "revertHunk", "revertSelection", "Open Accessible Diff Viewer"]], ["vs/editor/browser/widget/diffEditor/features/hideUnchangedRegionsFeature", ["foldUnchanged", "hiddenLines", "diff.hiddenLines.top", "showUnchangedRegion", "diff.bottom", "hiddenLines", "diff.hiddenLines.expandAll"]], ["vs/editor/browser/widget/diffEditor/features/movedBlocksLinesFeature", ["codeMovedToWithChanges", "codeMovedFromWithChanges", "codeMovedTo", "codeMovedFrom"]], ["vs/editor/browser/widget/diffEditor/features/revertButtonsFeature", ["revertSelectedChanges", "revertChange"]], ["vs/editor/browser/widget/diffEditor/registrations.contribution", ["diffEditor.move.border", "diffEditor.moveActive.border", "diffEditor.unchangedRegionShadow", "diffInsertIcon", "diffRemoveIcon"]], ["vs/editor/browser/widget/multiDiffEditor/colors", ["multiDiffEditor.headerBackground", "multiDiffEditor.background", "multiDiffEditor.border"]], ["vs/editor/browser/widget/multiDiffEditor/multiDiffEditorWidgetImpl", ["loading", "noChangedFiles"]], ["vs/editor/common/config/editorConfigurationSchema", ["editorConfigurationTitle", "tabSize", "indentSize", "insertSpaces", "detectIndentation", "trimAutoWhitespace", "largeFileOptimizations", "wordBasedSuggestions.off", "wordBasedSuggestions.currentDocument", "wordBasedSuggestions.matchingDocuments", "wordBasedSuggestions.allDocuments", "wordBasedSuggestions", "semanticHighlighting.true", "semanticHighlighting.false", "semanticHighlighting.configuredByTheme", "semanticHighlighting.enabled", "stablePeek", "maxTokenizationLineLength", "editor.experimental.asyncTokenization", "editor.experimental.asyncTokenizationLogging", "editor.experimental.asyncTokenizationVerification", "editor.experimental.treeSitterTelemetry", "editor.experimental.preferTreeSitter.css", "editor.experimental.preferTreeSitter.typescript", "editor.experimental.preferTreeSitter.ini", "editor.experimental.preferTreeSitter.regex", "schema.brackets", "schema.openBracket", "schema.closeBracket", "schema.colorizedBracketPairs", "schema.openBracket", "schema.closeBracket", "maxComputationTime", "maxFileSize", "sideBySide", "renderSideBySideInlineBreakpoint", "useInlineViewWhenSpaceIsLimited", "renderMarginRevertIcon", "renderGutterMenu", "ignoreTrimWhitespace", "renderIndicators", "codeLens", "wordWrap.off", "wordWrap.on", "wordWrap.inherit", "diffAlgorithm.legacy", "diffAlgorithm.advanced", "hideUnchangedRegions.enabled", "hideUnchangedRegions.revealLineCount", "hideUnchangedRegions.minimumLineCount", "hideUnchangedRegions.contextLineCount", "showMoves", "showEmptyDecorations", "useTrueInlineView"]], ["vs/editor/common/config/editorOptions", ["accessibilitySupport.auto", "accessibilitySupport.on", "accessibilitySupport.off", "accessibilitySupport", "comments.insertSpace", "comments.ignoreEmptyLines", "emptySelectionClipboard", "find.cursorMoveOnType", "editor.find.seedSearchStringFromSelection.never", "editor.find.seedSearchStringFromSelection.always", "editor.find.seedSearchStringFromSelection.selection", "find.seedSearchStringFromSelection", "editor.find.autoFindInSelection.never", "editor.find.autoFindInSelection.always", "editor.find.autoFindInSelection.multiline", "find.autoFindInSelection", "find.globalFindClipboard", "find.addExtraSpaceOnTop", "find.loop", "editor.find.history.never", "editor.find.history.workspace", "find.history", "editor.find.replaceHistory.never", "editor.find.replaceHistory.workspace", "find.replaceHistory", "fontLigatures", "fontFeatureSettings", "fontLigaturesGeneral", "fontVariations", "fontVariationSettings", "fontVariationsGeneral", "fontSize", "fontWeightErrorMessage", "fontWeight", "editor.gotoLocation.multiple.peek", "editor.gotoLocation.multiple.gotoAndPeek", "editor.gotoLocation.multiple.goto", "editor.gotoLocation.multiple.deprecated", "editor.editor.gotoLocation.multipleDefinitions", "editor.editor.gotoLocation.multipleTypeDefinitions", "editor.editor.gotoLocation.multipleDeclarations", "editor.editor.gotoLocation.multipleImplemenattions", "editor.editor.gotoLocation.multipleReferences", "alternativeDefinitionCommand", "alternativeTypeDefinitionCommand", "alternativeDeclarationCommand", "alternativeImplementationCommand", "alternativeReferenceCommand", "hover.enabled", "hover.delay", "hover.sticky", "hover.<PERSON><PERSON><PERSON><PERSON>", "hover.above", "wrappingStrategy.simple", "wrappingStrategy.advanced", "wrappingStrategy", "editor.lightbulb.enabled.off", "editor.lightbulb.enabled.onCode", "editor.lightbulb.enabled.on", "enabled", "editor.stickyScroll.enabled", "editor.stickyScroll.maxLineCount", "editor.stickyScroll.defaultModel", "editor.stickyScroll.scrollWithEditor", "inlayHints.enable", "editor.inlayHints.on", "editor.inlayHints.onUnlessPressed", "editor.inlayHints.offUnlessPressed", "editor.inlayHints.off", "inlayHints.fontSize", "inlayHints.fontFamily", "inlayHints.padding", "inlayHints.maximumLength", "lineHeight", "minimap.enabled", "minimap.autohide", "minimap.size.proportional", "minimap.size.fill", "minimap.size.fit", "minimap.size", "minimap.side", "minimap.showSlider", "minimap.scale", "minimap.renderCharacters", "minimap.maxColumn", "minimap.showRegionSectionHeaders", "minimap.showMarkSectionHeaders", "minimap.markSectionHeaderRegex", "minimap.sectionHeaderFontSize", "minimap.sectionHeaderLetterSpacing", "padding.top", "padding.bottom", "parameterHints.enabled", "parameterHints.cycle", "on", "inline", "off", "quickSuggestions.strings", "quickSuggestions.comments", "quickSuggestions.other", "quickSuggestions", "lineNumbers.off", "lineNumbers.on", "lineNumbers.relative", "lineNumbers.interval", "lineNumbers", "rulers.size", "rulers.color", "rulers", "scrollbar.vertical.auto", "scrollbar.vertical.visible", "scrollbar.vertical.fit", "scrollbar.vertical", "scrollbar.horizontal.auto", "scrollbar.horizontal.visible", "scrollbar.horizontal.fit", "scrollbar.horizontal", "scrollbar.verticalScrollbarSize", "scrollbar.horizontalScrollbarSize", "scrollbar.scrollByPage", "scrollbar.ignoreHorizontalScrollbarInContentHeight", "unicodeHighlight.nonBasicASCII", "unicodeHighlight.invisibleCharacters", "unicodeHighlight.ambiguousCharacters", "unicodeHighlight.includeComments", "unicodeHighlight.includeStrings", "unicodeHighlight.allowedCharacters", "unicodeHighlight.allowedLocales", "inlineSuggest.enabled", "inlineSuggest.showToolbar.always", "inlineSuggest.showToolbar.onHover", "inlineSuggest.showToolbar.never", "inlineSuggest.showToolbar", "inlineSuggest.syntaxHighlightingEnabled", "inlineSuggest.suppressSuggestions", "inlineSuggest.fontFamily", "inlineSuggest.edits.allowCodeShifting", "inlineSuggest.edits.renderSideBySide", "editor.inlineSuggest.edits.renderSideBySide.auto", "editor.inlineSuggest.edits.renderSideBySide.never", "inlineSuggest.edits.showCollapsed", "bracketPairColorization.enabled", "bracketPairColorization.independentColorPoolPerBracketType", "editor.guides.bracketPairs.true", "editor.guides.bracketPairs.active", "editor.guides.bracketPairs.false", "editor.guides.bracketPairs", "editor.guides.bracketPairsHorizontal.true", "editor.guides.bracketPairsHorizontal.active", "editor.guides.bracketPairsHorizontal.false", "editor.guides.bracketPairsHorizontal", "editor.guides.highlightActiveBracketPair", "editor.guides.indentation", "editor.guides.highlightActiveIndentation.true", "editor.guides.highlightActiveIndentation.always", "editor.guides.highlightActiveIndentation.false", "editor.guides.highlightActiveIndentation", "suggest.insertMode.insert", "suggest.insertMode.replace", "suggest.insertMode", "suggest.filterGraceful", "suggest.localityBonus", "suggest.shareSuggestSelections", "suggest.insertMode.always", "suggest.insertMode.never", "suggest.insertMode.whenTriggerCharacter", "suggest.insertMode.whenQuickSuggestion", "suggest.selectionMode", "suggest.snippetsPreventQuickSuggestions", "suggest.showIcons", "suggest.showStatusBar", "suggest.preview", "suggest.showInlineDetails", "suggest.maxVisibleSuggestions.dep", "deprecated", "editor.suggest.showMethods", "editor.suggest.showFunctions", "editor.suggest.showConstructors", "editor.suggest.showDeprecated", "editor.suggest.matchOnWordStartOnly", "editor.suggest.showFields", "editor.suggest.showVariables", "editor.suggest.showClasss", "editor.suggest.showStructs", "editor.suggest.showInterfaces", "editor.suggest.showModules", "editor.suggest.showPropertys", "editor.suggest.showEvents", "editor.suggest.showOperators", "editor.suggest.showUnits", "editor.suggest.show<PERSON><PERSON><PERSON>", "editor.suggest.showConstants", "editor.suggest.showEnums", "editor.suggest.showEnumMembers", "editor.suggest.showKeywords", "editor.suggest.showTexts", "editor.suggest.showColors", "editor.suggest.showFiles", "editor.suggest.showReferences", "editor.suggest.showCustomcolors", "editor.suggest.showFolders", "editor.suggest.showTypeParameters", "editor.suggest.showSnippets", "editor.suggest.showUsers", "editor.suggest.showIssues", "selectLeadingAndTrailingWhitespace", "selectSubwords", "wordSegmenterLocales", "wordSegmenterLocales", "wrappingIndent.none", "wrappingIndent.same", "wrappingIndent.indent", "wrappingIndent.deepIndent", "wrappingIndent", "dropIntoEditor.enabled", "dropIntoEditor.showDropSelector", "dropIntoEditor.showDropSelector.afterDrop", "dropIntoEditor.showDropSelector.never", "pasteAs.enabled", "pasteAs.showPasteSelector", "pasteAs.showPasteSelector.afterPaste", "pasteAs.showPasteSelector.never", "acceptSuggestionOnCommitCharacter", "acceptSuggestionOnEnterSmart", "acceptSuggestionOnEnter", "accessibilityPageSize", "editor<PERSON>iewAccessibleLabel", "screenReaderAnnounceInlineSuggestion", "editor.autoClosingBrackets.languageDefined", "editor.autoClosingBrackets.beforeWhitespace", "autoClosingBrackets", "editor.autoClosingComments.languageDefined", "editor.autoClosingComments.beforeWhitespace", "autoClosingComments", "editor.autoClosingDelete.auto", "autoClosingDelete", "editor.autoClosingOvertype.auto", "autoClosingOvertype", "editor.autoClosingQuotes.languageDefined", "editor.autoClosingQuotes.beforeWhitespace", "autoClosingQuotes", "editor.autoIndent.none", "editor.autoIndent.keep", "editor.autoIndent.brackets", "editor.autoIndent.advanced", "editor.autoIndent.full", "autoIndent", "editor.autoSurround.languageDefined", "editor.autoSurround.quotes", "editor.autoSurround.brackets", "autoSurround", "stickyTabStops", "codeLens", "codeLensFontFamily", "codeLensFontSize", "colorDecorators", "editor.colorDecoratorActivatedOn.clickAndHover", "editor.colorDecoratorActivatedOn.hover", "editor.colorDecoratorActivatedOn.click", "colorDecoratorActivatedOn", "colorDecoratorsLimit", "columnSelection", "copyWithSyntaxHighlighting", "cursorBlinking", "cursorSmoothCaretAnimation.off", "cursorSmoothCaretAnimation.explicit", "cursorSmoothCaretAnimation.on", "cursorSmoothCaretAnimation", "cursorStyle", "overtypeCursorStyle", "cursorSurroundingLines", "cursorSurroundingLinesStyle.default", "cursorSurroundingLinesStyle.all", "cursorSurroundingLinesStyle", "cursor<PERSON><PERSON><PERSON>", "dragAndDrop", "experimentalEditContextEnabled", "experimentalGpuAcceleration.off", "experimentalGpuAcceleration.on", "experimentalGpuAcceleration", "experimentalWhitespaceRendering.svg", "experimentalWhitespaceRendering.font", "experimentalWhitespaceRendering.off", "experimentalWhitespaceRendering", "fastScrollSensitivity", "folding", "foldingStrategy.auto", "foldingStrategy.indentation", "foldingStrategy", "foldingHighlight", "foldingImportsByDefault", "foldingMaximumRegions", "unfoldOnClickAfterEndOfLine", "fontFamily", "formatOnPaste", "formatOnType", "glyphMargin", "hideCursorInOverviewRuler", "letterSpacing", "linkedEditing", "links", "matchBrackets", "mouseWheelScrollSensitivity", "mouseWheelZoom.mac", "mouseWheelZoom", "multiCursorMergeOverlapping", "multiCursorModifier.ctrlCmd", "multiCursorModifier.alt", "multiCursorModifier", "multiCursorPaste.spread", "multiCursorPaste.full", "multiCursorPaste", "multiCursorLimit", "occurrencesHighlight.off", "occurrencesHighlight.singleFile", "occurrencesHighlight.multiFile", "occurrencesHighlight", "occurrencesHighlightDelay", "overtypeOnPaste", "overviewRulerBorder", "peekWidgetDefaultFocus.tree", "peekWidgetDefaultFocus.editor", "peekWidgetDefaultFocus", "definitionLinkOpensInPeek", "quickSuggestionsDelay", "renameOnType", "renameOnTypeDeprecate", "renderControlCharacters", "renderFinalNewline", "renderLineHighlight.all", "renderLineHighlight", "renderLineHighlightOnlyWhenFocus", "renderWhitespace.boundary", "renderWhitespace.selection", "renderWhitespace.trailing", "renderWhitespace", "roundedSelection", "scrollBeyondLastColumn", "scrollBeyondLastLine", "scrollPredominantAxis", "selectionClipboard", "selection<PERSON>ighlight", "showFoldingControls.always", "showFoldingControls.never", "showFoldingControls.mouseover", "showFoldingControls", "showUnused", "showDeprecated", "snippetSuggestions.top", "snippetSuggestions.bottom", "snippetSuggestions.inline", "snippetSuggestions.none", "snippetSuggestions", "smoothScrolling", "inlineCompletionsAccessibilityVerbose", "suggestFontSize", "suggestLineHeight", "suggestOnTriggerCharacters", "suggestSelection.first", "suggestSelection.recentlyUsed", "suggestSelection.recentlyUsedByPrefix", "suggestSelection", "tabCompletion.on", "tabCompletion.off", "tabCompletion.onlySnippets", "tabCompletion", "unusualLineTerminators.auto", "unusualLineTerminators.off", "unusualLineTerminators.prompt", "unusualLineTerminators", "useTabStops", "wordBreak.normal", "wordBreak.keepAll", "wordBreak", "wordSeparators", "wordWrap.off", "wordWrap.on", "wordWrap.wordWrapColumn", "wordWrap.bounded", "wordWrap", "wordWrapColumn", "editor.defaultColorDecorators.auto", "editor.defaultColorDecorators.always", "editor.defaultColorDecorators.never", "defaultColorDecorators", "tabFocusMode"]], ["vs/editor/common/core/editorColorRegistry", ["lineHighlight", "lineHighlightBorderBox", "rangeHighlight", "rangeHighlightBorder", "symbolHighlight", "symbolHighlightBorder", "caret", "editorCursorBackground", "editorMultiCursorPrimaryForeground", "editorMultiCursorPrimaryBackground", "editorMultiCursorSecondaryForeground", "editorMultiCursorSecondaryBackground", "editorWhitespaces", "editor<PERSON><PERSON><PERSON><PERSON><PERSON>", "editorIndentGuides", "deprecatedEditorIndentGuides", "editorActiveIndentGuide", "deprecatedEditorActiveIndentGuide", "editorIndentGuides1", "editorIndentGuides2", "editorIndentGuides3", "editorIndentGuides4", "editorIndentGuides5", "editorIndentGuides6", "editorActiveIndentGuide1", "editorActiveIndentGuide2", "editorActiveIndentGuide3", "editorActiveIndentGuide4", "editorActiveIndentGuide5", "editorActiveIndentGuide6", "editorActiveLineNumber", "deprecatedEditorActiveLineNumber", "editorActiveLineNumber", "editorDimmedLineNumber", "editor<PERSON><PERSON><PERSON>", "editorCodeLensForeground", "editorBracketMatchBackground", "editorBracketMatchBorder", "editorOverviewRulerBorder", "editorOverviewRulerBackground", "editor<PERSON><PERSON>", "unnecessaryCodeBorder", "unnecessaryCodeOpacity", "editorGhostTextBorder", "editorGhostTextForeground", "editorGhostTextBackground", "overviewRulerRangeHighlight", "overviewRuleError", "overviewRuleWarning", "overviewRuleInfo", "editorBracketHighlightForeground1", "editorBracketHighlightForeground2", "editorBracketHighlightForeground3", "editorBracketHighlightForeground4", "editorBracketHighlightForeground5", "editorBracketHighlightForeground6", "editorBracketHighlightUnexpectedBracketForeground", "editorBracketPairGuide.background1", "editorBracketPairGuide.background2", "editorBracketPairGuide.background3", "editorBracketPairGuide.background4", "editorBracketPairGuide.background5", "editorBracketPairGuide.background6", "editorBracketPairGuide.activeBackground1", "editorBracketPairGuide.activeBackground2", "editorBracketPairGuide.activeBackground3", "editorBracketPairGuide.activeBackground4", "editorBracketPairGuide.activeBackground5", "editorBracketPairGuide.activeBackground6", "editorUnicodeHighlight.border", "editorUnicodeHighlight.background"]], ["vs/editor/common/editorContext<PERSON>eys", ["editorTextFocus", "editor<PERSON><PERSON><PERSON>", "textInputFocus", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inDiffEditor", "isEmbeddedDiffEditor", "multiDiffEditorAllCollapsed", "diffEditorHasChanges", "comparingMovedCode", "accessibleDiffViewerVisible", "diffEditorRenderSideBySideInlineBreakpointReached", "diffEditorInlineMode", "diffEditorOriginalWritable", "diffEditorModifiedWritable", "diffEditorOriginalUri", "diffEditorModifiedUri", "editorColumnSelection", "editorHasSelection", "editorHasMultipleSelections", "editorTabMovesFocus", "editorHoverVisible", "editorHoverFocused", "stickyScrollFocused", "stickyScrollVisible", "standaloneColorPickerVisible", "standaloneColorPickerFocused", "inCompositeEditor", "editorLangId", "editorHasCompletionItemProvider", "editorHasCodeActionsProvider", "editorHasCodeLensProvider", "editorHasDefinitionProvider", "editorHasDeclarationProvider", "editorHasImplementationProvider", "editorHasTypeDefinitionProvider", "editor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "editorHasDocumentHighlightProvider", "editorHasDocumentSymbolProvider", "editorHasReferenceProvider", "editor<PERSON><PERSON><PERSON><PERSON><PERSON>", "editorHasSignatureHelpProvider", "editorHasInlayHintsProvider", "editorHasDocumentFormattingProvider", "editorHasDocumentSelectionFormattingProvider", "editorHasMultipleDocumentFormattingProvider", "editorHasMultipleDocumentSelectionFormattingProvider"]], ["vs/editor/common/languages", ["suggestWidget.kind.method", "suggestWidget.kind.function", "suggestWidget.kind.constructor", "suggestWidget.kind.field", "suggestWidget.kind.variable", "suggestWidget.kind.class", "suggestWidget.kind.struct", "suggestWidget.kind.interface", "suggestWidget.kind.module", "suggestWidget.kind.property", "suggestWidget.kind.event", "suggestWidget.kind.operator", "suggestWidget.kind.unit", "suggestWidget.kind.value", "suggestWidget.kind.constant", "suggestWidget.kind.enum", "suggestWidget.kind.enumMember", "suggestWidget.kind.keyword", "suggestWidget.kind.text", "suggestWidget.kind.color", "suggestWidget.kind.file", "suggestWidget.kind.reference", "suggestWidget.kind.customcolor", "suggestWidget.kind.folder", "suggestWidget.kind.typeParameter", "suggestWidget.kind.user", "suggestWidget.kind.issue", "suggestWidget.kind.snippet", "Array", "Boolean", "Class", "Constant", "<PERSON><PERSON><PERSON><PERSON>", "Enum", "EnumMember", "Event", "Field", "File", "Function", "Interface", "Key", "Method", "<PERSON><PERSON><PERSON>", "Namespace", "<PERSON><PERSON>", "Number", "Object", "Operator", "Package", "Property", "String", "Struct", "TypeParameter", "Variable", "symbolAriaLabel"]], ["vs/editor/common/languages/modesRegistry", ["plainText.alias"]], ["vs/editor/common/model/editStack", ["edit"]], ["vs/editor/common/standaloneStrings", ["accessibilityHelpTitle", "openingDocs", "readonlyDiffE<PERSON>or", "editableDiffEditor", "readonly<PERSON><PERSON><PERSON>", "editableEditor", "defaultWindowTitleIncludesEditorState", "defaultWindowTitleExcludingEditorState", "toolbar", "changeConfigToOnMac", "changeConfigToOnWinLinux", "auto_on", "auto_off", "screenReaderModeEnabled", "screenReaderModeDisabled", "tabFocusModeOnMsg", "tabFocusModeOffMsg", "stickScrollKb", "suggestActionsKb", "acceptSuggestAction", "toggleSuggestionFocus", "codeFolding", "intellisense", "showOrFocusHover", "goToSymbol", "showAccessibilityHelpAction", "listSignalSoundsCommand", "listAnnouncementsCommand", "quickChatCommand", "startInlineChatCommand", "debug.startDebugging", "debugConsole.setBreakpoint", "debugConsole.addToWatch", "debugConsole.executeSelection", "chatEditorModification", "chatEditorRequestInProgress", "chatEditing.navigation", "inspectTokens", "gotoLineActionLabel", "helpQuickAccess", "quickCommandActionLabel", "quickCommandActionHelp", "quickOutlineActionLabel", "quickOutlineByCategoryActionLabel", "editor<PERSON>iewAccessibleLabel", "toggleHighContrast", "bulkEditServiceSummary"]], ["vs/editor/common/viewLayout/viewLineRenderer", ["showMore", "overflow.chars"]], ["vs/editor/contrib/anchorSelect/browser/anchorSelect", ["selectionAnchor", "anchorSet", "setSelectionAnchor", "goToSelectionAnchor", "selectFromAnchorToCursor", "cancelSelectionAnchor"]], ["vs/editor/contrib/bracketMatching/browser/bracketMatching", ["overviewRulerBracketMatchForeground", "miGoToBracket", "smartSelect.jumpBracket", "smartSelect.selectToBracket", "smartSelect.selectToBracketDescription", "smartSelect.removeBrackets"]], ["vs/editor/contrib/caretOperations/browser/caretOperations", ["caret.moveLeft", "caret.moveRight"]], ["vs/editor/contrib/caretOperations/browser/transpose", ["transposeLetters.label"]], ["vs/editor/contrib/clipboard/browser/clipboard", ["miCut", "actions.clipboard.cutLabel", "actions.clipboard.cutLabel", "actions.clipboard.cutLabel", "miCopy", "actions.clipboard.copyLabel", "actions.clipboard.copyLabel", "actions.clipboard.copyLabel", "miPaste", "actions.clipboard.pasteLabel", "actions.clipboard.pasteLabel", "actions.clipboard.pasteLabel", "copy as", "copy as", "share", "share", "actions.clipboard.copyWithSyntaxHighlightingLabel"]], ["vs/editor/contrib/codeAction/browser/codeAction", ["applyCodeActionFailed"]], ["vs/editor/contrib/codeAction/browser/codeActionCommands", ["args.schema.kind", "args.schema.apply", "args.schema.apply.first", "args.schema.apply.ifSingle", "args.schema.apply.never", "args.schema.preferred", "editor.action.quickFix.noneMessage", "editor.action.codeAction.noneMessage.preferred.kind", "editor.action.codeAction.noneMessage.kind", "editor.action.codeAction.noneMessage.preferred", "editor.action.codeAction.noneMessage", "editor.action.refactor.noneMessage.preferred.kind", "editor.action.refactor.noneMessage.kind", "editor.action.refactor.noneMessage.preferred", "editor.action.refactor.noneMessage", "editor.action.source.noneMessage.preferred.kind", "editor.action.source.noneMessage.kind", "editor.action.source.noneMessage.preferred", "editor.action.source.noneMessage", "editor.action.organize.noneMessage", "fixAll.noneMessage", "editor.action.autoFix.noneMessage", "quickfix.trigger.label", "refactor.label", "source.label", "organizeImports.label", "organizeImports.description", "fixAll.label", "autoFix.label"]], ["vs/editor/contrib/codeAction/browser/codeActionContributions", ["showCodeActionHeaders", "includeNearbyQuickFixes", "triggerOnFocusChange"]], ["vs/editor/contrib/codeAction/browser/codeActionController", ["editingNewSelection", "hideMoreActions", "showMoreActions"]], ["vs/editor/contrib/codeAction/browser/codeActionMenu", ["codeAction.widget.id.more", "codeAction.widget.id.quickfix", "codeAction.widget.id.extract", "codeAction.widget.id.inline", "codeAction.widget.id.convert", "codeAction.widget.id.move", "codeAction.widget.id.surround", "codeAction.widget.id.source"]], ["vs/editor/contrib/codeAction/browser/lightBulbWidget", ["gutterLightbulbWidget", "gutterLightbulbAutoFixWidget", "gutterLightbulbAIFixWidget", "gutterLightbulbAIFixAutoFixWidget", "gutterLightbulbSparkleFilledWidget", "codeActionAutoRun", "preferredcodeActionWithKb", "codeActionWithKb", "codeAction"]], ["vs/editor/contrib/codelens/browser/codelensController", ["placeHolder", "showLensOnLine"]], ["vs/editor/contrib/colorPicker/browser/colorPickerParts/colorPickerCloseButton", ["closeIcon"]], ["vs/editor/contrib/colorPicker/browser/colorPickerParts/colorPickerHeader", ["clickToToggleColorOptions"]], ["vs/editor/contrib/colorPicker/browser/hoverColorPicker/hoverColorPickerParticipant", ["hoverAccessibilityColorParticipant"]], ["vs/editor/contrib/colorPicker/browser/standaloneColorPicker/standaloneColorPickerActions", ["mishowOrFocusStandaloneColorPicker", "showOrFocusStandaloneColorPicker", "showOrFocusStandaloneColorPickerDescription", "hideColorPicker", "hideColorPickerDescription", "insertColorWithStandaloneColorPicker", "insertColorWithStandaloneColorPickerDescription"]], ["vs/editor/contrib/comment/browser/comment", ["miToggleLineComment", "miToggleBlockComment", "comment.line", "comment.line.add", "comment.line.remove", "comment.block"]], ["vs/editor/contrib/contextmenu/browser/contextmenu", ["context.minimap.minimap", "context.minimap.renderCharacters", "context.minimap.size", "context.minimap.size.proportional", "context.minimap.size.fill", "context.minimap.size.fit", "context.minimap.slider", "context.minimap.slider.mouseover", "context.minimap.slider.always", "action.showContextMenu.label"]], ["vs/editor/contrib/cursorUndo/browser/cursorUndo", ["cursor.undo", "cursor.redo"]], ["vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution", ["pasteAs.kind", "pasteAs.preferences", "pasteAs", "pasteAsText"]], ["vs/editor/contrib/dropOrPasteInto/browser/copyPasteController", ["pasteWidgetVisible", "postPasteWidgetTitle", "noPreferences", "pasteAsError", "resolveProcess", "pasteIntoEditorProgress", "pasteAsDefault", "pasteAsPickerPlaceholder", "pasteAsProgress"]], ["vs/editor/contrib/dropOrPasteInto/browser/defaultProviders", ["text.label", "defaultDropProvider.uriList.uris", "defaultDropProvider.uriList.uri", "defaultDropProvider.uriList.paths", "defaultDropProvider.uriList.path", "defaultDropProvider.uriList.relativePaths", "defaultDropProvider.uriList.relativePath", "pasteHtmlLabel"]], ["vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController", ["dropWidgetVisible", "postDropWidgetTitle", "dropIntoEditorProgress"]], ["vs/editor/contrib/dropOrPasteInto/browser/postEditWidget", ["resolveError", "applyError"]], ["vs/editor/contrib/editorState/browser/keybindingCancellation", ["cancellableOperation"]], ["vs/editor/contrib/find/browser/findController", ["too.large.for.replaceall", "<PERSON><PERSON><PERSON>", "findMatchAction.noResults", "findMatchAction.inputPlaceHolder", "findMatchAction.inputValidationMessage", "findMatchAction.inputValidationMessage", "miReplace", "startFindAction", "startFindWithArgsAction", "startFindWithSelectionAction", "findNextMatchAction", "findPreviousMatchAction", "findMatchAction.goToMatch", "nextSelectionMatchFindAction", "previousSelectionMatchFindAction", "startReplace"]], ["vs/editor/contrib/find/browser/findWidget", ["findCollapsedIcon", "findExpandedIcon", "findSelectionIcon", "findReplaceIcon", "findReplaceAllIcon", "findPreviousMatchIcon", "findNextMatchIcon", "label.findDialog", "label.find", "placeholder.find", "label.previousMatchButton", "label.nextMatchButton", "label.toggleSelectionFind", "label.closeButton", "label.replace", "placeholder.replace", "label.replaceButton", "label.replaceAllButton", "label.toggleReplaceButton", "title.matchesCountLimit", "label.matchesLocation", "label.noResults", "ariaSearchNoResultEmpty", "ariaSearchNoResult", "ariaSearchNoResultWithLineNum", "ariaSearchNoResultWithLineNumNoCurrentMatch"]], ["vs/editor/contrib/folding/browser/folding", ["unfoldAction.label", "unFoldRecursivelyAction.label", "foldAction.label", "toggleFoldAction.label", "foldRecursivelyAction.label", "toggleFoldRecursivelyAction.label", "foldAllBlockComments.label", "foldAllMarkerRegions.label", "unfoldAllMarkerRegions.label", "foldAllExcept.label", "unfoldAllExcept.label", "foldAllAction.label", "unfoldAllAction.label", "gotoParentFold.label", "gotoPreviousFold.label", "gotoNextFold.label", "createManualFoldRange.label", "removeManualFoldingRanges.label", "toggleImportFold.label", "foldLevelAction.label"]], ["vs/editor/contrib/folding/browser/foldingDecorations", ["foldBackgroundBackground", "collapsedTextColor", "editorGutter.foldingControlForeground", "foldingExpandedIcon", "foldingCollapsedIcon", "foldingManualCollapedIcon", "foldingManualExpandedIcon", "linesCollapsed", "linesExpanded"]], ["vs/editor/contrib/fontZoom/browser/fontZoom", ["EditorFontZoomIn.label", "EditorFontZoomOut.label", "EditorFontZoomReset.label"]], ["vs/editor/contrib/format/browser/formatActions", ["formatDocument.label", "formatSelection.label"]], ["vs/editor/contrib/gotoError/browser/gotoError", ["nextMarkerIcon", "previousMarkerIcon", "miGotoNextProblem", "miGotoPreviousProblem", "markerAction.next.label", "markerAction.previous.label", "markerAction.nextInFiles.label", "markerAction.previousInFiles.label"]], ["vs/editor/contrib/gotoError/browser/gotoErrorWidget", ["Error", "Warning", "Info", "Hint", "marker aria", "problems", "change", "editorMarkerNavigationError", "editorMarkerNavigationErrorHeaderBackground", "editor<PERSON><PERSON><PERSON><PERSON><PERSON>gation<PERSON><PERSON>ning", "editorMarkerNavigationWarningBackground", "editorMarkerNavigationInfo", "editorMarkerNavigationInfoHeaderBackground", "editorMarkerNavigationBackground"]], ["vs/editor/contrib/gotoSymbol/browser/goToCommands", ["peek.submenu", "def.title", "noResultWord", "generic.noResults", "miGotoDefinition", "decl.title", "decl.noResultWord", "decl.generic.noResults", "miGotoDeclaration", "decl.noResultWord", "decl.generic.noResults", "typedef.title", "goToTypeDefinition.noResultWord", "goToTypeDefinition.generic.noResults", "miGotoTypeDefinition", "impl.title", "goToImplementation.noResultWord", "goToImplementation.generic.noResults", "miGotoImplementation", "references.no", "references.noGeneric", "miGotoReference", "ref.title", "ref.title", "generic.title", "generic.noResult", "ref.title", "actions.goToDecl.label", "actions.goToDeclToSide.label", "actions.previewDecl.label", "actions.goToDeclaration.label", "actions.peekDecl.label", "actions.goToTypeDefinition.label", "actions.peekTypeDefinition.label", "actions.goToImplementation.label", "actions.peekImplementation.label", "goToReferences.label", "references.action.label", "label.generic"]], ["vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition", ["multipleResults"]], ["vs/editor/contrib/gotoSymbol/browser/peek/referencesController", ["referenceSearchVisible", "labelLoading", "metaTitle.N"]], ["vs/editor/contrib/gotoSymbol/browser/peek/referencesTree", ["referencesCount", "referenceCount", "treeAriaLabel"]], ["vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget", ["missingPreviewMessage", "noResults", "peekView.alternateTitle"]], ["vs/editor/contrib/gotoSymbol/browser/referencesModel", ["aria.oneReference", "aria.oneReference.preview", "aria.fileReferences.1", "aria.fileReferences.N", "aria.result.0", "aria.result.1", "aria.result.n1", "aria.result.nm"]], ["vs/editor/contrib/gotoSymbol/browser/symbolNavigation", ["hasSymbols", "location.kb", "location"]], ["vs/editor/contrib/gpu/browser/gpuActions", ["logTextureAtlasStats.label", "saveTextureAtlas.label", "drawGlyph.label", "gpuDebug.label"]], ["vs/editor/contrib/hover/browser/contentHoverRendered", ["hoverAccessibilityStatusBar", "hoverAccessibilityStatusBarActionWithKeybinding", "hoverAccessibilityStatusBarActionWithoutKeybinding"]], ["vs/editor/contrib/hover/browser/hoverAccessibleViews", ["increaseVerbosity", "decreaseVerbosity"]], ["vs/editor/contrib/hover/browser/hoverActionIds", ["increaseHoverVerbosityLevel", "decreaseHoverVerbosityLevel"]], ["vs/editor/contrib/hover/browser/hoverActions", ["showOrFocusHover.focus.noAutoFocus", "showOrFocusHover.focus.focusIfVisible", "showOrFocusHover.focus.autoFocusImmediately", "showOrFocusHover", "showOrFocusHoverDescription", "showDefinitionPreviewHover", "showDefinitionPreviewHoverDescription", "hideHover", "scrollUpHover", "scrollUpHoverDescription", "scrollDownHover", "scrollDownHoverDescription", "scrollLeftHover", "scrollLeftHoverDescription", "scrollRightHover", "scrollRightHoverDescription", "pageUpHover", "pageUpHoverDescription", "pageDownHover", "pageDownHoverDescription", "goToTopHover", "goToTopHoverDescription", "goToBottomHover", "goToBottomHoverDescription"]], ["vs/editor/contrib/hover/browser/markdownHoverParticipant", ["increaseHoverVerbosity", "decreaseHoverVerbosity", "modesContentHover.loading", "stopped rendering", "too many characters", "increaseVerbosityWithKb", "increaseVerbosity", "decreaseVerbosityWithKb", "decreaseVerbosity"]], ["vs/editor/contrib/hover/browser/markerHoverParticipant", ["view problem", "noQuickFixes", "checkingForQuickFixes", "noQuickFixes", "quick fixes"]], ["vs/editor/contrib/indentation/browser/indentation", ["configuredTabSize", "defaultTabSize", "currentTabSize", "selectTabWidth", "indentationToSpaces", "indentationToSpacesDescription", "indentationToTabs", "indentationToTabsDescription", "indentUsingTabs", "indentUsingTabsDescription", "indentUsingSpaces", "indentUsingSpacesDescription", "changeTabDisplaySize", "changeTabDisplaySizeDescription", "detectIndentation", "detectIndentationDescription", "editor.reindent<PERSON>", "editor.reindentlinesDescription", "editor.reindentselectedlines", "editor.reindentselectedlinesDescription"]], ["vs/editor/contrib/inlayHints/browser/inlayHintsHover", ["hint.dbl", "links.navigate.kb.meta.mac", "links.navigate.kb.meta", "links.navigate.kb.alt.mac", "links.navigate.kb.alt", "hint.defAndCommand", "hint.def", "hint.cmd"]], ["vs/editor/contrib/inlineCompletions/browser/controller/commands", ["noInlineEditAvailable", "acceptWord", "acceptLine", "accept", "accept", "jump", "reject", "action.inlineSuggest.alwaysShowToolbar", "action.inlineSuggest.dev.extractRepro", "action.inlineSuggest.showNext", "action.inlineSuggest.showPrevious", "action.inlineSuggest.trigger", "action.inlineSuggest.trigger.explicitInlineEdit", "action.inlineSuggest.acceptNextWord", "action.inlineSuggest.acceptNextLine", "action.inlineSuggest.accept", "action.inlineSuggest.jump", "action.inlineSuggest.hide", "action.inlineSuggest.toggleShowCollapsed"]], ["vs/editor/contrib/inlineCompletions/browser/controller/inlineCompletionContextKeys", ["inlineSuggestionVisible", "inlineSuggestionHasIndentation", "inlineSuggestionHasIndentationLessThanTabSize", "suppressSuggestions", "cursorInIndentation", "editor.hasSelection", "cursorAtInlineEdit", "inlineEditVisible", "tabShouldJumpToInlineEdit", "tabShouldAcceptInlineEdit", "inInlineEditsPreviewEditor"]], ["vs/editor/contrib/inlineCompletions/browser/controller/inlineCompletionsController", ["showAccessibleViewHint"]], ["vs/editor/contrib/inlineCompletions/browser/hintsWidget/hoverParticipant", ["hoverAccessibilityStatusBar", "inlineSuggestionFollows"]], ["vs/editor/contrib/inlineCompletions/browser/hintsWidget/inlineCompletionsHintsWidget", ["parameterHintsNextIcon", "parameterHintsPreviousIcon", "content", "previous", "next"]], ["vs/editor/contrib/inlineCompletions/browser/view/inlineEdits/components/gutterIndicatorMenu", ["goto", "accept", "reject", "showExpanded", "showCollapsed", "settings"]], ["vs/editor/contrib/inlineCompletions/browser/view/inlineEdits/components/indicatorView", ["inlineEdit.indicator.foreground", "inlineEdit.indicator.background", "inlineEdit.indicator.border"]], ["vs/editor/contrib/inlineCompletions/browser/view/inlineEdits/inlineEditsModel", ["inlineEdit"]], ["vs/editor/contrib/inlineCompletions/browser/view/inlineEdits/theme", ["inlineEdit.originalBackground", "inlineEdit.modifiedBackground", "inlineEdit.originalChangedLineBackground", "inlineEdit.originalChangedTextBackground", "inlineEdit.modifiedChangedLineBackground", "inlineEdit.modifiedChangedTextBackground", "inlineEdit.gutterIndicator.primaryForeground", "inlineEdit.gutterIndicator.primaryBorder", "inlineEdit.gutterIndicator.primaryBackground", "inlineEdit.gutterIndicator.secondaryForeground", "inlineEdit.gutterIndicator.secondaryBorder", "inlineEdit.gutterIndicator.secondaryBackground", "inlineEdit.gutterIndicator.successfulForeground", "inlineEdit.gutterIndicator.successfulBorder", "inlineEdit.gutterIndicator.successfulBackground", "inlineEdit.gutterIndicator.background", "inlineEdit.originalBorder", "inlineEdit.modifiedBorder", "inlineEdit.tabWillAcceptModifiedBorder", "inlineEdit.tabWillAcceptOriginalBorder"]], ["vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace", ["InPlaceReplaceAction.previous.label", "InPlaceReplaceAction.next.label"]], ["vs/editor/contrib/insertFinalNewLine/browser/insertFinalNewLine", ["insertFinalNewLine"]], ["vs/editor/contrib/lineSelection/browser/lineSelection", ["expandLineSelection"]], ["vs/editor/contrib/linesOperations/browser/linesOperations", ["miCopyLinesUp", "miCopyLinesDown", "miDuplicateSelection", "miMoveLinesUp", "miMoveLinesDown", "lines.copyUp", "lines.copyDown", "duplicateSelection", "lines.moveUp", "lines.moveDown", "lines.sortAscending", "lines.sortDescending", "lines.deleteDuplicates", "lines.trimTrailingWhitespace", "lines.delete", "lines.indent", "lines.outdent", "lines.insertBefore", "lines.insertAfter", "lines.deleteAllLeft", "lines.deleteAllRight", "lines.joinLines", "editor.transpose", "editor.transformToUppercase", "editor.transformToLowercase", "editor.transformToTitlecase", "editor.transformToSnakecase", "editor.transformToCamelcase", "editor.transformToPascalcase", "editor.transformToKebabcase"]], ["vs/editor/contrib/linkedEditing/browser/linkedEditing", ["editorLinkedEditingBackground", "linkedEditing.label"]], ["vs/editor/contrib/links/browser/links", ["invalid.url", "missing.url", "links.navigate.executeCmd", "links.navigate.follow", "links.navigate.kb.meta.mac", "links.navigate.kb.meta", "links.navigate.kb.alt.mac", "links.navigate.kb.alt", "tooltip.explanation", "label"]], ["vs/editor/contrib/message/browser/messageController", ["messageVisible"]], ["vs/editor/contrib/multicursor/browser/multicursor", ["cursorAdded", "cursorsAdded", "miInsertCursorAbove", "miInsertCursorBelow", "miInsertCursorAtEndOfEachLineSelected", "miAddSelectionToNextFindMatch", "miAddSelectionToPreviousFindMatch", "miSelectHighlights", "mutlicursor.focusNextCursor.description", "mutlicursor.focusPreviousCursor.description", "mutlicursor.insertAbove", "mutlicursor.insertBelow", "mutlicursor.insertAtEndOfEachLineSelected", "mutlicursor.addCursorsToBottom", "mutlicursor.addCursorsToTop", "addSelectionToNextFindMatch", "addSelectionToPreviousFindMatch", "moveSelectionToNextFindMatch", "moveSelectionToPreviousFindMatch", "selectAllOccurrencesOfFindMatch", "changeAll.label", "mutlicursor.focusNextCursor", "mutlicursor.focusPreviousCursor"]], ["vs/editor/contrib/parameterHints/browser/parameterHints", ["parameterHints.trigger.label"]], ["vs/editor/contrib/parameterHints/browser/parameterHintsWidget", ["parameterHintsNextIcon", "parameterHintsPreviousIcon", "hint", "editorHoverWidgetHighlightForeground"]], ["vs/editor/contrib/peekView/browser/peekView", ["inReferenceSearchEditor", "label.close", "peekViewTitleBackground", "peekViewTitleForeground", "peekViewTitleInfoForeground", "peekViewBorder", "peekViewResultsBackground", "peekViewResultsMatchForeground", "peekViewResultsFileForeground", "peekViewResultsSelectionBackground", "peekViewResultsSelectionForeground", "peekViewEditorBackground", "peekViewEditorGutterBackground", "peekViewEditorStickScrollBackground", "peekViewResultsMatchHighlight", "peekViewEditorMatchHighlight", "peekViewEditorMatchHighlightBorder"]], ["vs/editor/contrib/placeholderText/browser/placeholderText.contribution", ["placeholderForeground"]], ["vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess", ["cannotRunGotoLine", "gotoLineColumnLabel", "gotoLineLabel", "gotoLineLabelEmptyWithLimit", "gotoLineLabelEmpty"]], ["vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess", ["cannotRunGotoSymbolWithoutEditor", "cannotRunGotoSymbolWithoutSymbolProvider", "noMatchingSymbolResults", "noSymbolResults", "openToSide", "openToBottom", "symbols", "property", "method", "function", "_constructor", "variable", "class", "struct", "event", "operator", "interface", "namespace", "package", "typeParameter", "modules", "property", "enum", "enumMember", "string", "file", "array", "number", "boolean", "object", "key", "field", "constant"]], ["vs/editor/contrib/readOnlyMessage/browser/contribution", ["editor.simple.readonly", "editor.readonly"]], ["vs/editor/contrib/rename/browser/rename", ["no result", "resolveRenameLocationFailed", "label", "quotable<PERSON><PERSON>l", "aria", "rename.failedApply", "rename.failed", "enablePreview", "rename.label", "focusNextRenameSuggestion", "focusPreviousRenameSuggestion"]], ["vs/editor/contrib/rename/browser/renameWidget", ["renameInputVisible", "renameInputFocused", "label", "renameSuggestionsReceivedAria", "renameAriaLabel", "generateRenameSuggestionsButton", "cancelRenameSuggestionsButton"]], ["vs/editor/contrib/smartSelect/browser/smartSelect", ["miSmartSelectGrow", "miSmartSelectShrink", "smartSelect.expand", "smartSelect.shrink"]], ["vs/editor/contrib/snippet/browser/snippetController2", ["inSnippetMode", "hasNextTabstop", "hasPrevTabstop", "next"]], ["vs/editor/contrib/snippet/browser/snippetVariables", ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "SundayShort", "MondayShort", "TuesdayShort", "WednesdayShort", "ThursdayShort", "FridayShort", "SaturdayShort", "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December", "JanuaryShort", "FebruaryShort", "MarchShort", "AprilShort", "MayS<PERSON><PERSON>", "JuneShort", "JulyShort", "AugustShort", "SeptemberShort", "OctoberShort", "NovemberShort", "DecemberShort"]], ["vs/editor/contrib/stickyScroll/browser/stickyScrollActions", ["mitoggleStickyScroll", "stickyScroll", "miStickyScroll", "mifocusEditorStickyScroll", "toggleEditorStickyScroll", "toggleEditorStickyScroll.description", "focusStickyScroll", "selectNextStickyScrollLine.title", "selectPreviousStickyScrollLine.title", "goToFocusedStickyScrollLine.title", "selectEditor.title"]], ["vs/editor/contrib/suggest/browser/suggest", ["suggestWidgetHasSelection", "suggestWidgetDetailsVisible", "suggestWidgetMultipleSuggestions", "suggestionMakesTextEdit", "acceptSuggestionOnEnter", "suggestionHasInsertAndReplaceRange", "suggestionInsertMode", "suggestionCanResolve"]], ["vs/editor/contrib/suggest/browser/suggestController", ["aria.alert.snippet", "accept.insert", "accept.insert", "accept.replace", "accept.replace", "accept.insert", "detail.more", "detail.less", "suggest.trigger.label", "suggest.reset.label"]], ["vs/editor/contrib/suggest/browser/suggestWidget", ["editorSuggestWidgetBackground", "editorSuggestWidgetBorder", "editorSuggestWidgetForeground", "editorSuggestWidgetSelectedForeground", "editorSuggestWidgetSelectedIconForeground", "editorSuggestWidgetSelectedBackground", "editorSuggestWidgetHighlightForeground", "editorSuggestWidgetFocusHighlightForeground", "editorSuggestWidgetStatusForeground", "suggestWidget.loading", "suggestWidget.noSuggestions", "suggest", "label.full", "label.detail", "label.desc", "label", "ariaCurrenttSuggestionReadDetails"]], ["vs/editor/contrib/suggest/browser/suggestWidgetDetails", ["details.close", "loading"]], ["vs/editor/contrib/suggest/browser/suggestWidgetRenderer", ["suggestMoreInfoIcon", "readMore"]], ["vs/editor/contrib/suggest/browser/wordContextKey", ["desc"]], ["vs/editor/contrib/symbolIcons/browser/symbolIcons", ["symbolIcon.arrayForeground", "symbolIcon.booleanForeground", "symbolIcon.classForeground", "symbolIcon.colorForeground", "symbolIcon.constantForeground", "symbolIcon.constructorForeground", "symbolIcon.enumeratorForeground", "symbolIcon.enumeratorMemberForeground", "symbolIcon.eventForeground", "symbolIcon.fieldForeground", "symbolIcon.fileForeground", "symbolIcon.folderForeground", "symbolIcon.functionForeground", "symbolIcon.interfaceForeground", "symbolIcon.keyForeground", "symbolIcon.keywordForeground", "symbolIcon.methodForeground", "symbolIcon.moduleForeground", "symbolIcon.namespaceForeground", "symbolIcon.nullForeground", "symbolIcon.numberForeground", "symbolIcon.objectForeground", "symbolIcon.operatorForeground", "symbolIcon.packageForeground", "symbolIcon.propertyForeground", "symbolIcon.referenceForeground", "symbolIcon.snippetForeground", "symbolIcon.stringForeground", "symbolIcon.structForeground", "symbolIcon.textForeground", "symbolIcon.typeParameterForeground", "symbolIcon.unitForeground", "symbolIcon.variableForeground"]], ["vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode", ["toggle.tabMovesFocus.on", "toggle.tabMovesFocus.off", "toggle.tabMovesFocus", "tabMovesFocusDescriptions"]], ["vs/editor/contrib/tokenization/browser/tokenization", ["forceRetokenize"]], ["vs/editor/contrib/unicodeHighlighter/browser/bannerController", ["closeBanner"]], ["vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter", ["warningIcon", "unicodeHighlighting.thisDocumentHasManyNonBasicAsciiUnicodeCharacters", "unicodeHighlighting.thisDocumentHasManyAmbiguousUnicodeCharacters", "unicodeHighlighting.thisDocumentHasManyInvisibleUnicodeCharacters", "unicodeHighlight.configureUnicodeHighlightOptions", "unicodeHighlight.characterIsAmbiguousASCII", "unicodeHighlight.characterIsAmbiguous", "unicodeHighlight.characterIsInvisible", "unicodeHighlight.characterIsNonBasicAscii", "unicodeHighlight.adjustSettings", "unicodeHighlight.disableHighlightingInComments.shortLabel", "unicodeHighlight.disableHighlightingInStrings.shortLabel", "unicodeHighlight.disableHighlightingOfAmbiguousCharacters.shortLabel", "unicodeHighlight.disableHighlightingOfInvisibleCharacters.shortLabel", "unicodeHighlight.disableHighlightingOfNonBasicAsciiCharacters.shortLabel", "unicodeHighlight.excludeInvisibleCharFromBeingHighlighted", "unicodeHighlight.excludeCharFromBeingHighlighted", "unicodeHighlight.allowCommonCharactersInLanguage", "action.unicodeHighlight.disableHighlightingInComments", "action.unicodeHighlight.disableHighlightingInStrings", "action.unicodeHighlight.disableHighlightingOfAmbiguousCharacters", "action.unicodeHighlight.disableHighlightingOfInvisibleCharacters", "action.unicodeHighlight.disableHighlightingOfNonBasicAsciiCharacters", "action.unicodeHighlight.showExcludeOptions"]], ["vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators", ["unusualLineTerminators.title", "unusualLineTerminators.message", "unusualLineTerminators.detail", "unusualLineTerminators.fix", "unusualLineTerminators.ignore"]], ["vs/editor/contrib/wordHighlighter/browser/highlightDecorations", ["wordHighlight", "wordHighlightStrong", "wordHighlightText", "wordHighlightBorder", "wordHighlightStrongBorder", "wordHighlightTextBorder", "overviewRulerWordHighlightForeground", "overviewRulerWordHighlightStrongForeground", "overviewRulerWordHighlightTextForeground"]], ["vs/editor/contrib/wordHighlighter/browser/wordHighlighter", ["wordHighlight.next.label", "wordHighlight.previous.label", "wordHighlight.trigger.label"]], ["vs/editor/contrib/wordOperations/browser/wordOperations", ["deleteInsideWord"]], ["vs/platform/accessibilitySignal/browser/accessibilitySignalService", ["accessibilitySignals.positionHasError.name", "accessibility.signals.positionHasError", "accessibilitySignals.positionHasWarning.name", "accessibility.signals.positionHasWarning", "accessibilitySignals.lineHasError.name", "accessibility.signals.lineHasError", "accessibilitySignals.lineHasWarning.name", "accessibility.signals.lineHasWarning", "accessibilitySignals.lineHasFoldedArea.name", "accessibility.signals.lineHasFoldedArea", "accessibilitySignals.lineHasBreakpoint.name", "accessibility.signals.lineHasBreakpoint", "accessibilitySignals.lineHasInlineSuggestion.name", "accessibilitySignals.nextEditSuggestion.name", "accessibility.signals.nextEditSuggestion", "accessibilitySignals.terminalQuickFix.name", "accessibility.signals.terminalQuickFix", "accessibilitySignals.onDebugBreak.name", "accessibility.signals.onDebugBreak", "accessibilitySignals.noInlayHints", "accessibility.signals.noInlayHints", "accessibilitySignals.taskCompleted", "accessibility.signals.taskCompleted", "accessibilitySignals.taskFailed", "accessibility.signals.taskFailed", "accessibilitySignals.terminalCommandFailed", "accessibility.signals.terminalCommandFailed", "accessibilitySignals.terminalCommandSucceeded", "accessibility.signals.terminalCommandSucceeded", "accessibilitySignals.terminalBell", "accessibility.signals.terminalBell", "accessibilitySignals.notebookCellCompleted", "accessibility.signals.notebookCellCompleted", "accessibilitySignals.notebookCellFailed", "accessibility.signals.notebookCellFailed", "accessibilitySignals.diffLineInserted", "accessibilitySignals.diffLineDeleted", "accessibilitySignals.diffLineModified", "accessibilitySignals.chatEditModifiedFile", "accessibility.signals.chatEditModifiedFile", "accessibilitySignals.chatRequestSent", "accessibility.signals.chatRequestSent", "accessibilitySignals.chatResponseReceived", "accessibilitySignals.codeActionRequestTriggered", "accessibility.signals.codeActionRequestTriggered", "accessibilitySignals.codeActionApplied", "accessibilitySignals.progress", "accessibility.signals.progress", "accessibilitySignals.clear", "accessibility.signals.clear", "accessibilitySignals.save", "accessibility.signals.save", "accessibilitySignals.format", "accessibility.signals.format", "accessibilitySignals.voiceRecordingStarted", "accessibilitySignals.voiceRecordingStopped", "accessibilitySignals.editsKept", "accessibility.signals.editsKept", "accessibilitySignals.editsUndone", "accessibility.signals.editsUndone"]], ["vs/platform/action/common/actionCommonCategories", ["view", "help", "test", "file", "preferences", "developer"]], ["vs/platform/actions/browser/buttonbar", ["labelWithKeybinding", "moreActions", "moreActions"]], ["vs/platform/actions/browser/dropdownActionViewItemWithKeybinding", ["titleAndKb"]], ["vs/platform/actions/browser/menuEntryActionViewItem", ["titleAndKb", "titleAndKb", "titleAndKbAndAlt", "content2", "content"]], ["vs/platform/actions/browser/toolbar", ["hide", "resetThisMenu"]], ["vs/platform/actions/common/menuResetAction", ["title"]], ["vs/platform/actions/common/menuService", ["hide.label", "configure keybinding"]], ["vs/platform/actionWidget/browser/actionList", ["label-preview", "label", "customQuickFixWidget.labels", "customQuickFixWidget"]], ["vs/platform/actionWidget/browser/actionWidget", ["actionBar.toggledBackground", "codeActionMenuVisible", "hideCodeActionWidget.title", "selectPrevCodeAction.title", "selectNextCodeAction.title", "acceptSelected.title", "previewSelected.title"]], ["vs/platform/configuration/common/configurationRegistry", ["defaultLanguageConfigurationOverrides.title", "defaultLanguageConfiguration.description", "overrideSettings.defaultDescription", "overrideSettings.errorMessage", "overrideSettings.defaultDescription", "overrideSettings.errorMessage", "config.property.empty", "config.property.languageDefault", "config.property.duplicate", "config.policy.duplicate"]], ["vs/platform/contextkey/browser/contextKeyService", ["getContextKeyInfo"]], ["vs/platform/contextkey/common/contextkey", ["contextkey.parser.error.emptyString", "contextkey.parser.error.emptyString.hint", "contextkey.parser.error.noInAfterNot", "contextkey.parser.error.closingParenthesis", "contextkey.parser.error.unexpectedToken", "contextkey.parser.error.unexpectedToken.hint", "contextkey.parser.error.unexpectedEOF", "contextkey.parser.error.unexpectedEOF.hint", "contextkey.parser.error.expectedButGot", "contextkey.scanner.errorForLinterWithHint", "contextkey.scanner.errorForLinter"]], ["vs/platform/contextkey/common/contextkeys", ["isMac", "isLinux", "isWindows", "isWeb", "isMacNative", "isIOS", "isMobile", "productQualityType", "inputFocus"]], ["vs/platform/contextkey/common/scanner", ["contextkey.scanner.hint.didYouMean1", "contextkey.scanner.hint.didYouMean2", "contextkey.scanner.hint.didYouMean3", "contextkey.scanner.hint.didYouForgetToOpenOrCloseQuote", "contextkey.scanner.hint.didYouForgetToEscapeSlash"]], ["vs/platform/dialogs/common/dialogs", ["yesButton", "cancelButton", "cancelButton", "cancelButton", "okButton", "okButton", "cancelButton", "moreFile", "moreFiles"]], ["vs/platform/dialogs/electron-main/dialogMainService", ["open", "openFolder", "openFile", "openWorkspaceTitle", "openWorkspace"]], ["vs/platform/dnd/browser/dnd", ["fileTooLarge"]], ["vs/platform/environment/node/argv", ["optionsUpperCase", "extensionsManagement", "troubleshooting", "cliDataDir", "cliDataDir", "diff", "merge", "add", "remove", "goto", "newWindow", "reuseWindow", "wait", "locale", "userDataDir", "profileName", "help", "extensionHomePath", "listExtensions", "showVersions", "category", "installExtension", "install prerelease", "uninstallExtension", "updateExtensions", "experimentalApis", "addMcp", "version", "verbose", "log", "status", "prof-startup", "disableExtensions", "disableExtension", "turn sync", "inspect-extensions", "inspect-brk-extensions", "disableLCDText", "disableGPU", "disableChromiumSandbox", "locateShellIntegrationPath", "telemetry", "deprecated.useInstead", "paths", "usage", "options", "stdinWindows", "stdinUnix", "subcommands", "unknownVersion", "unknownCommit"]], ["vs/platform/environment/node/argvHelper", ["multipleValues", "emptyValue", "deprecatedArgument", "unknownSubCommandOption", "unknownOption", "gotoValidation"]], ["vs/platform/extensionManagement/common/abstractExtensionManagementService", ["not allowed to install", "incompatible platform", "learn why", "MarketPlaceDisabled", "malicious extension", "notFoundDeprecatedReplacementExtension", "incompatible platform", "incompatibleAPI", "notFoundReleaseExtension", "notFoundCompatibleDependency", "singleDependentError", "twoDependentsError", "multipleDependentsError", "singleIndirectDependentError", "twoIndirectDependentsError", "multipleIndirectDependentsError"]], ["vs/platform/extensionManagement/common/allowedExtensionsService", ["specific extension not allowed", "extension prerelease not allowed", "specific version of extension not allowed", "publisher not allowed", "prerelease versions from this publisher not allowed"]], ["vs/platform/extensionManagement/common/extensionManagement", ["extensionsConfigurationTitle", "extensions.allowed", "extensions.allowed.none", "extensions.allowed.all", "extensions.allowed.policy", "extensions.allow.description", "extensions.allowed.enable.desc", "extensions.allowed.disable.desc", "extensions.allowed.disable.stable.desc", "extensions.allow.version.description", "extension.publisher.allow.description", "extensions.publisher.allowed.enable.desc", "extensions.publisher.allowed.disable.desc", "extensions.publisher.allowed.disable.stable.desc", "extensions.allow.all.description", "extensions.allow.all.enable", "extensions.allow.all.disable", "extensions", "preferences"]], ["vs/platform/extensionManagement/common/extensionManagementCLI", ["notFound", "useId", "listFromLocation", "installingExtensionsOnLocation", "installingExtensions", "error while installing extensions", "installation failed", "updateExtensionsQuery", "updateExtensionsNoExtensions", "updateExtensionsNewVersionsAvailable", "errorUpdatingExtension", "successUpdate", "alreadyInstalled-checkAndUpdate", "alreadyInstalled", "alreadyInstalled", "updateMessage", "installing builtin with version", "installing builtin ", "installing with version", "installing", "errorInstallingExtension", "successInstall", "successVsixInstall", "cancelVsixInstall", "forceDowngrade", "builtin", "forceUninstall", "uninstalling", "successUninstallFromLocation", "successUninstall", "notInstalleddOnLocation", "notInstalled"]], ["vs/platform/extensionManagement/common/extensionNls", ["missingNLS<PERSON>ey"]], ["vs/platform/extensionManagement/common/extensionsScannerService", ["fileReadFail", "jsonParseFail", "jsonParseInvalidType", "jsonsParseReportErrors", "jsonInvalidFormat", "jsonsParseReportErrors", "jsonInvalidFormat"]], ["vs/platform/extensionManagement/node/extensionManagementService", ["incompatible", "notAllowed", "invalidManifest", "signature verification not executed", "signature verification failed", "signature verification failed", "errorDeleting", "cannot read", "restartCode", "restartCode"]], ["vs/platform/extensionManagement/node/extensionManagementUtil", ["invalidManifest"]], ["vs/platform/extensions/common/extensionValidator", ["extensionDescription.publisher", "extensionDescription.name", "extensionDescription.version", "extensionDescription.engines", "extensionDescription.engines.vscode", "extensionDescription.extensionDependencies", "extensionDescription.activationEvents1", "extensionDescription.activationEvents2", "extensionDescription.extensionKind", "extensionDescription.main1", "extensionDescription.main2", "extensionDescription.browser1", "extensionDescription.browser2", "notSemver", "apiProposalMismatch1", "apiProposalMismatch2", "versionSyntax", "versionSpecificity1", "versionSpecificity2", "versionMismatch"]], ["vs/platform/externalTerminal/node/externalTerminalService", ["console.title", "mac.terminal.script.failed", "mac.terminal.type.not.supported", "press.any.key", "linux.term.failed", "ext.term.app.not.found"]], ["vs/platform/files/browser/htmlFileSystemProvider", ["fileSystemRenameError", "fileSystemNotAllowedError"]], ["vs/platform/files/browser/indexedDBFileSystemProvider", ["fileNotExists", "fileIsDirectory", "fileNotDirectory", "dirIsNotEmpty", "fileExceedsStorageQuota", "internal"]], ["vs/platform/files/common/files", ["unknownError", "sizeB", "sizeKB", "sizeMB", "sizeGB", "sizeTB"]], ["vs/platform/files/common/fileService", ["invalidPath", "noProviderFound", "fileNotFoundError", "fileExists", "err.write", "writeFailedUnlockUnsupported", "writeFailedAtomicUnsupported1", "writeFailedAtomicUnsupported2", "writeFailedAtomicUnlock", "fileIsDirectoryWriteError", "fileModifiedError", "err.read", "fileIsDirectoryReadError", "fileNotModifiedError", "fileTooLargeError", "unableToMoveCopyError1", "unableToMoveCopyError2", "unableToMoveCopyError3", "unableToMoveCopyError4", "mkdirExistsError", "deleteFailedTrashUnsupported", "deleteFailedAtomicUnsupported", "deleteFailedTrashAndAtomicUnsupported", "deleteFailedNotFound", "deleteFailedNonEmptyFolder", "err.readonly", "err.readonly"]], ["vs/platform/files/common/io", ["fileTooLargeError"]], ["vs/platform/files/electron-main/diskFileSystemProviderServer", ["binFailed", "trashFailed"]], ["vs/platform/files/node/diskFileSystemProvider", ["fileExists", "fileNotExists", "moveError", "copyError", "fileCopyErrorPathCase", "fileMoveCopyErrorNotFound", "fileMoveCopyErrorExists"]], ["vs/platform/history/browser/contextScopedHistoryWidget", ["suggestWidgetVisible"]], ["vs/platform/keybinding/common/abstractKeybindingService", ["first.chord", "next.chord", "missing.chord", "missing.chord"]], ["vs/platform/keyboardLayout/common/keyboardConfig", ["keyboardConfigurationTitle", "dispatch", "mapAltGrToCtrlAlt"]], ["vs/platform/languagePacks/common/languagePacks", ["currentDisplayLanguage"]], ["vs/platform/languagePacks/common/localizedStrings", ["open", "close", "find"]], ["vs/platform/list/browser/listService", ["workbenchConfigurationTitle", "multiSelectModifier.ctrlCmd", "multiSelectModifier.alt", "multiSelectModifier", "openModeModifier", "horizontalScrolling setting", "list.scrollByPage", "tree indent setting", "render tree indent guides", "list smoothScrolling setting", "Mouse Wheel Scroll Sensitivity", "Fast Scroll Sensitivity", "defaultFindModeSettingKey.highlight", "defaultFindModeSettingKey.filter", "defaultFindModeSettingKey", "keyboardNavigationSettingKey.simple", "keyboardNavigationSettingKey.highlight", "keyboardNavigationSettingKey.filter", "keyboardNavigationSettingKey", "keyboardNavigationSettingKeyDeprecated", "defaultFindMatchTypeSettingKey.fuzzy", "defaultFindMatchTypeSettingKey.contiguous", "defaultFindMatchTypeSettingKey", "expand mode", "sticky scroll", "sticky scroll maximum items", "typeNavigationMode2"]], ["vs/platform/log/common/log", ["trace", "debug", "info", "warn", "error", "off"]], ["vs/platform/markers/common/markers", ["sev.error", "sev.warning", "sev.info", "sev.errors", "sev.warnings", "sev.infos"]], ["vs/platform/markers/common/markerService", ["filtered", "filtered.network"]], ["vs/platform/menubar/electron-main/menubar", ["miNewWindow", "mFile", "mEdit", "mSelection", "m<PERSON>iew", "mGoto", "mRun", "mTerminal", "m<PERSON><PERSON>ow", "mHelp", "mAbout", "miPreferences", "mServices", "mHide", "mHideOthers", "mShowAll", "miQuit", "quit", "cancel", "quitMessage", "mMinimize", "mZoom", "mBringToFront", "miSwitchWindow", "mNewTab", "mShowPreviousTab", "mShowNextTab", "mMoveTabToNewWindow", "mMergeAllWindows", "miCheckForUpdates", "miCheckingForUpdates", "miDownloadUpdate", "miDownloadingUpdate", "miInstallUpdate", "miInstallingUpdate", "miRestartToUpdate"]], ["vs/platform/native/electron-main/nativeHostMainService", ["warnEscalation", "ok", "cancel", "cantCreateBinFolder", "warnEscalationUninstall", "ok", "cancel", "cantUninstall", "sourceMissing"]], ["vs/platform/process/electron-main/processMainService", ["local", "processExplorer", "trace.message", "trace.detail", "trace.ok"]], ["vs/platform/quickinput/browser/commandsQuickAccess", ["recentlyUsed", "suggested", "commonlyUsed", "morecCommands", "suggested", "commandPickAriaLabelWithKeybinding", "canNotRun"]], ["vs/platform/quickinput/browser/helpQuickAccess", ["helpPickAriaLabel"]], ["vs/platform/quickinput/browser/quickInput", ["inQuickInput", "quickInputAlignment", "quickInputType", "cursorAtEndOfQuickInputBox", "quickInput.back", "inputModeEntry", "quickInput.steps", "quickInputBox.ariaLabel", "ok", "inputModeEntryDescription"]], ["vs/platform/quickinput/browser/quickInputActions", ["quickPick", "quickInput.nextSeparatorWithQuickAccessFallback", "quickInput.previousSeparatorWithQuickAccessFallback"]], ["vs/platform/quickinput/browser/quickInputController", ["quickInput.checkAll", "quickInput.visibleCount", "quickInput.countSelected", "ok", "custom", "quickInput.backWithKeybinding", "quickInput.back"]], ["vs/platform/quickinput/browser/quickInputTree", ["quickInput"]], ["vs/platform/quickinput/browser/quickInputUtils", ["executeCommand"]], ["vs/platform/quickinput/browser/quickPickPin", ["terminal.commands.pinned", "pinCommand", "<PERSON><PERSON><PERSON><PERSON>"]], ["vs/platform/remoteTunnel/common/remoteTunnel", ["remoteTunnelLog"]], ["vs/platform/remoteTunnel/node/remoteTunnelService", ["remoteTunnelService.building", "remoteTunnelService.authorizing", "remoteTunnelService.openTunnelWithName", "remoteTunnelService.openTunnel", "remoteTunnelService.serviceInstallFailed"]], ["vs/platform/request/common/request", ["httpConfigurationTitle", "useLocalProxy", "httpConfigurationTitle", "electronFetch", "httpConfigurationTitle", "proxy", "strictSSL", "proxyKerberosServicePrincipal", "noProxy", "proxyAuthorization", "proxySupportOff", "proxySupportOn", "proxySupportFallback", "proxySupportOverride", "proxySupport", "systemCertificates", "systemCertificatesV2", "fetchAdditionalSupport"]], ["vs/platform/shell/node/shellEnv", ["resolveShellEnvTimeout", "resolveShellEnvError", "resolveShellEnvExitError"]], ["vs/platform/telemetry/common/telemetryLogAppender", ["telemetryLog"]], ["vs/platform/telemetry/common/telemetryService", ["telemetry.telemetryLevelMd", "telemetry.docsStatement", "telemetry.docsAndPrivacyStatement", "telemetry.restart", "telemetry.crashReports", "telemetry.errors", "telemetry.usage", "telemetry.telemetryLevel.tableDescription", "telemetry.telemetryLevel.deprecated", "telemetryConfigurationTitle", "telemetry.telemetryLevel.default", "telemetry.telemetryLevel.error", "telemetry.telemetryLevel.crash", "telemetry.telemetryLevel.off", "telemetry.telemetryLevel.policyDescription", "telemetry.feedback.enabled", "telemetry.enableTelemetry", "telemetry.enableTelemetryMd", "enableTelemetryDeprecated"]], ["vs/platform/telemetry/common/telemetryUtils", ["telemetryLogName"]], ["vs/platform/terminal/common/terminalLogService", ["terminalLoggerName"]], ["vs/platform/terminal/common/terminalPlatformConfiguration", ["terminalProfile.args", "terminalProfile.overrideName", "terminalProfile.icon", "terminalProfile.color", "terminalProfile.env", "terminalProfile.path", "terminalAutomationProfile.path", "terminal.integrated.profile", "terminalIntegratedConfigurationTitle", "terminal.integrated.automationProfile.linux", "terminal.integrated.automationProfile.osx", "terminal.integrated.automationProfile.windows", "terminalProfile.windowsSource", "terminalProfile.windowsExtensionIdentifier", "terminalProfile.windowsExtensionId", "terminalProfile.windowsExtensionTitle", "terminalProfile.osxExtensionIdentifier", "terminalProfile.osxExtensionId", "terminalProfile.osxExtensionTitle", "terminalProfile.linuxExtensionIdentifier", "terminalProfile.linuxExtensionId", "terminalProfile.linuxExtensionTitle", "terminal.integrated.useWslProfiles", "terminal.integrated.inheritEnv", "terminal.integrated.persistentSessionScrollback", "terminal.integrated.showLinkHover", "terminal.integrated.confirmIgnoreProcesses", "terminalIntegratedConfigurationTitle", "terminal.integrated.defaultProfile.linux", "terminal.integrated.defaultProfile.osx", "terminal.integrated.defaultProfile.windows"]], ["vs/platform/terminal/common/terminalProfiles", ["terminalAutomaticProfile"]], ["vs/platform/terminal/node/ptyHostMain", ["ptyHost"]], ["vs/platform/terminal/node/ptyService", ["terminal-history-restored"]], ["vs/platform/terminal/node/terminalProcess", ["launchFail.cwdNotDirectory", "launchFail.cwdDoesNotExist", "launchFail.executableDoesNotExist", "launchFail.executableIsNotFileOrSymlink"]], ["vs/platform/theme/common/colors/baseColors", ["foreground", "disabledForeground", "errorForeground", "descriptionForeground", "iconForeground", "focusBorder", "contrastBorder", "activeContrastBorder", "selectionBackground", "textLinkForeground", "textLinkActiveForeground", "textSeparatorForeground", "textPreformatForeground", "textPreformatBackground", "textBlockQuoteBackground", "textBlockQuoteBorder", "textCodeBlockBackground"]], ["vs/platform/theme/common/colors/chartsColors", ["chartsForeground", "chartsLines", "chartsRed", "chartsBlue", "chartsYellow", "chartsOrange", "chartsGreen", "chartsPurple"]], ["vs/platform/theme/common/colors/editorColors", ["editorBack<PERSON>", "editor<PERSON><PERSON><PERSON>", "editorStickyScrollBackground", "editorStickyScrollHoverBackground", "editorStickyScrollBorder", "editorStickyScrollShadow", "editorWidgetBackground", "editorWidgetForeground", "editorW<PERSON>tBorder", "editorWidgetResizeBorder", "editorError.background", "editorError.foreground", "errorBorder", "editor<PERSON><PERSON>ning.background", "editorWarning.foreground", "warningBorder", "editorInfo.background", "editorInfo.foreground", "infoBorder", "editorHint.foreground", "hintBorder", "activeLinkForeground", "editorSelectionBackground", "editorSelectionForeground", "editorInactiveSelection", "editorSelectionHighlight", "editorSelectionHighlightBorder", "editorCompositionBorder", "editorFindMatch", "editorFindMatchForeground", "findMatchHighlight", "findMatchHighlightForeground", "findRangeHighlight", "editorFindMatchBorder", "findMatchHighlightBorder", "findRangeHighlightBorder", "hoverHighlight", "hoverBackground", "hoverForeground", "hoverBorder", "statusBarBackground", "editorInlayHintForeground", "editorInlayHintBackground", "editorInlayHintForegroundTypes", "editorInlayHintBackgroundTypes", "editorInlayHintForegroundParameter", "editorInlayHintBackgroundParameter", "editorLightBulbForeground", "editorLightBulbAutoFixForeground", "editorLightBulbAiForeground", "snippetTabstopHighlightBackground", "snippetTabstopHighlightBorder", "snippetFinalTabstopHighlightBackground", "snippetFinalTabstopHighlightBorder", "diffEditorInserted", "diffEditorRemoved", "diffEditorInsertedLines", "diffEditorRemovedLines", "diffEditorInsertedLineGutter", "diffEditorRemovedLineGutter", "diffEditorOverviewInserted", "diffEditorOverviewRemoved", "diffEditorInsertedOutline", "diffEditorRemovedOutline", "diffEditorBorder", "diffDiagonalFill", "diffEditor.unchangedRegionBackground", "diffEditor.unchangedRegionForeground", "diffEditor.unchangedCodeBackground", "widgetShadow", "widgetBorder", "toolbarHoverBackground", "toolbarHoverOutline", "toolbarActiveBackground", "breadcrumbsFocusForeground", "breadcrumbsBackground", "breadcrumbsFocusForeground", "breadcrumbsSelectedForeground", "breadcrumbsSelectedBackground", "mergeCurrentHeaderBackground", "mergeCurrentContentBackground", "mergeIncomingHeaderBackground", "mergeIncomingContentBackground", "mergeCommonHeaderBackground", "mergeCommonContentBackground", "mergeBorder", "overviewRulerCurrentContentForeground", "overviewRulerIncomingContentForeground", "overviewRulerCommonContentForeground", "overviewRulerFindMatchForeground", "overviewRulerSelectionHighlightForeground", "problemsErrorIconForeground", "problemsWarningIconForeground", "problemsInfoIconForeground"]], ["vs/platform/theme/common/colors/inputColors", ["inputBoxBackground", "inputBoxForeground", "inputBoxBorder", "inputBoxActiveOptionBorder", "inputOption.hoverBackground", "inputOption.activeBackground", "inputOption.activeForeground", "inputPlaceholderForeground", "inputValidationInfoBackground", "inputValidationInfoForeground", "inputValidationInfoBorder", "inputValidationWarningBackground", "inputValidationWarningForeground", "inputValidationWarningBorder", "inputValidationErrorBackground", "inputValidationErrorForeground", "inputValidationErrorBorder", "dropdownBackground", "dropdownListBackground", "dropdownForeground", "dropdownBorder", "buttonForeground", "buttonSeparator", "buttonBackground", "buttonHoverBackground", "buttonBorder", "buttonSecondaryForeground", "buttonSecondaryBackground", "buttonSecondaryHoverBackground", "radioActiveForeground", "radioBackground", "radioActiveBorder", "radioInactiveForeground", "radioInactiveBackground", "radioInactiveBorder", "radioHoverBackground", "checkbox.background", "checkbox.select.background", "checkbox.foreground", "checkbox.border", "checkbox.select.border", "checkbox.disabled.background", "checkbox.disabled.foreground", "keybindingLabelBackground", "keybindingLabelForeground", "keybindingLabelBorder", "keybindingLabelBottomBorder"]], ["vs/platform/theme/common/colors/listColors", ["listFocusBackground", "listFocusForeground", "listFocusOutline", "listFocusAndSelectionOutline", "listActiveSelectionBackground", "listActiveSelectionForeground", "listActiveSelectionIconForeground", "listInactiveSelectionBackground", "listInactiveSelectionForeground", "listInactiveSelectionIconForeground", "listInactiveFocusBackground", "listInactiveFocusOutline", "listHoverBackground", "listHoverForeground", "listDropBackground", "listDropBetweenBackground", "highlight", "listFocusHighlightForeground", "invalidItemForeground", "listErrorForeground", "listWarningForeground", "listFilterWidgetBackground", "listFilterWidgetOutline", "listFilterWidgetNoMatchesOutline", "listFilterWidgetShadow", "listFilterMatchHighlight", "listFilterMatchHighlightBorder", "listDeemphasizedForeground", "treeIndentGuidesStroke", "treeInactiveIndentGuidesStroke", "tableColumnsBorder", "tableOddRowsBackgroundColor", "editorActionListBackground", "editorActionListForeground", "editorActionListFocusForeground", "editorActionListFocusBackground"]], ["vs/platform/theme/common/colors/menuColors", ["menuBorder", "menuForeground", "menuBackground", "menuSelectionForeground", "menuSelectionBackground", "menuSelectionBorder", "menuSeparatorBackground"]], ["vs/platform/theme/common/colors/minimapColors", ["minimapFindMatchHighlight", "minimapSelectionOccurrenceHighlight", "minimapSelectionHighlight", "minimapInfo", "overviewRuleWarning", "minimapError", "minimapBackground", "minimapForegroundOpacity", "minimapSliderBackground", "minimapSliderHoverBackground", "minimapSliderActiveBackground"]], ["vs/platform/theme/common/colors/miscColors", ["sashActiveBorder", "badgeBackground", "badgeForeground", "activityWarningBadge.foreground", "activityWarningBadge.background", "activityErrorBadge.foreground", "activityErrorBadge.background", "scrollbarShadow", "scrollbarSliderBackground", "scrollbarSliderHoverBackground", "scrollbarSliderActiveBackground", "progressBarBackground", "chartLine", "chartAxis", "chartGuide"]], ["vs/platform/theme/common/colors/quickpickColors", ["pickerBackground", "pickerForeground", "pickerTitleBackground", "pickerGroupForeground", "pickerGroupBorder", "quickInput.list.focusBackground deprecation", "quickInput.listFocusForeground", "quickInput.listFocusIconForeground", "quickInput.listFocusBackground"]], ["vs/platform/theme/common/colors/searchColors", ["search.resultsInfoForeground", "searchEditor.queryMatch", "searchEditor.editorFindMatchBorder"]], ["vs/platform/theme/common/colorUtils", ["transparecyRequired", "useDefault"]], ["vs/platform/theme/common/iconRegistry", ["schema.fontId.formatError", "iconDefinition.fontId", "iconDefinition.fontCharacter", "widgetClose", "previousChangeIcon", "nextChangeIcon"]], ["vs/platform/theme/common/tokenClassificationRegistry", ["schema.token.settings", "schema.token.foreground", "schema.token.background.warning", "schema.token.fontStyle", "schema.fontStyle.error", "schema.token.fontStyle.none", "schema.token.bold", "schema.token.italic", "schema.token.underline", "schema.token.strikethrough", "comment", "string", "keyword", "number", "regexp", "operator", "namespace", "type", "struct", "class", "interface", "enum", "typeParameter", "function", "member", "method", "macro", "variable", "parameter", "property", "enumMember", "event", "decorator", "labels", "declaration", "documentation", "static", "abstract", "deprecated", "modification", "async", "readonly"]], ["vs/platform/undoRedo/common/undoRedoService", ["externalRemoval", "noParallelUniverses", "cannotWorkspaceUndo", "cannotWorkspaceUndo", "cannotWorkspaceUndoDueToChanges", "cannotWorkspaceUndoDueToInProgressUndoRedo", "cannotWorkspaceUndoDueToInMeantimeUndoRedo", "confirmWorkspace", "ok", "nok", "cannotResourceUndoDueToInProgressUndoRedo", "confirmDifferentSource", "confirmDifferentSource.yes", "confirmDifferentSource.no", "cannotWorkspaceRedo", "cannotWorkspaceRedo", "cannotWorkspaceRedoDueToChanges", "cannotWorkspaceRedoDueToInProgressUndoRedo", "cannotWorkspaceRedoDueToInMeantimeUndoRedo", "cannotResourceRedoDueToInProgressUndoRedo"]], ["vs/platform/update/common/update.config.contribution", ["updateConfigurationTitle", "updateMode", "none", "manual", "start", "default", "updateMode", "deprecated", "enableWindowsBackgroundUpdatesTitle", "enableWindowsBackgroundUpdates", "showReleaseNotes"]], ["vs/platform/userDataProfile/common/userDataProfile", ["defaultProfile"]], ["vs/platform/userDataSync/common/abstractSynchronizer", ["incompatible", "incompatible sync data"]], ["vs/platform/userDataSync/common/keybindingsSync", ["errorInvalidSettings", "errorInvalidSettings"]], ["vs/platform/userDataSync/common/settingsSync", ["errorInvalidSettings"]], ["vs/platform/userDataSync/common/userDataAutoSyncService", ["default service changed", "service changed", "turned off", "default service changed", "service changed", "session expired", "turned off machine"]], ["vs/platform/userDataSync/common/userDataSync", ["settings sync", "settingsSync.keybindingsPerPlatform", "settingsSync.ignoredExtensions", "app.extension.identifier.errorMessage", "settingsSync.ignoredSettings"]], ["vs/platform/userDataSync/common/userDataSyncLog", ["userDataSyncLog"]], ["vs/platform/userDataSync/common/userDataSyncMachines", ["error incompatible"]], ["vs/platform/userDataSync/common/userDataSyncResourceProvider", ["incompatible sync data"]], ["vs/platform/windows/electron-main/windowImpl", ["reopen", "close", "wait", "appStalled", "appStalledDetail", "doNotRestoreEditors", "appGone", "appGoneDetails", "reopen", "newWindow", "close", "appGoneDetailWorkspace", "appGoneDetailEmptyWindow", "doNotRestoreEditors", "hiddenMenuBar"]], ["vs/platform/windows/electron-main/windowsMainService", ["ok", "pathNotExistTitle", "uriInvalidTitle", "pathNotExistDetail", "uriInvalidDetail", "allow", "cancel", "learnMore", "confirmOpenMessage", "confirmOpenDetail", "doNotAskAgain"]], ["vs/platform/workspace/common/workspace", ["codeWorkspace"]], ["vs/platform/workspaces/electron-main/workspacesHistoryMainService", ["clearButtonLabel", "cancel", "confirmClearRecentsMessage", "confirmClearDetail", "newWindow", "newWindowDesc", "recentFoldersAndWorkspaces", "recentFolders", "untitledWorkspace", "workspaceName"]], ["vs/platform/workspaces/electron-main/workspacesManagementMainService", ["ok", "workspaceOpenedMessage", "workspaceOpenedDetail"]], ["vs/server/node/remoteExtensionHostAgentCli", ["remotecli"]], ["vs/server/node/serverEnvironmentService", ["host", "port", "socket-path", "server-base-path", "connection-token", "connection-token-file", "without-connection-token", "acceptLicenseTerms", "serverDataDir", "telemetry-level", "default-folder", "default-workspace", "start-server"]], ["vs/server/node/serverServices", ["remoteExtensionLog"]], ["vs/workbench/api/browser/mainThreadAuthentication", ["signedOut", "confirmModelAccess", "<PERSON><PERSON><PERSON><PERSON>", "confirmLogin", "allow", "learnMore", "incorrectAccount", "incorrectAccountDetail", "keep", "loginWith"]], ["vs/workbench/api/browser/mainThreadCLICommands", ["cannot be installed"]], ["vs/workbench/api/browser/mainThreadComments", ["commentsViewIcon"]], ["vs/workbench/api/browser/mainThreadCustomEditors", ["vetoExtHostRestart", "defaultEditLabel"]], ["vs/workbench/api/browser/mainThreadEditSessionIdentityParticipant", ["timeout.onWillCreateEditSessionIdentity"]], ["vs/workbench/api/browser/mainThreadExtensionService", ["reload window", "reload", "notSupportedInWorkspace", "restrictedMode", "manageWorkspaceTrust", "disabledDep", "enable dep", "disabledDepNoAction", "uninstalledDep", "install missing dep", "unknownDep"]], ["vs/workbench/api/browser/mainThreadFileSystemEventService", ["ask.1.create", "ask.1.copy", "ask.1.move", "ask.1.delete", "ask.N.create", "ask.N.copy", "ask.N.move", "ask.N.delete", "preview", "cancel", "ok", "preview", "cancel", "again", "msg-create", "msg-rename", "msg-copy", "msg-delete", "msg-write", "label"]], ["vs/workbench/api/browser/mainThreadLanguageModels", ["languageModelsAccountId"]], ["vs/workbench/api/browser/mainThreadMessageService", ["defaultSource", "manageExtension", "cancel", "ok"]], ["vs/workbench/api/browser/mainThreadNotebookSaveParticipant", ["timeout.onWillSave"]], ["vs/workbench/api/browser/mainThreadOutputService", ["status.showOutput", "status.showOutputAria", "status.showOutputTooltip"]], ["vs/workbench/api/browser/mainThreadProgress", ["manageExtension"]], ["vs/workbench/api/browser/mainThreadSaveParticipant", ["timeout.onWillSave"]], ["vs/workbench/api/browser/mainThreadTask", ["task.label"]], ["vs/workbench/api/browser/mainThreadTunnelService", ["remote.tunnel.openTunnel", "remote.tunnelsView.elevationButton"]], ["vs/workbench/api/browser/mainThreadUriOpeners", ["openerFailedUseDefault", "openerFailedMessage"]], ["vs/workbench/api/browser/mainThreadWebviews", ["errorMessage"]], ["vs/workbench/api/browser/mainThreadWorkspace", ["folderStatusMessageAddSingleFolder", "folderStatusMessageAddMultipleFolders", "folderStatusMessageRemoveSingleFolder", "folderStatusMessageRemoveMultipleFolders", "folderStatusChangeFolder"]], ["vs/workbench/api/browser/statusBarExtensionPoint", ["id", "name", "text", "tooltip", "command", "alignment", "priority", "accessibilityInformation", "accessibilityInformation.role", "accessibilityInformation.label", "vscode.extension.contributes.statusBarItems", "invalid"]], ["vs/workbench/api/browser/viewsExtensionPoint", ["vscode.extension.contributes.views.containers.id", "vscode.extension.contributes.views.containers.title", "vscode.extension.contributes.views.containers.icon", "vscode.extension.contributes.viewsContainers", "views.container.activitybar", "views.container.panel", "vscode.extension.contributes.view.type", "vscode.extension.contributes.view.tree", "vscode.extension.contributes.view.webview", "vscode.extension.contributes.view.id", "vscode.extension.contributes.view.name", "vscode.extension.contributes.view.when", "vscode.extension.contributes.view.icon", "vscode.extension.contributes.view.contextualTitle", "vscode.extension.contributes.view.initialState", "vscode.extension.contributes.view.initialState.visible", "vscode.extension.contributes.view.initialState.hidden", "vscode.extension.contributes.view.initialState.collapsed", "vscode.extension.contributs.view.size", "vscode.extension.contributes.view.accessibilityHelpContent", "vscode.extension.contributes.view.id", "vscode.extension.contributes.view.name", "vscode.extension.contributes.view.when", "vscode.extension.contributes.view.group", "vscode.extension.contributes.view.remoteName", "vscode.extension.contributes.views", "views.explorer", "views.debug", "views.scm", "views.test", "views.remote", "views.contributed", "viewcontainer requirearray", "requireidstring", "requireidstring", "requirestring", "requirestring", "require<PERSON>emptys<PERSON>", "ViewContainerRequiresProposedAPI", "ViewContainerDoesnotExist", "duplicateView1", "duplicateView2", "unknownViewType", "requirearray", "requirestring", "requirestring", "optstring", "optstring", "optstring", "optenum", "view container id", "view container title", "view container location", "view id", "view name title", "view container location", "viewsContainers", "views"]], ["vs/workbench/api/common/configurationExtensionPoint", ["vscode.extension.contributes.configuration.title", "vscode.extension.contributes.configuration.order", "vscode.extension.contributes.configuration.properties", "vscode.extension.contributes.configuration.property.empty", "vscode.extension.contributes.configuration.properties.schema", "scope.application.description", "scope.machine.description", "scope.window.description", "scope.resource.description", "scope.language-overridable.description", "scope.machine-overridable.description", "scope.description", "scope.enumDescriptions", "scope.markdownEnumDescriptions", "scope.enumItemLabels", "scope.markdownDescription", "scope.deprecationMessage", "scope.markdownDeprecationMessage", "scope.singlelineText.description", "scope.multilineText.description", "scope.editPresentation", "scope.order", "scope.ignoreSync", "scope.tags", "config.property.preventDefaultConfiguration.warning", "config.property.defaultConfiguration.warning", "vscode.extension.contributes.configuration", "invalid.title", "invalid.properties", "config.property.duplicate", "invalid.property", "invalid.allOf", "workspaceConfig.folders.description", "workspaceConfig.path.description", "workspaceConfig.name.description", "workspaceConfig.uri.description", "workspaceConfig.name.description", "workspaceConfig.settings.description", "workspaceConfig.launch.description", "workspaceConfig.tasks.description", "workspaceConfig.mcp.description", "workspaceConfig.extensions.description", "workspaceConfig.remoteAuthority", "workspaceConfig.transient", "unknownWorkspaceProperty", "setting name", "description", "default", "settings"]], ["vs/workbench/api/common/extHostDiagnostics", ["limitHit"]], ["vs/workbench/api/common/extHostExtensionService", ["extensionTestError1", "extensionTestError"]], ["vs/workbench/api/common/extHostLanguageFeatures", ["defaultPasteLabel", "defaultDropLabel"]], ["vs/workbench/api/common/extHostLanguageModels", ["chatAccessWithJustification"]], ["vs/workbench/api/common/extHostLogService", ["remote", "worker", "local"]], ["vs/workbench/api/common/extHostNotebook", ["err.readonly", "fileModifiedError"]], ["vs/workbench/api/common/extHostStatusBar", ["extensionLabel", "status.extensionMessage"]], ["vs/workbench/api/common/extHostTelemetry", ["extensionTelemetryLog"]], ["vs/workbench/api/common/extHostTerminalService", ["launchFail.idMissingOnExtHost"]], ["vs/workbench/api/common/extHostTreeViews", ["treeView.duplicateElement"]], ["vs/workbench/api/common/extHostTunnelService", ["tunnelPrivacy.private", "tunnelPrivacy.public"]], ["vs/workbench/api/common/extHostWorkspace", ["updateerror"]], ["vs/workbench/api/common/jsonValidationExtensionPoint", ["contributes.jsonValidation", "contributes.jsonValidation.fileMatch", "contributes.jsonValidation.url", "invalid.jsonValidation", "invalid.fileMatch", "invalid.url", "invalid.path.1", "invalid.url.fileschema", "invalid.url.schema", "fileMatch", "schema", "jsonValidation"]], ["vs/workbench/api/node/extHostDebugService", ["debug.terminal.title"]], ["vs/workbench/api/test/browser/mainThreadTreeViews.test", ["test", "Test View 1"]], ["vs/workbench/browser/actions/developerActions", ["storageLogDialogMessage", "storageLogDialogDetails", "largeStorageItemDetail", "global", "profile", "workspace", "machine", "user", "removeLargeStorageEntriesPickerButton", "removeLargeStorageEntriesPickerPlaceholder", "removeLargeStorageEntriesPickerDescriptionNoEntries", "removeLargeStorageEntriesConfirmRemove", "removeLargeStorageEntriesConfirmRemoveDetail", "removeLargeStorageEntriesButtonLabel", "screencastModeConfigurationTitle", "screencastMode.location.verticalPosition", "screencastMode.fontSize", "screencastMode.keyboardOptions.description", "screencastMode.keyboardOptions.showKeys", "screencastMode.keyboardOptions.showKeybindings", "screencastMode.keyboardOptions.showCommands", "screencastMode.keyboardOptions.showCommandGroups", "screencastMode.keyboardOptions.showSingleEditorCursorMoves", "screencastMode.keyboardOverlayTimeout", "screencastMode.mouseIndicatorColor", "screencastMode.mouseIndicatorSize", "inspect context keys", "toggle screencast mode", "logStorage", "logWorkingCopies", "removeLargeStorageDatabaseEntries", "startTrackDisposables", "snapshotTrackedDisposables", "stopTrackDisposables"]], ["vs/workbench/browser/actions/helpActions", ["miKeyboardShortcuts", "miVideoTutorials", "miTipsAndTricks", "miDocumentation", "miYouTube", "miUserVoice", "miLicense", "miPrivacyStatement", "keybindingsReference", "openVideoTutorialsUrl", "openTipsAndTricksUrl", "openDocumentationUrl", "newsletterSignup", "openYouTubeUrl", "openUserVoiceUrl", "openLicenseUrl", "openPrivacyStatement", "getStartedWithAccessibilityFeatures", "askVScode", "askVScode"]], ["vs/workbench/browser/actions/layoutActions", ["menuBarIcon", "activityBarLeft", "activityBarRight", "panelLeft", "panelLeftOff", "panelRight", "panelRightOff", "panelBottom", "statusBarIcon", "panelBottomLeft", "panelBottomRight", "panelBottomCenter", "panelBottomJustify", "quickInputAlignmentTop", "quickInputAlignmentCenter", "fullScreenIcon", "centerLayoutIcon", "zenModeIcon", "miToggleCenteredLayout", "toggleSidebarPosition", "moveSidebarRight", "moveSidebarLeft", "cofigureLayoutIcon", "configureLayout", "move side bar right", "move sidebar left", "move second sidebar left", "move second sidebar right", "miMoveSidebarRight", "miMoveSidebarLeft", "miShowEditorArea", "mi<PERSON><PERSON><PERSON>ce", "compositePart.hideSideBarLabel", "primary sidebar", "primary sidebar mnemonic", "openAndCloseSidebar", "compositePart.hideSideBarLabel", "toggleSideBar", "toggleSideBar", "miStatusbar", "tabBar", "tabBar", "editorActionsPosition", "miToggleZenMode", "miMenuBar", "miMenuBarNoMnemonic", "sidebarContainer", "panelContainer", "secondarySideBarContainer", "moveFocusedView.selectView", "moveFocusedView.error.noFocusedView", "moveFocusedView.error.nonMovableView", "moveFocusedView.selectDestination", "moveFocusedView.title", "moveFocusedView.newContainerInPanel", "moveFocusedView.newContainerInSidebar", "moveFocusedView.newContainerInSidePanel", "sidebar", "panel", "secondarySideBar", "resetFocusedView.error.noFocusedView", "selectToHide", "selectToShow", "active", "menuBar", "activityBar", "sideBar", "secondarySideBar", "panel", "statusBar", "leftSideBar", "rightSideBar", "leftPanel", "rightPanel", "centerPanel", "justifyPanel", "top", "center", "fullscreen", "zenMode", "centeredLayout", "toggleVisibility", "sideBarPosition", "panelAlignment", "quickOpen", "layoutModes", "customizeLayoutQuickPickTitle", "close", "restore defaults", "toggleCenteredLayout", "moveSidebarRight", "moveSidebarLeft", "toggleSidebarPosition", "toggleEditor", "toggleSidebar", "toggleStatusbar", "hideEditorTabs", "hideEditorTabsDescription", "hideEditorTabsZenMode", "hideEditorTabsZenModeDescription", "showMultipleEditorTabs", "showMultipleEditorTabsDescription", "showMultipleEditorTabsZenMode", "showMultipleEditorTabsZenModeDescription", "showSingleEditorTab", "showSingleEditorTabDescription", "showSingleEditorTabZenMode", "showSingleEditorTabZenModeDescription", "moveEditorActionsToTitleBar", "moveEditorActionsToTitleBarDescription", "moveEditorActionsToTabBar", "moveEditorActionsToTabBarDescription", "hideEditorActons", "hideEditorActonsDescription", "showEditorActons", "showEditorActonsDescription", "configureTabs", "configureEditors", "toggleSeparatePinnedEditorTabs", "toggleSeparatePinnedEditorTabsDescription", "toggleZenMode", "toggleMenuBar", "resetViewLocations", "moveView", "moveFocusedView", "resetFocusedViewLocation", "increaseViewSize", "increaseEditorWidth", "increaseEditorHeight", "decreaseViewSize", "decreaseEditorWidth", "decreaseEditorHeight", "alignQuickInputTop", "alignQuickInputCenter", "customizeLayout"]], ["vs/workbench/browser/actions/listCommands", ["mitoggleTreeStickyScroll", "toggleTreeStickyScrollDescription", "toggleTreeStickyScroll"]], ["vs/workbench/browser/actions/navigationActions", ["navigateLeft", "navigateRight", "navigateUp", "navigateDown", "focusNextPart", "focusPreviousPart"]], ["vs/workbench/browser/actions/quickAccessActions", ["quickOpenWithModes", "quickOpen", "quickNavigateNext", "quickNavigatePrevious", "quickSelectNext", "quickSelectPrevious"]], ["vs/workbench/browser/actions/textInputActions", ["undo", "redo", "cut", "copy", "paste", "selectAll"]], ["vs/workbench/browser/actions/windowActions", ["remove", "dirtyRecentlyOpenedFolder", "dirtyRecentlyOpenedWorkspace", "workspacesAndFolders", "folders", "files", "openRecentPlaceholderMac", "openRecentPlaceholder", "dirtyWorkspace", "dirtyFolder", "dirtyWorkspaceConfirm", "dirtyFolderConfirm", "dirtyWorkspaceConfirmDetail", "dirtyFolderConfirmDetail", "recentDirtyWorkspaceAriaLabel", "recentDirtyFolderAriaLabel", "miMore", "miToggleFullScreen", "miAbout", "miNewWindow", "miConfirmClose", "miOpenRecent", "openRecent", "quickOpenRecent", "toggleFullScreen", "reloadWindow", "about", "newWindow", "blur"]], ["vs/workbench/browser/actions/workspaceActions", ["miOpenFile", "miOpenFolder", "miOpenFolder", "miOpen", "miOpenWorkspace", "miAddFolderToWorkspace", "miSaveWorkspaceAs", "duplicateWorkspace", "miCloseFolder", "miCloseWorkspace", "workspaces", "openFile", "openFolder", "openFolder", "openFileFolder", "openWorkspaceAction", "closeWorkspace", "openWorkspaceConfigFile", "globalRemoveFolderFromWorkspace", "saveWorkspaceAsAction", "duplicateWorkspaceInNewWindow"]], ["vs/workbench/browser/actions/workspaceCommands", ["add", "addFolderToWorkspaceTitle", "workspaceFolderPickerPlaceholder", "addFolderToWorkspace"]], ["vs/workbench/browser/editor", ["preview", "pinned"]], ["vs/workbench/browser/labels", ["notebookCellLabel", "notebookCellLabel", "notebookCellOutputLabel", "notebookCellOutputLabelSimple"]], ["vs/workbench/browser/parts/activitybar/activitybarPart", ["menu", "hideMenu", "activity bar position", "miDefaultActivityBar", "default", "miTopActivityBar", "top", "miBottomActivityBar", "bottom", "miHideActivityBar", "hide", "positionActivituBar", "positionActivituBar", "positionActivityBarDefault", "positionActivityBarTop", "positionActivityBarBottom", "hideActivityBar", "previousSideBarView", "nextSideBarView", "focusActivityBar"]], ["vs/workbench/browser/parts/auxiliarybar/auxiliaryBarActions", ["toggleAuxiliaryIconRight", "toggleAuxiliaryIconRightOn", "toggleAuxiliaryIconLeft", "toggleAuxiliaryIconLeftOn", "closeSecondarySideBar", "secondary sidebar mnemonic", "openAndCloseAuxiliaryBar", "toggleSecondarySideBar", "toggleSecondarySideBar", "toggleAuxiliaryBar", "closeSecondarySideBar", "focusAuxiliaryBar", "hideAuxiliaryBar", "previousAuxiliaryBarView", "nextAuxiliaryBarView"]], ["vs/workbench/browser/parts/auxiliarybar/auxiliaryBarPart", ["showIcons", "showLabels", "activity bar position", "move second side bar left", "move second side bar right", "hide second side bar"]], ["vs/workbench/browser/parts/banner/bannerPart", ["closeBanner", "focusBanner"]], ["vs/workbench/browser/parts/compositeBar", ["activityBarAriaLabel"]], ["vs/workbench/browser/parts/compositeBarActions", ["titleKeybinding", "additionalViews", "numberBadge", "hide", "keep", "hideBadge", "showBadge", "toggle", "toggleBadge"]], ["vs/workbench/browser/parts/compositePart", ["ariaCompositeToolbarLabel", "viewsAndMoreActions", "titleTooltip"]], ["vs/workbench/browser/parts/dialogs/dialogHandler", ["aboutDetail", "copy", "ok"]], ["vs/workbench/browser/parts/editor/auxiliaryEditorPart", ["enableCompactAuxiliaryWindow", "disableCompactAuxiliaryWindow", "toggleCompactAuxiliaryWindow"]], ["vs/workbench/browser/parts/editor/binaryDiffEditor", ["metadataDiff"]], ["vs/workbench/browser/parts/editor/binaryEditor", ["binaryEditor", "binaryError", "openAnyway"]], ["vs/workbench/browser/parts/editor/breadcrumbs", ["title", "enabled", "filepath", "filepath.on", "filepath.off", "filepath.last", "symbolpath", "symbolpath.on", "symbolpath.off", "symbolpath.last", "symbolSortOrder", "symbolSortOrder.position", "symbolSortOrder.name", "symbolSortOrder.type", "icons", "filteredTypes.file", "filteredTypes.module", "filteredTypes.namespace", "filteredTypes.package", "filteredTypes.class", "filteredTypes.method", "filteredTypes.property", "filteredTypes.field", "filteredTypes.constructor", "filteredTypes.enum", "filteredTypes.interface", "filteredTypes.function", "filteredTypes.variable", "filteredTypes.constant", "filteredTypes.string", "filteredTypes.number", "filteredTypes.boolean", "filteredTypes.array", "filteredTypes.object", "filteredTypes.key", "filteredTypes.null", "filteredTypes.enumMember", "filteredTypes.struct", "filteredTypes.event", "filteredTypes.operator", "filteredTypes.typeParameter"]], ["vs/workbench/browser/parts/editor/breadcrumbsControl", ["separatorIcon", "breadcrumbsPossible", "breadcrumbsVisible", "breadcrumbsActive", "empty", "miBreadcrumbs", "cmd.toggle2", "miBreadcrumbs2", "cmd.toggle", "cmd.focusAndSelect", "cmd.focus"]], ["vs/workbench/browser/parts/editor/breadcrumbsPicker", ["breadcrumbs"]], ["vs/workbench/browser/parts/editor/diffEditorCommands", ["compare", "compare", "compare.nextChange", "compare.previousChange", "toggleInlineView", "swapDiffSides"]], ["vs/workbench/browser/parts/editor/editor.contribution", ["textEditor", "textDiffEditor", "binaryDiffEditor", "sideBySideEditor", "editorQuickAccessPlaceholder", "activeGroupEditorsByMostRecentlyUsedQuickAccess", "editorQuickAccessPlaceholder", "allEditorsByAppearanceQuickAccess", "editorQuickAccessPlaceholder", "allEditorsByMostRecentlyUsedQuickAccess", "lockGroupAction", "unlockGroupAction", "closeGroupAction", "splitUp", "splitDown", "splitLeft", "splitRight", "newWindow", "toggleLockGroup", "close", "splitUp", "splitDown", "splitLeft", "splitRight", "moveEditorGroupToNewWindow", "copyEditorGroupToNewWindow", "tabBar", "multipleTabs", "singleTab", "hideTabs", "tabBar", "multipleTabs", "singleTab", "hideTabs", "editorActionsPosition", "tabBar", "titleBar", "hidden", "configureTabs", "close", "closeOthers", "closeRight", "closeAllSaved", "closeAll", "reopenWith", "keep<PERSON>pen", "pin", "unpin", "splitUp", "splitDown", "splitLeft", "splitRight", "splitInGroup", "joinInGroup", "moveToNewWindow", "copyToNewWindow", "share", "inlineView", "showOpenedEditors", "closeAll", "closeAllSaved", "togglePreviewMode", "maximizeGroup", "unmaximizeGroup", "lockGroup", "configureEditors", "splitEditorRight", "splitEditorDown", "splitEditorDown", "splitEditorRight", "toggleSplitEditorInGroupLayout", "close", "closeAll", "close", "closeAll", "unpin", "close", "unpin", "close", "lockEditorGroup", "unlockEditorGroup", "previousChangeIcon", "navigate.prev.label", "nextChangeIcon", "navigate.next.label", "swapDiffSides", "toggleWhitespace", "ignoreTrimWhitespace.label", "miReopenClosedEditor", "miClearRecentOpen", "miShare", "miEditorLayout", "miSplitEditorUp", "miSplitEditorDown", "miSplitEditorLeft", "miSplitEditorRight", "miSplitEditorInGroup", "miJoinEditorInGroup", "miMoveEditorToNewWindow", "miCopyEditorToNewWindow", "miSingleColumnEditorLayout", "miTwoColumnsEditorLayout", "miThreeColumnsEditorLayout", "miTwoRowsEditorLayout", "miThreeRowsEditorLayout", "miTwoByTwoGridEditorLayout", "miTwoRowsRightEditorLayout", "miTwoColumnsBottomEditorLayout", "miLastEditLocation", "miFirstSideEditor", "miSecondSideEditor", "miNextEditor", "miPreviousEditor", "miNextRecentlyUsedEditor", "miPreviousRecentlyUsedEditor", "miNextEditorInGroup", "miPreviousEditorInGroup", "miNextUsedEditorInGroup", "miPreviousUsedEditorInGroup", "miSwitchEditor", "miFocusFirstGroup", "miFocusSecondGroup", "miFocusThirdGroup", "miFocusFourthGroup", "miFocusFifthGroup", "miNextGroup", "miPreviousGroup", "miFocusLeftGroup", "miFocusRightGroup", "miFocusAboveGroup", "miFocusBelowGroup", "miSwitchGroup", "keepEditor", "pinEditor", "unpinEditor", "closeEditor", "closePinnedEditor", "closeEditorsInGroup", "closeSavedEditors", "closeOtherEditors", "closeRightEditors", "closeEditorGroup", "reopenWith", "miSplitEditorUpWithoutMnemonic", "miSplitEditorDownWithoutMnemonic", "miSplitEditorLeftWithoutMnemonic", "miSplitEditorRightWithoutMnemonic", "miSplitEditorInGroupWithoutMnemonic", "miJoinEditorInGroupWithoutMnemonic", "moveEditorToNewWindow", "copyEditorToNewWindow", "miSingleColumnEditorLayoutWithoutMnemonic", "miTwoColumnsEditorLayoutWithoutMnemonic", "miThreeColumnsEditorLayoutWithoutMnemonic", "miTwoRowsEditorLayoutWithoutMnemonic", "miThreeRowsEditorLayoutWithoutMnemonic", "miTwoByTwoGridEditorLayoutWithoutMnemonic", "miTwoRowsRightEditorLayoutWithoutMnemonic", "miTwoColumnsBottomEditorLayoutWithoutMnemonic"]], ["vs/workbench/browser/parts/editor/editorActions", ["splitEditorGroupUp", "splitEditorGroupDown", "closeEditor", "unpinEditor", "closeOneEditor", "reverting", "navigateForward", "miForward", "navigateBack", "miBack", "confirmClearRecentsMessage", "confirmClearDetail", "clearButtonLabel", "confirmClearEditorHistoryMessage", "confirmClearDetail", "clearButtonLabel", "splitEditorToLeftGroup", "miMoveEditorToNewWindow", "miCopyEditorToNewWindow", "miMoveEditorGroupToNewWindow", "miCopyEditorGroupToNewWindow", "miRestoreEditorsToMainWindow", "miNewEmptyEditorWindow", "splitEditor", "splitEditorOrthogonal", "splitEditorGroupLeft", "splitEditorGroupRight", "splitEditorGroupUp", "splitEditorGroupDown", "joinTwoGroups", "joinAllGroups", "navigateEditorGroups", "focusActiveEditorGroup", "focusFirstEditorGroup", "focusLastEditorGroup", "focusNextGroup", "focusPreviousGroup", "focusLeftGroup", "focusRightGroup", "focusAboveGroup", "focusBelowGroup", "revertAndCloseActiveEditor", "closeEditorsToTheLeft", "closeAllEditors", "closeAllGroups", "closeEditorsInOtherGroups", "closeEditorInAllGroups", "moveActiveGroupLeft", "moveActiveGroupRight", "moveActiveGroupUp", "moveActiveGroupDown", "duplicateActiveGroupLeft", "duplicateActiveGroupRight", "duplicateActiveGroupUp", "duplicateActiveGroupDown", "minimizeOtherEditorGroups", "minimizeOtherEditorGroupsHideSidebar", "evenEditorGroups", "toggleEditorWidths", "maximizeEditorHideSidebar", "toggleMaximizeEditorGroup", "openNextEditor", "openPreviousEditor", "nextEditorInGroup", "openPreviousEditorInGroup", "firstEditorInGroup", "lastEditorInGroup", "navigateForward", "navigateBack", "navigatePrevious", "navigateForwardInEdits", "navigateBackInEdits", "navigatePreviousInEdits", "navigateToLastEditLocation", "navigateForwardInNavigations", "navigateBackInNavigations", "navigatePreviousInNavigationLocations", "navigateToLastNavigationLocation", "reopenClosedEditor", "clearRecentFiles", "showEditorsInActiveGroup", "showAllEditors", "showAllEditorsByMostRecentlyUsed", "quickOpenPreviousRecentlyUsedEditor", "quickOpenLeastRecentlyUsedEditor", "quickOpenPreviousRecentlyUsedEditorInGroup", "quickOpenLeastRecentlyUsedEditorInGroup", "navigateEditorHistoryByInput", "openNextRecentlyUsedEditor", "openPreviousRecentlyUsedEditor", "openNextRecentlyUsedEditorInGroup", "openPreviousRecentlyUsedEditorInGroup", "clearEditorHistory", "moveEditorLeft", "moveEditorRight", "moveEditorToPreviousGroup", "moveEditorToNextGroup", "moveEditorToAboveGroup", "moveEditorToBelowGroup", "moveEditorToLeftGroup", "moveEditorToRightGroup", "moveEditorToFirstGroup", "moveEditorToLastGroup", "splitEditorToPreviousGroup", "splitEditorToNextGroup", "splitEditorToAboveGroup", "splitEditorToBelowGroup", "splitEditorToLeftGroup", "splitEditorToRightGroup", "splitEditorToFirstGroup", "splitEditorToLastGroup", "editorLayoutSingle", "editorLayoutTwoColumns", "editorLayoutThreeColumns", "editorLayoutTwoRows", "editorLayoutThreeRows", "editorLayoutTwoByTwoGrid", "editorLayoutTwoColumnsBottom", "editorLayoutTwoRowsRight", "newGroupLeft", "newGroupRight", "newGroupAbove", "newGroupBelow", "toggleEditorType", "reopenTextEditor", "moveEditorToNewWindow", "copyEditorToNewWindow", "moveEditorGroupToNewWindow", "copyEditorGroupToNewWindow", "restoreEditorsToMainWindow", "newEmptyEditorWindow"]], ["vs/workbench/browser/parts/editor/editorCommands", ["editorCommand.activeEditorMove.description", "editorCommand.activeEditorMove.arg.name", "editorCommand.activeEditorMove.arg.description", "editorCommand.activeEditorCopy.description", "editorCommand.activeEditorCopy.arg.name", "editorCommand.activeEditorCopy.arg.description", "editorGroupLayout.horizontal", "editorGroupLayout.vertical", "splitEditorInGroup", "joinEditorInGroup", "toggleJoinEditorInGroup", "toggleSplitEditorInGroupLayout", "focusLeftSideEditor", "focusRightSideEditor", "focusOtherSideEditor", "toggleEditorGroupLock", "lockEditorGroup", "unlockEditorGroup"]], ["vs/workbench/browser/parts/editor/editorConfiguration", ["interactiveWindow", "markdownPreview", "simpleBrowser", "livePreview", "workbench.editor.autoLockGroups", "workbench.editor.defaultBinaryEditor", "editor.editor<PERSON><PERSON><PERSON><PERSON>", "editorLargeFileSizeConfirmation"]], ["vs/workbench/browser/parts/editor/editorDropTarget", ["dropIntoEditorPrompt"]], ["vs/workbench/browser/parts/editor/editorGroupView", ["ariaLabelGroupActions", "emptyEditorGroup", "groupLabelLong", "groupLabel", "groupAriaLabelLong", "groupAriaLabel", "moveErrorDetails"]], ["vs/workbench/browser/parts/editor/editorGroupWatermark", ["watermark.showCommands", "watermark.quickAccess", "watermark.openFile", "watermark.openFolder", "watermark.openFileFolder", "watermark.openRecent", "watermark.newUntitledFile", "watermark.findInFiles", "watermark.toggleTerminal", "watermark.startDebugging", "watermark.openSettings", "watermark.openChat", "editor<PERSON><PERSON><PERSON><PERSON><PERSON>"]], ["vs/workbench/browser/parts/editor/editor<PERSON>anes", ["editorOpenErrorDialog", "ok"]], ["vs/workbench/browser/parts/editor/editorParts", ["groupLabel"]], ["vs/workbench/browser/parts/editor/editorPlaceholder", ["trustRequiredEditor", "requiresFolderTrustText", "requiresWorkspaceTrustText", "manageTrust", "errorEditor", "unavailableResourceErrorEditorText", "unknownErrorEditorTextWithError", "unknownErrorEditorTextWithoutError", "retry"]], ["vs/workbench/browser/parts/editor/editorQuickAccess", ["noViewResults", "entryAriaLabelWithGroupDirty", "entryAriaLabelWithGroup", "entryAriaLabel<PERSON>irty", "closeEditor"]], ["vs/workbench/browser/parts/editor/editorStatus", ["singleSelectionRange", "singleSelection", "multiSelectionRange", "multiSelection", "endOfLineLineFeed", "endOfLineCarriageReturnLineFeed", "noE<PERSON>or", "noWritableCodeEditor", "indentConvert", "indentView", "pickAction", "tabFocusModeEnabled", "status.editor.tabFocusMode", "disableTabMode", "inputModeOvertype", "status.editor.enableInsertMode", "columnSelectionModeEnabled", "status.editor.columnSelectionMode", "disableColumnSelectionMode", "status.editor.selection", "gotoLine", "status.editor.indentation", "selectIndentation", "status.editor.encoding", "selectEncoding", "status.editor.eol", "selectEOL", "status.editor.mode", "selectLanguageMode", "status.editor.info", "fileInfo", "spacesSize", "spacesAndTabsSize", "tabSize", "currentProblem", "currentProblem", "showLanguageExtensions", "changeLanguageMode.description", "changeLanguageMode.arg.name", "noE<PERSON>or", "languageDescription", "languageDescriptionConfigured", "languagesPicks", "configureModeSettings", "configureAssociationsExt", "autoDetect", "pickLanguage", "currentAssociation", "pickLanguageToConfigure", "noE<PERSON>or", "noWritableCodeEditor", "pickEndOfLine", "noE<PERSON>or", "noE<PERSON>or", "noFileEditor", "saveWithEncoding", "reopenWithEncoding", "pickAction", "guessedEncoding", "pickEncodingForReopen", "pickEncodingForSave", "changeMode", "changeEndOfLine", "changeEncoding"]], ["vs/workbench/browser/parts/editor/editorTabsControl", ["ariaLabelEditorActions", "draggedEditorGroup"]], ["vs/workbench/browser/parts/editor/multiEditorTabsControl", ["ariaLabelTabActions"]], ["vs/workbench/browser/parts/editor/sideBySideEditor", ["sideBySideEditor"]], ["vs/workbench/browser/parts/editor/textCodeEditor", ["textEditor"]], ["vs/workbench/browser/parts/editor/textDiffEditor", ["textDiffEditor", "fileTooLargeForHeapErrorWithSize", "fileTooLargeForHeapErrorWithoutSize"]], ["vs/workbench/browser/parts/editor/textEditor", ["editor"]], ["vs/workbench/browser/parts/globalCompositeBar", ["accountsViewBarIcon", "hideAccounts", "manage", "accounts", "accounts", "loading", "authProviderUnavailable", "manageTrustedExtensions", "signOut", "manage", "manage profile", "hideAccounts", "accounts", "manage"]], ["vs/workbench/browser/parts/notifications/notificationAccessibleView", ["notification.accessibleViewSrc", "notification.accessibleView", "clearNotification", "clearNotification"]], ["vs/workbench/browser/parts/notifications/notificationsActions", ["clearIcon", "clearAllIcon", "hideIcon", "expandIcon", "collapseIcon", "configureIcon", "doNotDisturbIcon", "clearNotification", "clearNotifications", "toggleDoNotDisturbMode", "toggleDoNotDisturbModeBySource", "configureDoNotDisturbMode", "hideNotificationsCenter", "expandNotification", "collapseNotification", "configureNotification", "copyNotification"]], ["vs/workbench/browser/parts/notifications/notificationsAlerts", ["alertErrorMessage", "alertWarningMessage", "alertInfoMessage"]], ["vs/workbench/browser/parts/notifications/notificationsCenter", ["notificationsEmpty", "notifications", "notificationsToolbar", "turnOnNotifications", "turnOffNotifications", "moreSources", "notificationsCenterWidgetAriaLabel"]], ["vs/workbench/browser/parts/notifications/notificationsCommands", ["selectSources", "notifications", "showNotifications", "hideNotifications", "clearAllNotifications", "acceptNotificationPrimaryAction", "toggleDoNotDisturbMode", "toggleDoNotDisturbModeBySource", "focusNotificationToasts"]], ["vs/workbench/browser/parts/notifications/notificationsList", ["notificationAccessibleViewHint", "notificationAccessibleViewHintNoKb", "notificationAriaLabelHint", "notificationAriaLabel", "notificationWithSourceAriaLabelHint", "notificationWithSourceAriaLabel", "notificationsList"]], ["vs/workbench/browser/parts/notifications/notificationsStatus", ["status.notifications", "status.notifications", "status.doNotDisturb", "status.doNotDisturbTooltip", "hideNotifications", "zeroNotifications", "noNotifications", "oneNotification", "notifications", "noNotificationsWithProgress", "oneNotificationWithProgress", "notificationsWithProgress", "status.message"]], ["vs/workbench/browser/parts/notifications/notificationsToasts", ["notificationAriaLabel", "notificationWithSourceAriaLabel"]], ["vs/workbench/browser/parts/notifications/notificationsViewer", ["executeCommand", "notificationActions", "turnOnNotifications", "turnOffNotifications", "notificationSource"]], ["vs/workbench/browser/parts/paneCompositeBar", ["moveToMenu", "resetLocation", "resetLocation", "panel", "sidebar", "auxiliarybar"]], ["vs/workbench/browser/parts/paneCompositePart", ["pane.emptyMessage", "moreActions", "views"]], ["vs/workbench/browser/parts/panel/panelActions", ["maximizeIcon", "restoreIcon", "closeIcon", "togglePanelOffIcon", "togglePanelOnIcon", "closePanel", "toggle panel mnemonic", "openAndClosePanel", "focusPanel", "positionPanelTopShort", "positionPanelLeftShort", "positionPanelRightShort", "positionPanelBottomShort", "alignPanelLeftShort", "alignPanelRightShort", "alignPanelCenterShort", "alignPanelJustifyShort", "positionPanel", "alignPanel", "maximizePanel", "minimizePanel", "panelMaxNotSupported", "togglePanel", "togglePanelVisibility", "closePanel", "focusPanel", "positionPanelTop", "positionPanelLeft", "positionPanelRight", "positionPanelBottom", "alignPanelLeft", "alignPanelRight", "alignPanelCenter", "alignPanelJustify", "previousPanelView", "nextPanelView", "toggleMaximizedPanel", "movePanelToSecondarySideBar", "movePanelToSecondarySideBar", "moveSidePanelToPanel", "moveSidePanelToPanel"]], ["vs/workbench/browser/parts/panel/panelPart", ["showIcons", "showLabels", "panel position", "align panel", "hide<PERSON>anel"]], ["vs/workbench/browser/parts/sidebar/sidebarActions", ["closeSidebar", "focusSideBar"]], ["vs/workbench/browser/parts/sidebar/sidebarPart", ["toggleActivityBar"]], ["vs/workbench/browser/parts/statusbar/statusbarActions", ["hide", "manageExtension", "focusStatusBar"]], ["vs/workbench/browser/parts/statusbar/statusbarPart", ["hideStatusBar"]], ["vs/workbench/browser/parts/titlebar/commandCenterControl", ["label.dfl", "label1", "label2", "title", "title2", "title3"]], ["vs/workbench/browser/parts/titlebar/menubarControl", ["mFile", "mEdit", "mSelection", "m<PERSON>iew", "mGoto", "mTerminal", "mHelp", "mPreferences", "menubar.customTitlebarAccessibilityNotification", "goToSetting", "checkForUpdates", "checkingForUpdates", "download now", "DownloadingUpdate", "installUpdate...", "installingUpdate", "restartToUpdate", "focusMenu"]], ["vs/workbench/browser/parts/titlebar/titlebarActions", ["toggle.commandCenter", "toggle.commandCenterDescription", "toggle.navigation", "toggle.navigationDescription", "toggle.layout", "toggle.layoutDescription", "toggle.hideCustomTitleBar", "toggle.hideCustomTitleBarInFullScreen", "toggle.customTitleBar", "toggle.editorActions", "accounts", "accounts", "manage", "manage", "showCustomTitleBar", "hideCustomTitleBar", "hideCustomTitleBarInFullScreen"]], ["vs/workbench/browser/parts/titlebar/titlebarPart", ["ariaLabelTitleActions", "focusTitleBar"]], ["vs/workbench/browser/parts/titlebar/windowTitle", ["userIsAdmin", "userIsSudo", "devExtensionWindowTitlePrefix"]], ["vs/workbench/browser/parts/views/checkbox", ["checked", "unchecked"]], ["vs/workbench/browser/parts/views/treeView", ["no-dataprovider", "treeView.enableCollapseAll", "treeView.enableRefresh", "refresh", "collapseAll", "treeView.toggleCollapseAll", "command-error"]], ["vs/workbench/browser/parts/views/viewFilter", ["more filters"]], ["vs/workbench/browser/parts/views/viewPane", ["viewPaneContainerExpandedIcon", "viewPaneContainerCollapsedIcon", "viewToolbarAriaLabel", "viewAccessibilityHelp"]], ["vs/workbench/browser/parts/views/viewPaneContainer", ["views", "viewMoveUp", "viewMoveLeft", "viewMoveDown", "viewMoveRight", "viewsMove"]], ["vs/workbench/browser/quickaccess", ["inQuickOpen"]], ["vs/workbench/browser/web.main", ["reset user data message", "reset"]], ["vs/workbench/browser/window", ["quitMessageMac", "quitMessage", "closeWindowMessage", "quitButtonLabel", "exitButtonLabel", "closeWindowButtonLabel", "doNotAskAgain", "shutdownError", "shutdownErrorDetail", "reload", "unableToOpenExternal", "open", "learnMore", "openExternalDialogButtonRetry.v2", "openExternalDialogDetail.v2", "openExternalDialogButtonInstall.v3", "openExternalDialogDetailNoInstall", "openExternalDialogTitle"]], ["vs/workbench/browser/workbench.contribution", ["browser", "workbench.editor.titleScrollbarSizing.default", "workbench.editor.titleScrollbarSizing.large", "tabScrollbarHeight", "workbench.editor.showTabs.multiple", "workbench.editor.showTabs.single", "workbench.editor.showTabs.none", "showEditorTabs", "workbench.editor.editorActionsLocation.default", "workbench.editor.editorActionsLocation.titleBar", "workbench.editor.editorActionsLocation.hidden", "editorActionsLocation", "alwaysShowEditorActions", "wrapTabs", "scrollToSwitchTabs", "highlightModifiedTabs", "decorations.badges", "decorations.colors", "workbench.editor.label.enabled", "workbench.editor.label.patterns", "workbench.editor.label.dirname", "workbench.editor.label.nthdirname", "workbench.editor.label.filename", "workbench.editor.label.extname", "workbench.editor.label.nthextname", "customEditorLabelDescriptionExample", "workbench.editor.label.template", "workbench.editor.labelFormat.default", "workbench.editor.labelFormat.short", "workbench.editor.labelFormat.medium", "workbench.editor.labelFormat.long", "tabDescription", "workbench.editor.untitled.labelFormat.content", "workbench.editor.untitled.labelFormat.name", "untitledLabelFormat", "workbench.editor.empty.hint", "workbench.editor.languageDetection", "workbench.editor.historyBasedLanguageDetection", "workbench.editor.preferBasedLanguageDetection", "workbench.editor.showLanguageDetectionHints", "workbench.editor.showLanguageDetectionHints.editors", "workbench.editor.showLanguageDetectionHints.notebook", "tabActionLocation", "workbench.editor.tabActionCloseVisibility", "workbench.editor.tabActionUnpinVisibility", "workbench.editor.tabSizing.fit", "workbench.editor.tabSizing.shrink", "workbench.editor.tabSizing.fixed", "tabSizing", "workbench.editor.tabSizingFixedMinWidth", "workbench.editor.tabSizingFixedMaxWidth", "workbench.editor.tabHeight", "workbench.editor.pinnedTabSizing.normal", "workbench.editor.pinnedTabSizing.compact", "workbench.editor.pinnedTabSizing.shrink", "pinnedTabSizing", "workbench.editor.pinnedTabsOnSeparateRow", "workbench.editor.preventPinnedEditorClose.always", "workbench.editor.preventPinnedEditorClose.onlyKeyboard", "workbench.editor.preventPinnedEditorClose.onlyMouse", "workbench.editor.preventPinnedEditorClose.never", "workbench.editor.preventPinnedEditorClose", "workbench.editor.splitSizingAuto", "workbench.editor.splitSizingDistribute", "workbench.editor.splitSizingSplit", "splitSizing", "splitOnDragAndDrop", "dragToOpenWindow", "focusRecentEditorAfterClose", "showIcons", "enablePreview", "enablePreviewFromQuickOpen", "enablePreviewFromCodeNavigation", "closeOnFileDelete", "editorOpenPositioning", "sideBySideDirection", "closeEmptyGroups", "revealIfOpen", "mouseBackForwardToNavigate", "navigationScope", "workbench.editor.navigationScopeDefault", "workbench.editor.navigationScopeEditorGroup", "workbench.editor.navigationScopeEditor", "restoreViewState", "sharedViewState", "splitInGroupLayout", "workbench.editor.splitInGroupLayoutVertical", "workbench.editor.splitInGroupLayoutHorizontal", "centeredLayoutAutoResize", "centeredLayoutDynamicWidth", "doubleClickTabToToggleEditorGroupSizes", "workbench.editor.doubleClickTabToToggleEditorGroupSizes.maximize", "workbench.editor.doubleClickTabToToggleEditorGroupSizes.expand", "workbench.editor.doubleClickTabToToggleEditorGroupSizes.off", "limitEditorsEnablement", "limitEditorsMaximum", "limitEditorsExcludeDirty", "perEditorGroup", "localHistoryEnabled", "localHistoryMaxFileSize", "localHistoryMaxFileEntries", "exclude", "mergeWindow", "commandHistory", "preserveInput", "suggestCommands", "askChatLocation", "askChatLocation.chatView", "askChatLocation.quickChat", "enableNaturalLanguageSearch", "closeOnFocusLost", "workbench.quickOpen.preserveInput", "openDefaultSettings", "useSplitJSON", "openDefaultKeybindings", "sideBarLocation", "panelShowLabels", "panelDefaultLocation", "panelOpensMaximized", "workbench.panel.opensMaximized.always", "workbench.panel.opensMaximized.never", "workbench.panel.opensMaximized.preserve", "secondarySideBarDefaultVisibility", "workbench.secondarySideBar.defaultVisibility.hidden", "workbench.secondarySideBar.defaultVisibility.visibleInWorkspace", "workbench.secondarySideBar.defaultVisibility.visible", "secondarySideBarShowLabels", "statusBarVisibility", "activityBarLocation", "workbench.activityBar.location.default", "workbench.activityBar.location.top", "workbench.activityBar.location.bottom", "workbench.activityBar.location.hide", "activityBarIconClickBehavior", "workbench.activityBar.iconClickBehavior.toggle", "workbench.activityBar.iconClickBehavior.focus", "viewVisibility", "workbench.view.showQuietly", "workbench.panel.output", "fontAliasing", "workbench.fontAliasing.default", "workbench.fontAliasing.antialiased", "workbench.fontAliasing.none", "workbench.fontAliasing.auto", "settings.editor.ui", "settings.editor.json", "settings.editor.desc", "settings.showSuggestions", "workbench.hover.delay", "workbench.reduceMotion", "workbench.reduceMotion.on", "workbench.reduceMotion.off", "workbench.reduceMotion.auto", "navigationControlEnabledWeb", "navigationControlEnabled", "layoutControlEnabledWeb", "layoutControlEnabled", "layoutcontrol.type.menu", "layoutcontrol.type.toggles", "layoutcontrol.type.both", "layoutControlType", "tips.enabled", "windowTitle", "activeEditorShort", "activeEditorMedium", "activeEditorLong", "activeFolderShort", "activeFolderMedium", "activeFolderLong", "folderName", "folderPath", "rootName", "rootNameShort", "rootPath", "profileName", "appName", "remoteName", "dirty", "focused<PERSON>iew", "activeRepositoryName", "activeRepositoryBranchName", "activeEditorState", "separator", "window.titleSeparator", "window.commandCenterWeb", "window.commandCenter", "window.menuBarVisibility.classic", "window.menuBarVisibility.visible", "window.menuBarVisibility.toggle.mac", "window.menuBarVisibility.toggle", "window.menuBarVisibility.hidden", "window.menuBarVisibility.compact.web", "window.menuBarVisibility.compact", "menuBarVisibility.mac", "menuBarVisibility", "enableMenuBarMnemonics", "customMenuBarAltFocus", "window.openFilesInNewWindow.on", "window.openFilesInNewWindow.off", "window.openFilesInNewWindow.defaultMac", "window.openFilesInNewWindow.default", "openFilesInNewWindowMac", "openFilesInNewWindow", "window.openFoldersInNewWindow.on", "window.openFoldersInNewWindow.off", "window.openFoldersInNewWindow.default", "openFoldersInNewWindow", "window.confirmBeforeClose.always.web", "window.confirmBeforeClose.always", "window.confirmBeforeClose.keyboardOnly.web", "window.confirmBeforeClose.keyboardOnly", "window.confirmBeforeClose.never.web", "window.confirmBeforeClose.never", "confirmBeforeCloseWeb", "confirmBeforeClose", "problems.visibility", "zenModeConfigurationTitle", "zenMode.fullScreen", "zenMode.centerLayout", "zenMode.showTabs", "zenMode.showTabs.multiple", "zenMode.showTabs.single", "zenMode.showTabs.none", "zenMode.hideStatusBar", "zenMode.hideActivityBar", "zenMode.hideLineNumbers", "zenMode.restore", "zenMode.silentNotifications"]], ["vs/workbench/common/configuration", ["applicationConfigurationTitle", "workbenchConfigurationTitle", "securityConfigurationTitle", "problemsConfigurationTitle", "windowConfigurationTitle", "security.allowedUNCHosts.patternErrorMessage", "security.allowedUNCHosts", "security.restrictUNCAccess", "active window", "newWindowProfile"]], ["vs/workbench/common/contextkeys", ["workbenchState", "workspaceFolderCount", "dirtyWorkingCopies", "remoteName", "virtualWorkspace", "temporaryWorkspace", "isFullscreen", "isAuxiliaryWindowFocusedContext", "isWindowAlwaysOnTop", "embedderIdentifier", "activeEditorIsDirty", "activeEditorIsNotPreview", "activeEditorIsFirstInGroup", "activeEditorIsLastInGroup", "activeEditorIsPinned", "activeEditorIsReadonly", "activeCompareEditorCanSwap", "activeEditorCanToggleReadonly", "activeEditorCanRevert", "activeEditor", "activeEditorAvailableEditorIds", "textCompareEditorVisible", "textCompareEditorActive", "sideBySideEditorActive", "groupEditorsCount", "activeEditorGroupEmpty", "activeEditorGroupIndex", "activeEditorGroupLast", "activeEditorGroupLocked", "multipleEditorGroups", "multipleEditorsSelectedInGroup", "twoEditorsSelectedInGroup", "SelectedEditorsInGroupFileOrUntitledResourceContextKey", "editorPartMultipleEditorGroups", "editorPartEditorGroupMaximized", "isAuxiliaryEditorPart", "editorIsOpen", "inZenMode", "isMainEditorCenteredLayout", "splitEditorsVertically", "mainEditorAreaVisible", "editorTabsVisible", "sideBarVisible", "sideBarFocus", "activeViewlet", "statusBarFocused", "titleBarStyle", "titleBarVisible", "isAuxiliaryTitleBar", "isCompactTitleBar", "bannerFocused", "notificationFocus", "notificationCenterVisible", "notificationToastsVisible", "activeAuxiliary", "auxiliaryBarFocus", "auxiliaryBarVisible", "activePanel", "panelFocus", "panelPosition", "panelAlignment", "panelVisible", "panelMaximized", "focused<PERSON>iew", "resourceScheme", "resourceFilename", "resourceDirname", "resourcePath", "resourceLangId", "resource", "resourceExtname", "resourceSet", "isFileSystemResource"]], ["vs/workbench/common/editor", ["promptOpenWith.defaultEditor.displayName", "builtinProviderDisplayName", "openLargeFile", "configureEditorLargeFileConfirmation"]], ["vs/workbench/common/editor/diffEditorInput", ["sideBySideLabels"]], ["vs/workbench/common/editor/sideBySideEditorInput", ["sideBySideLabels"]], ["vs/workbench/common/editor/textEditorModel", ["languageAutoDetected"]], ["vs/workbench/common/theme", ["tabActiveBackground", "tabUnfocusedActiveBackground", "tabInactiveBackground", "tabUnfocusedInactiveBackground", "tabActiveForeground", "tabInactiveForeground", "tabUnfocusedActiveForeground", "tabUnfocusedInactiveForeground", "tabHoverBackground", "tabUnfocusedHoverBackground", "tabHoverForeground", "tabUnfocusedHoverForeground", "tabBorder", "lastPinnedTabBorder", "tabActiveBorder", "tabActiveUnfocusedBorder", "tabActiveBorderTop", "tabActiveUnfocusedBorderTop", "tabSelectedBorderTop", "tabSelectedBackground", "tabSelectedForeground", "tabHoverBorder", "tabUnfocusedHoverBorder", "tabDragAndDropBorder", "tabActiveModifiedBorder", "tabInactiveModifiedBorder", "unfocusedActiveModifiedBorder", "unfocusedINactiveModifiedBorder", "editor<PERSON><PERSON>B<PERSON><PERSON>", "editorGroupEmptyBackground", "editorGroupFocusedEmptyBorder", "tabsContainerBackground", "tabsContainerBorder", "editorGroupHeaderBackground", "editorTitleContainerBorder", "editorGroupBorder", "editorDragAndDropBackground", "editorDropIntoPromptForeground", "editorDropIntoPromptBackground", "editorDropIntoPromptBorder", "sideBySideEditor.horizontalBorder", "sideBySideEditor.verticalBorder", "outputViewBackground", "outputViewStickyScrollBackground", "banner.background", "banner.foreground", "banner.iconForeground", "statusBarForeground", "statusBarNoFolderForeground", "statusBarBackground", "statusBarNoFolderBackground", "statusBarBorder", "statusBarFocusBorder", "statusBarNoFolderBorder", "statusBarItemActiveBackground", "statusBarItemFocusBorder", "statusBarItemHoverBackground", "statusBarItemHoverForeground", "statusBarItemCompactHoverBackground", "statusBarProminentItemForeground", "statusBarProminentItemBackground", "statusBarProminentItemHoverForeground", "statusBarProminentItemHoverBackground", "statusBarErrorItemBackground", "statusBarErrorItemForeground", "statusBarErrorItemHoverForeground", "statusBarErrorItemHoverBackground", "statusBarWarningItemBackground", "statusBarWarningItemForeground", "statusBarWarningItemHoverForeground", "statusBarWarningItemHoverBackground", "activityBarBackground", "activityBarForeground", "activityBarInActiveForeground", "activityBarBorder", "activityBarActiveBorder", "activityBarActiveFocusBorder", "activityBarActiveBackground", "activityBarDragAndDropBorder", "activityBarBadgeBackground", "activityBarBadgeForeground", "activityBarTop", "activityBarTopActiveFocusBorder", "activityBarTopActiveBackground", "activityBarTopInActiveForeground", "activityBarTopDragAndDropBorder", "activityBarTopBackground", "panelBackground", "panelBorder", "panelTitleBorder", "panelActiveTitleForeground", "panelInactiveTitleForeground", "panelActiveTitleBorder", "panelTitleBadgeBackground", "panelTitleBadgeForeground", "panelInputBorder", "panelDragAndDropBorder", "panelSectionDragAndDropBackground", "panelSectionHeaderBackground", "panelSectionHeaderForeground", "panelSectionHeaderBorder", "panelSectionBorder", "panelStickyScrollBackground", "panelStickyScrollBorder", "panelStickyScrollShadow", "profileBadgeBackground", "profileBadgeForeground", "statusBarItemRemoteBackground", "statusBarItemRemoteForeground", "statusBarRemoteItemHoverForeground", "statusBarRemoteItemHoverBackground", "statusBarItemOfflineBackground", "statusBarItemOfflineForeground", "statusBarOfflineItemHoverForeground", "statusBarOfflineItemHoverBackground", "extensionBadge.remoteBackground", "extensionBadge.remoteForeground", "sideBarBackground", "sideBarForeground", "sideBarBorder", "sideBarTitleBackground", "sideBarTitleForeground", "sideBarTitleBorder", "sideBarDragAndDropBackground", "sideBarSectionHeaderBackground", "sideBarSectionHeaderForeground", "sideBarSectionHeaderBorder", "sideBarActivityBarTopBorder", "sideBarStickyScrollBackground", "sideBarStickyScrollBorder", "sideBarStickyScrollShadow", "titleBarActiveForeground", "titleBarInactiveForeground", "titleBarActiveBackground", "titleBarInactiveBackground", "titleBarBorder", "menubarSelectionForeground", "menubarSelectionBackground", "menubarSelectionBorder", "commandCenter-foreground", "commandCenter-activeForeground", "commandCenter-inactiveForeground", "commandCenter-background", "commandCenter-activeBackground", "commandCenter-border", "commandCenter-activeBorder", "commandCenter-inactiveBorder", "notificationCenterBorder", "notificationToastBorder", "notificationsForeground", "notificationsBackground", "notificationsLink", "notificationCenterHeaderForeground", "notificationCenterHeaderBackground", "notificationsBorder", "notificationsErrorIconForeground", "notificationsWarningIconForeground", "notificationsInfoIconForeground", "windowActiveBorder", "windowInactiveBorder"]], ["vs/workbench/common/views", ["views log", "defaultViewIcon", "duplicateId", "treeView.notRegistered"]], ["vs/workbench/contrib/accessibility/browser/accessibilityConfiguration", ["accessibilityConfigurationTitle", "sound.enabled.auto", "sound.enabled.on", "sound.enabled.off", "announcement.enabled.auto", "announcement.enabled.off", "verbosity.terminal.description", "verbosity.diffEditor.description", "verbosity.chat.description", "verbosity.interactiveEditor.description", "verbosity.inlineCompletions.description", "verbosity.keybindingsEditor.description", "verbosity.notebook", "verbosity.hover", "verbosity.notification", "verbosity.emptyEditorHint", "verbosity.replEditor.description", "verbosity.comments", "verbosity.diffEditorActive", "verbosity.debug", "verbosity.walkthrough", "terminal.integrated.accessibleView.closeOnKeyPress", "verbosity.scm", "accessibility.signalOptions.volume", "accessibility.signalOptions.debouncePositionChanges", "accessibility.signalOptions.delays.general.announcement", "accessibility.signalOptions.delays.general.sound", "accessibility.signalOptions.delays.warningAtPosition.announcement", "accessibility.signalOptions.delays.warningAtPosition.sound", "accessibility.signalOptions.delays.errorAtPosition.announcement", "accessibility.signalOptions.delays.errorAtPosition.sound", "accessibility.signals.lineHasBreakpoint", "accessibility.signals.lineHasBreakpoint.sound", "accessibility.signals.lineHasBreakpoint.announcement", "accessibility.signals.lineHasInlineSuggestion", "accessibility.signals.lineHasInlineSuggestion.sound", "accessibility.signals.nextEditSuggestion", "accessibility.signals.nextEditSuggestion.sound", "accessibility.signals.nextEditSuggestion.announcement", "accessibility.signals.lineHasError", "accessibility.signals.lineHasError.sound", "accessibility.signals.lineHasError.announcement", "accessibility.signals.lineHasFoldedArea", "accessibility.signals.lineHasFoldedArea.sound", "accessibility.signals.lineHasFoldedArea.announcement", "accessibility.signals.lineHasWarning", "accessibility.signals.lineHasWarning.sound", "accessibility.signals.lineHasWarning.announcement", "accessibility.signals.positionHasError", "accessibility.signals.positionHasError.sound", "accessibility.signals.positionHasError.announcement", "accessibility.signals.positionHasWarning", "accessibility.signals.positionHasWarning.sound", "accessibility.signals.positionHasWarning.announcement", "accessibility.signals.onDebugBreak", "accessibility.signals.onDebugBreak.sound", "accessibility.signals.onDebugBreak.announcement", "accessibility.signals.noInlayHints", "accessibility.signals.noInlayHints.sound", "accessibility.signals.noInlayHints.announcement", "accessibility.signals.taskCompleted", "accessibility.signals.taskCompleted.sound", "accessibility.signals.taskCompleted.announcement", "accessibility.signals.taskFailed", "accessibility.signals.taskFailed.sound", "accessibility.signals.taskFailed.announcement", "accessibility.signals.terminalCommandFailed", "accessibility.signals.terminalCommandFailed.sound", "accessibility.signals.terminalCommandFailed.announcement", "accessibility.signals.terminalCommandSucceeded", "accessibility.signals.terminalCommandSucceeded.sound", "accessibility.signals.terminalCommandSucceeded.announcement", "accessibility.signals.terminalQuickFix", "accessibility.signals.terminalQuickFix.sound", "accessibility.signals.terminalQuickFix.announcement", "accessibility.signals.terminalBell", "accessibility.signals.terminalBell.sound", "accessibility.signals.terminalBell.announcement", "accessibility.signals.diffLineInserted", "accessibility.signals.sound", "accessibility.signals.diffLineModified", "accessibility.signals.diffLineModified.sound", "accessibility.signals.diffLineDeleted", "accessibility.signals.diffLineDeleted.sound", "accessibility.signals.chatEditModifiedFile", "accessibility.signals.chatEditModifiedFile.sound", "accessibility.signals.notebookCellCompleted", "accessibility.signals.notebookCellCompleted.sound", "accessibility.signals.notebookCellCompleted.announcement", "accessibility.signals.notebookCellFailed", "accessibility.signals.notebookCellFailed.sound", "accessibility.signals.notebookCellFailed.announcement", "accessibility.signals.progress", "accessibility.signals.progress.sound", "accessibility.signals.progress.announcement", "accessibility.signals.chatRequestSent", "accessibility.signals.chatRequestSent.sound", "accessibility.signals.chatRequestSent.announcement", "accessibility.signals.chatResponseReceived", "accessibility.signals.chatResponseReceived.sound", "accessibility.signals.codeActionTriggered", "accessibility.signals.codeActionTriggered.sound", "accessibility.signals.codeActionApplied", "accessibility.signals.codeActionApplied.sound", "accessibility.signals.voiceRecordingStarted", "accessibility.signals.voiceRecordingStarted.sound", "accessibility.signals.voiceRecordingStopped", "accessibility.signals.voiceRecordingStopped.sound", "accessibility.signals.clear", "accessibility.signals.clear.sound", "accessibility.signals.clear.announcement", "accessibility.signals.editsUndone", "accessibility.signals.editsUndone.sound", "accessibility.signals.editsUndone.announcement", "accessibility.signals.editsKept", "accessibility.signals.editsKept.sound", "accessibility.signals.editsKept.announcement", "accessibility.signals.save", "accessibility.signals.save.sound", "accessibility.signals.save.sound.userGesture", "accessibility.signals.save.sound.always", "accessibility.signals.save.sound.never", "accessibility.signals.save.announcement", "accessibility.signals.save.announcement.userGesture", "accessibility.signals.save.announcement.always", "accessibility.signals.save.announcement.never", "accessibility.signals.format", "accessibility.signals.format.sound", "accessibility.signals.format.userGesture", "accessibility.signals.format.always", "accessibility.signals.format.never", "accessibility.signals.format.announcement", "accessibility.signals.format.announcement.userGesture", "accessibility.signals.format.announcement.always", "accessibility.signals.format.announcement.never", "accessibility.underlineLinks", "accessibility.debugWatchVariableAnnouncements", "accessibility.replEditor.readLastExecutedOutput", "replEditor.autoFocusAppendedCell", "accessibility.windowTitleOptimized", "dimUnfocusedEnabled", "dimUnfocusedOpacity", "accessibility.hideAccessibleView", "voice.speechTimeout", "voice.ignoreCodeBlocks", "voice.speechLanguage", "accessibility.voice.autoSynthesize.on", "accessibility.voice.autoSynthesize.off", "autoSynthesize", "speechLanguage.auto"]], ["vs/workbench/contrib/accessibility/browser/accessibilityStatus", ["screenReaderDetectedExplanation.question", "screenReaderDetectedExplanation.answerYes", "screenReaderDetectedExplanation.answerNo", "screenReaderDetectedExplanation.answerLearnMore", "screenReaderDetected", "status.editor.screenReaderMode"]], ["vs/workbench/contrib/accessibility/browser/accessibleView", ["keybindings", "selectKeybinding", "symbol<PERSON><PERSON><PERSON>", "symbolLabelAria", "disableAccessibilityHelp", "ariaAccessibleViewActionsBottom", "ariaAccessibleViewActions", "accessibility-help", "accessible-view", "accessible-view-hint", "accessibility-help-hint", "accessibleHelpToolbar", "accessibleViewToolbar", "toolbar", "intro", "insertAtCursor", "insertIntoNewFile", "runInTerminal", "accessibleViewNextPreviousHint", "acessibleViewDisableHint", "goToSymbolHint", "configureKb", "configureKbAssigned", "exit", "openDoc", "acessibleViewHint", "acessibleViewHintNoKbEither", "accessibleViewSymbolQuickPickPlaceholder", "accessibleViewSymbolQuickPickTitle"]], ["vs/workbench/contrib/accessibility/browser/accessibleViewActions", ["editor.action.accessibleViewNext", "editor.action.accessibleViewNextCodeBlock", "editor.action.accessibleViewPreviousCodeBlock", "editor.action.accessibleViewPrevious", "editor.action.accessibleViewGoToSymbol", "editor.action.accessibilityHelp", "editor.action.accessibleView", "editor.action.accessibleViewDisableHint", "editor.action.accessibilityHelpConfigureUnassignedKeybindings", "editor.action.accessibilityHelpConfigureAssignedKeybindings", "editor.action.accessibilityHelpOpenHelpLink", "editor.action.accessibleViewAcceptInlineCompletionAction"]], ["vs/workbench/contrib/accessibilitySignals/browser/commands", ["accessibility.sound.help.description", "sounds.help.settings", "sounds.help.placeholder", "accessibility.announcement.help.description", "announcement.help.settings", "announcement.help.placeholder", "announcement.help.placeholder.disabled", "signals.sound.help", "accessibility.announcement.help"]], ["vs/workbench/contrib/accessibilitySignals/browser/openDiffEditorAnnouncement", ["openDiffEditorAnnouncement"]], ["vs/workbench/contrib/authentication/browser/actions/manageAccountPreferencesForExtensionAction", ["selectProvider", "pickAProviderTitle", "noAccountUsage", "use new account", "placeholder v2", "title", "currentAccount", "noAccounts", "manageAccountPreferenceForExtension", "accounts"]], ["vs/workbench/contrib/authentication/browser/actions/manageTrustedExtensionsForAccountAction", ["pickAccount", "noTrustedExtensions", "trustedExtensions", "accountLastUsedDate", "notUsed", "trustedExtensionTooltip", "accountPreferences", "manageTrustedExtensions.cancel", "manageTrustedExtensions", "manageExtensions", "manageTrustedExtensionsForAccount", "accounts"]], ["vs/workbench/contrib/authentication/browser/actions/signOutOfAccountAction", ["signOutOfAccount", "signOutMessage", "signOutMessageSimple", "signOut"]], ["vs/workbench/contrib/authentication/browser/authentication.contribution", ["authenticationlabel", "authenticationid", "authentication", "authentication.Placeholder", "loading"]], ["vs/workbench/contrib/bulkEdit/browser/bulkEditService", ["summary.0", "summary.nm", "summary.n0", "summary.textFiles", "workspaceEdit", "workspaceEdit", "nothing", "closeTheWindow.message", "closeTheWindow", "changeWorkspace.message", "changeWorkspace", "reloadTheWindow.message", "reloadTheWindow", "quit.message", "quit", "areYouSureQuiteBulkEdit.detail", "fileOperation", "refactoring.autoSave"]], ["vs/workbench/contrib/bulkEdit/browser/preview/bulkEdit.contribution", ["overlap", "detail", "continue", "refactorPreviewViewIcon", "apply", "cat", "Discard", "cat", "toogleSelection", "cat", "groupByFile", "cat", "groupByType", "cat", "groupByType", "cat", "panel", "panel"]], ["vs/workbench/contrib/bulkEdit/browser/preview/bulkEditPane", ["ok", "cancel", "empty.msg", "conflict.1", "conflict.N"]], ["vs/workbench/contrib/bulkEdit/browser/preview/bulkEditPreview", ["default"]], ["vs/workbench/contrib/bulkEdit/browser/preview/bulkEditTree", ["bulkEdit", "aria.renameAndEdit", "aria.createAndEdit", "aria.deleteAndEdit", "aria.editOnly", "aria.rename", "aria.create", "aria.delete", "aria.replace", "aria.del", "aria.insert", "rename.label", "detail.rename", "detail.create", "detail.del", "title"]], ["vs/workbench/contrib/callHierarchy/browser/callHierarchy.contribution", ["editorHasCallHierarchyProvider", "callHierarchyVisible", "callHierarchyDirection", "no.item", "error", "showIncomingCallsIcons", "showOutgoingCallsIcon", "close", "title", "title.incoming", "title.outgoing", "title.refocus"]], ["vs/workbench/contrib/callHierarchy/browser/callHierarchyPeek", ["callFrom", "callsTo", "title.loading", "empt.callsFrom", "empt.callsTo"]], ["vs/workbench/contrib/callHierarchy/browser/callHierarchyTree", ["tree.aria", "from", "to"]], ["vs/workbench/contrib/chat/browser/actions/chatAccessibilityHelp", ["chat.overview", "chat.differenceQuick", "chat.differencePanel", "chat.followUp", "chat.requestHistory", "chat.inspectResponse", "chat.announcement", "workbench.action.chat.focus", "workbench.action.chat.focusInput", "workbench.action.chat.nextCodeBlock", "workbench.action.chat.newChat", "chatAgent.overview", "chatEditing.overview", "chatEditing.format", "chatEditing.expectation", "chatEditing.review", "chatEditing.sections", "chatEditing.acceptHunk", "chatEditing.undoKeepSounds", "chatAgent.userActionRequired", "chatAgent.runCommand", "chatEditing.helpfulCommands", "workbench.action.chat.undoEdits", "workbench.action.chat.editing.attachFiles", "chatEditing.removeFileFromWorkingSet", "chatEditing.acceptFile", "chatEditing.saveAllFiles", "chatEditing.acceptAllFiles", "chatEditing.discardAllFiles", "chatEditing.openFileInDiff", "chatEditing.viewChanges", "inlineChat.overview", "inlineChat.access", "inlineChat.requestHistory", "inlineChat.inspectResponse", "inlineChat.contextActions", "inlineChat.fix", "inlineChat.diff", "inlineChat.toolbar", "chat.signals"]], ["vs/workbench/contrib/chat/browser/actions/chatActions", ["switchChat.confirmPhrase", "interactiveSession.history.editor", "interactiveSession.history.delete", "chat.history.rename", "currentChatLabel", "interactiveSession.history.pick", "newChatTitle", "upgradeChat", "chatQuotaExceeded", "completionsQuotaExceeded", "chatAndCompletionsQuotaExceeded", "quotaResetDate", "upgradeToPro", "copilo<PERSON><PERSON><PERSON><PERSON>Reached", "dismiss", "upgradePro", "upgradePlan", "title4", "title4", "toggle.chatControl", "toggle.chatControlsDescription", "resetTrustedToolsSuccess", "more", "toggleChat", "signInToChatSetup", "chatQuotaExceededButton", "chat.startEditing.confirmation.pending.message.default", "chat.startEditing.confirmation.title", "chat.startEditing.confirmation.pending.message.2", "chat.startEditing.confirmation.acceptEdits", "chat.startEditing.confirmation.discardEdits", "chat.category", "openChat", "openChatMode", "toggleChat", "chat.history.label", "interactiveSession.open", "chatWith", "interactiveSession.clearHistory.label", "chat.clear.label", "actions.interactiveSession.focus", "interactiveSession.focusInput.label", "manageCopilot", "showCopilotUsageExtensions", "configureCompletions", "resetTrustedTools"]], ["vs/workbench/contrib/chat/browser/actions/chatClearActions", ["chat.newChat.label", "chat.newEdits.label", "chat.undoEdit.label", "chat.redoEdit.label"]], ["vs/workbench/contrib/chat/browser/actions/chatCodeblockActions", ["interactive.applyInEditorWithURL.label", "interactive.copyCodeBlock.label", "interactive.applyInEditor.label", "interactive.insertCodeBlock.label", "interactive.insertIntoNewFile.label", "interactive.runInTerminal.label", "interactive.nextCodeBlock.label", "interactive.previousCodeBlock.label", "interactive.compare.apply", "interactive.compare.discard"]], ["vs/workbench/contrib/chat/browser/actions/chatContextActions", ["relatedFiles", "pastedImage", "pastedImage", "imageFromClipboard", "chatContext.attachScreenshot.labelElectron.Window", "chatContext.attachScreenshot.labelWeb", "chatContext.tools", "chatContext.symbol", "chatContext.folder", "chatContext.diagnstic", "chatContext.notebook.kernelVariable", "chatContext.notebook.selectkernelVariable", "chatContext.relatedFiles", "chatContext.editors", "chatContext.searchResults", "chatContext.attach.instructions.label", "chatContext.attach.placeholder", "markers.panel.at.ln.col.number", "markers.panel.allErrors", "pickAProblem", "chatContext.tools.internal", "chatContext.tools.mcp", "chatContext.tools.extension", "chatContext.tools.placeholder", "workbench.action.chat.attachFile.label", "workbench.action.chat.attachFolder.label", "workbench.action.chat.attachSelection.label", "chat.insertSearchResults", "workbench.action.chat.attachContext.label.2"]], ["vs/workbench/contrib/chat/browser/actions/chatCopyActions", ["interactive.copyAll.label", "interactive.copyItem.label"]], ["vs/workbench/contrib/chat/browser/actions/chatDeveloperActions", ["workbench.action.chat.logInputHistory.label", "workbench.action.chat.logChatIndex.label"]], ["vs/workbench/contrib/chat/browser/actions/chatExecuteActions", ["setChatMode", "switchMode.confirmPhrase", "agent.newSession", "agent.newSessionMessage", "agent.newSession.confirm", "requestIsPaused", "requestNotPaused", "interactive.submit.label", "interactive.toggleAgent.label", "interactive.toggleRequestPausd.label", "interactive.switchToNextModel.label", "interactive.openModelPicker.label", "interactive.changeModel.label", "edits.submit.label", "interactive.submitWithoutDispatch.label", "actions.chat.submitWithCodebase", "chat.newChat.label", "interactive.cancel.label"]], ["vs/workbench/contrib/chat/browser/actions/chatFileTreeActions", ["interactive.nextFileTree.label", "interactive.previousFileTree.label"]], ["vs/workbench/contrib/chat/browser/actions/chatImportExport", ["chat.file.label", "chat.export.label", "chat.import.label"]], ["vs/workbench/contrib/chat/browser/actions/chatMoveActions", ["chat.openInEditor.label", "chat.openInNewWindow.label", "interactiveSession.openInSidebar.label"]], ["vs/workbench/contrib/chat/browser/actions/chatQuickInputActions", ["toggle.desc", "toggle.query", "toggle.isPartial<PERSON>uery", "toggle.query", "chat.openInChatView.label", "chat.closeQuickChat.label", "quickChat", "interactiveSession.open"]], ["vs/workbench/contrib/chat/browser/actions/chatTitleActions", ["chat.retryLast.confirmation.title2", "chat.retry.confirmation.message2", "chat.retryLast.confirmation.message2", "chat.retry.confirmation.primaryB<PERSON>on", "chat.retry.confirmation.checkbox", "interactive.helpful.label", "interactive.unhelpful.label", "interactive.reportIssueForBug.label", "chat.retry.label", "interactive.insertIntoNotebook.label"]], ["vs/workbench/contrib/chat/browser/actions/chatToolActions", ["label", "addServer", "addExtension", "addAny", "noTools", "defaultBucketLabel", "configMcpCol", "mcpShowOutput", "mcplabel", "mc<PERSON><PERSON>us", "placeholder", "noTools", "chat.accept"]], ["vs/workbench/contrib/chat/browser/actions/codeBlockOperations", ["insertCodeBlock.noActiveEditor", "insertCodeBlock.readonlyNotebook", "insertCodeBlock.readonly", "applyCodeBlock.errorOpeningFile", "applyCodeBlock.noActiveEditor", "activeEditor", "newUntitledFile", "createFile", "selectOption", "applyCodeBlock.fileWriteError", "applyCodeBlock.readonlyNotebook", "applyCodeBlock.readonly", "applyCodeBlock.noCodeMapper", "applyCodeBlock.progress", "applyCodeBlock.error"]], ["vs/workbench/contrib/chat/browser/actions/promptActions/chatAttachInstructionsAction", ["commands.instructions.select-dialog.placeholder", "attach-instructions.capitalized.ellipses"]], ["vs/workbench/contrib/chat/browser/actions/promptActions/chatRunPromptAction", ["commands.prompt.select-dialog.placeholder", "run-prompt.capitalized", "run-prompt.capitalized.ellipses", "run-prompt-in-new-chat.capitalized"]], ["vs/workbench/contrib/chat/browser/actions/promptActions/chatSaveToPromptAction", ["workbench.actions.save-to-prompt.label"]], ["vs/workbench/contrib/chat/browser/actions/promptActions/dialogs/askToSelectPrompt/promptFilePickers", ["help", "commands.new-promptfile.select-dialog.label", "commands.new-instructionsfile.select-dialog.label", "commands.prompts.use.select-dialog.open-button.tooltip", "delete", "user-data-dir.capitalized", "commands.prompts.use.select-dialog.delete-prompt.confirm.message"]], ["vs/workbench/contrib/chat/browser/attachments/implicitContextAttachment", ["file.lowercase", "prompt.lowercase", "chat.fileAttachmentWithRange", "chat.fileAttachment", "openEditor", "enableHint", "hint.label.current", "disable", "enable"]], ["vs/workbench/contrib/chat/browser/attachments/promptInstructions/promptInstructionsWidget", ["chat.promptAttachment", "chat.instructionsAttachment", "prompt", "instructions", "error", "warning", "remove"]], ["vs/workbench/contrib/chat/browser/chat.contribution", ["interactiveSessionConfigurationTitle", "interactiveSession.editor.fontSize", "interactiveSession.editor.fontFamily", "interactiveSession.editor.fontWeight", "interactiveSession.editor.wordWrap", "interactiveSession.editor.lineHeight", "chat.commandCenter.enabled", "chat.implicitContext.enabled.1", "chat.implicitContext.value", "chat.implicitContext.value.never", "chat.implicitContext.value.first", "chat.implicitContext.value.always", "chat.editing.autoAcceptDelay", "chat.editing.confirmEditRequestRemoval", "chat.editing.confirmEditRequestRetry", "chat.experimental.detectParticipant.enabled.deprecated", "chat.experimental.detectParticipant.enabled", "chat.detectParticipant.enabled", "chat.renderRelatedFiles", "chat.focusWindowOnConfirmation", "chat.tools.autoApprove", "chat.sendElementsToChat.enabled", "chat.sendElementsToChat.attachCSS", "chat.sendElementsToChat.attachImages", "chat.mcp.enabled", "workspaceConfig.mcp.description", "chat.useFileStorage", "chat.edits2Enabled", "chat.extensionToolsEnabled", "chat.extensionToolsPolicy", "chat.agent.enabled.description", "mcp.discovery.source", "mpc.discovery.enabled", "chat.reusablePrompts.config.enabled.title", "chat.reusablePrompts.config.enabled.description", "chat.promptFiles.policy", "chat.instructions.config.locations.title", "chat.instructions.config.locations.description", "chat.reusablePrompts.config.locations.title", "chat.reusablePrompts.config.locations.description", "chat", "chat", "interactiveSessionConfigurationTitle", "chat.agent.maxRequests", "clear", "save-chat-to-prompt-file", "file"]], ["vs/workbench/contrib/chat/browser/chatAccessibilityProvider", ["toolInvocationsHintKb", "toolInvocationsHint", "chat", "toolCompletedHint", "singleTableHint", "multiTableHint", "singleFileTreeHint", "multiFileTreeHint", "noCodeBlocksHint", "noCodeBlocks", "singleCodeBlockHint", "singleCodeBlock", "multiCodeBlockHint", "multiCodeBlock"]], ["vs/workbench/contrib/chat/browser/chatAgentHover", ["reservedName", "viewExtensionLabel"]], ["vs/workbench/contrib/chat/browser/chatAttachmentResolve", ["imageTooLarge", "imageTooLargeMessage"]], ["vs/workbench/contrib/chat/browser/chatAttachmentWidgets", ["chat.attachment.clearButton", "chat.fileAttachmentWithRange", "chat.fileAttachment", "chat.omitted<PERSON><PERSON><PERSON>ttachment", "chat.fileAttachmentHover", "chat.omittedImageAttachment", "chat.partiallyOmittedImageAttachment", "chat.imageAttachment", "chat.fileAttachmentHover", "chat.imageAttachmentWarning", "chat.attachment", "chat.attachment", "chat.NotebookImageAttachment", "chat.omittedNotebookImageAttachment", "chat.partiallyOmittedNotebookImageAttachment", "chat.elementAttachment", "chat.clickToViewContents"]], ["vs/workbench/contrib/chat/browser/chatContentParts/chatAgentCommandContentPart", ["rerun"]], ["vs/workbench/contrib/chat/browser/chatContentParts/chatAttachmentsContentPart", ["resource", "chat.notebookOutputOmittedImageAttachment", "chat.notebookOutputPartiallyOmittedImageAttachment", "chat.notebookOutputImageAttachment", "chat.notebookOutputOmittedFileAttachment", "chat.notebookOutputPartialFileAttachment", "chat.notebookOutputFileAttachment3", "chat.omittedFileAttachmentWithRange", "chat.omitted<PERSON><PERSON><PERSON>ttachment", "chat.partialFileAttachmentWithRange", "chat.partialFileAttachment", "chat.fileAttachmentWithRange3", "chat.fileAttachment3", "chat.omittedImageAttachment", "chat.partiallyOmittedImageAttachment", "chat.imageAttachment", "chat.elementAttachment", "chat.clickToViewContents", "chat.attachment", "chat.attachment3", "chat.imageAttachmentHover", "chat.fileAttachmentHover", "chat.imageAttachmentWarning"]], ["vs/workbench/contrib/chat/browser/chatContentParts/chatCodeCitationContentPart", ["viewMatches"]], ["vs/workbench/contrib/chat/browser/chatContentParts/chatCollapsibleContentPart", ["usedReferencesExpanded", "usedReferencesCollapsed"]], ["vs/workbench/contrib/chat/browser/chatContentParts/chatCommandContentPart", ["commandButtonDisabled"]], ["vs/workbench/contrib/chat/browser/chatContentParts/chatConfirmationContentPart", ["accept", "dismiss"]], ["vs/workbench/contrib/chat/browser/chatContentParts/chatExtensionsContentPart", ["chat.extensions.loading"]], ["vs/workbench/contrib/chat/browser/chatContentParts/chatMarkdownContentPart", ["chat.codeblock.generating", "chat.codeblock.insertions.one", "chat.codeblock.insertions", "chat.codeblock.deletions.one", "chat.codeblock.deletions", "summary", "chat.codeblock.generating", "chat.codeblock.applyingPercentage"]], ["vs/workbench/contrib/chat/browser/chatContentParts/chatProgressContentPart", ["pausedMessage", "workingMessage", "resume"]], ["vs/workbench/contrib/chat/browser/chatContentParts/chatQuotaExceededPart", ["enableAdditionalUsage", "upgradeToCopilotPro", "waitWarning", "clickToContinue"]], ["vs/workbench/contrib/chat/browser/chatContentParts/chatReferencesContentPart", ["usedReferencesPlural", "usedReferencesSingular", "chatCollapsibleList", "setting.hover", "addToChat", "copyLink"]], ["vs/workbench/contrib/chat/browser/chatContentParts/chatTextEditContentPart", ["edits0", "editsSummary"]], ["vs/workbench/contrib/chat/browser/chatContentParts/chatToolInputOutputContentPart", ["chat.input", "chat.output"]], ["vs/workbench/contrib/chat/browser/chatContentParts/chatToolInvocationPart", ["continue", "cancel", "allowSession", "allowSesssionTooltip", "allowWorkspace", "allowWorkspaceTooltip", "allowGlobally", "allowGloballTooltip", "cancel", "seeM<PERSON>", "continue", "cancel", "toolResultData"]], ["vs/workbench/contrib/chat/browser/chatContentParts/chatTreeContentPart", ["treeAriaLabel"]], ["vs/workbench/contrib/chat/browser/chatDragAndDrop", ["file", "file", "folder", "image", "symbol", "problem", "url", "notebookOutput", "dragAndDroppedImageName", "attacAsContext"]], ["vs/workbench/contrib/chat/browser/chatEditing/chatEditingActions", ["chat.editing.removeFile.confirmationmanyFiles", "chat.editing.removeFile.confirmation.title", "chat.editing.removeFile.confirmation.message", "chat.editing.removeFile.confirmation.primaryButton", "accept", "acceptAllEdits", "discard", "discardAllEdits", "chat.editing.discardAll.confirmation.title", "chat.editing.discardAll.confirmation.oneFile", "chat.editing.discardAll.confirmation.manyFiles", "chat.editing.discardAll.confirmation.primaryButton", "clearWorkingSet", "clearWorkingSet", "chatEditing.viewChanges", "chat.removeLast.confirmation.message2", "chat.removeLast.confirmation.multipleEdits.message", "chat.remove.confirmation.message2", "chat.remove.confirmation.multipleEdits.message", "chat.removeLast.confirmation.title", "chat.remove.confirmation.title", "chat.remove.confirmation.primary<PERSON><PERSON>on", "chat.remove.confirmation.checkbox", "chat.openFileUpdatedBySnapshot.label", "chat.openSnapshot.label", "chatEditing.snapshot", "chatEditing.viewPreviousEdits", "removeFileFromWorkingSet", "open.fileInDiff", "accept.file", "discard.file", "chat.undoEdits.label", "addFilesFromReferences"]], ["vs/workbench/contrib/chat/browser/chatEditing/chatEditingCodeEditorIntegration", ["diff.agent", "diff.generic"]], ["vs/workbench/contrib/chat/browser/chatEditing/chatEditingEditorActions", ["accept4", "discard4", "label", "next", "prev", "accept", "discard", "accept2", "discard2", "accept3", "discard3", "acceptHunk", "undo", "diff", "accessibleDiff", "review"]], ["vs/workbench/contrib/chat/browser/chatEditing/chatEditingEditorContextKeys", ["chat.ctxEditSessionIsGlobal", "chat.hasEditorModifications", "chat.ctxReviewModeEnabled", "chat.ctxHasRequestInProgress", "chatEdits.requestCount"]], ["vs/workbench/contrib/chat/browser/chatEditing/chatEditingEditorOverlay", ["working", "paused", "working", "nOfM", "0Of0", "tooltip_11", "tooltip_1n", "tooltip_n1", "tooltip_nm", "tooltip_busy"]], ["vs/workbench/contrib/chat/browser/chatEditing/chatEditingModifiedDocumentEntry", ["default", "chatEditing1", "chatEditing2"]], ["vs/workbench/contrib/chat/browser/chatEditing/chatEditingModifiedFileEntry", ["editorSelectionBackground"]], ["vs/workbench/contrib/chat/browser/chatEditing/chatEditingModifiedNotebookEntry", ["chatNotebookEdit1", "chatNotebookEdit2"]], ["vs/workbench/contrib/chat/browser/chatEditing/chatEditingServiceImpl", ["join.chatEditingSession", "chat", "chatEditing.modified", "chatEditing.modified2"]], ["vs/workbench/contrib/chat/browser/chatEditing/chatEditingSession", ["multiDiffEditorInput.name"]], ["vs/workbench/contrib/chat/browser/chatEditing/notebook/chatEditingNotebookEditorIntegration", ["diff.agent", "diff.generic"]], ["vs/workbench/contrib/chat/browser/chatEditing/simpleBrowserEditorOverlay", ["elementSelectionMessage", "selectAnElement", "startSelection", "cancelSelection", "cancel", "chat.configureElements", "chat.hideOverlay", "chat.nextSelection", "chat.expandOverlay", "elementSelectionInProgress", "elementSelectionComplete", "elementCancelMessage"]], ["vs/workbench/contrib/chat/browser/chatEditorInput", ["chatEditorLabelIcon", "chatEditorConfirmTitle", "chat.startEditing.confirmation.pending.message.default", "chatEditorName"]], ["vs/workbench/contrib/chat/browser/chatFollowups", ["followUpAriaLabel"]], ["vs/workbench/contrib/chat/browser/chatInlineAnchorWidget", ["miGotoDefinition", "miGotoTypeDefinition", "miGotoImplementations", "miGotoReference", "actions.attach.label", "actions.copy.label", "actions.openToSide.label", "actions.goToDecl.label", "goToTypeDefinitions.label", "goToImplementations.label", "goToReferences.label"]], ["vs/workbench/contrib/chat/browser/chatInputPart", ["actions.chat.accessibiltyHelp", "chatInput.accessibilityHelpNoKb", "chatInput", "notebook.moreExecuteActionsLabel", "chatAttachFiles", "chatEditingSession.oneFile.1", "chatEditingSession.manyFiles.1", "suggeste.title", "chatEditingSession.addSuggestion", "chatEditingSession.addSuggested"]], ["vs/workbench/contrib/chat/browser/chatList<PERSON><PERSON>er", ["usedAgentSlashCommand", "usedAgent", "paused", "working", "chatConfirmationAction", "incorrectCode", "didNotFollowInstructions", "missingContext", "offensiveOrUnsafe", "poorlyWrittenOrFormatted", "refusedAValidRequest", "incompleteCode", "reportIssue", "other"]], ["vs/workbench/contrib/chat/browser/chatParticipant.contribution", ["miToggleChat", "vscode.extension.contributes.chatParticipant", "chatParticipantId", "chatParticipantName", "chatParticipantFullName", "chatParticipantDescription", "chatCommandSticky", "chatSampleRequest", "chatParticipantWhen", "chatParticipantDisambiguation", "chatParticipantDisambiguationCategory", "chatParticipantDisambiguationDescription", "chatParticipantDisambiguationExamples", "chatCommandsDescription", "chatCommand", "chatCommandDescription", "chatCommandWhen", "chatCommandSampleRequest", "chatCommandSticky", "chatCommandDisambiguation", "chatCommandDisambiguationCategory", "chatCommandDisambiguationDescription", "chatCommandDisambiguationExamples", "showExtension", "chatFailErrorMessage", "participantName", "participantFullName", "participantDescription", "participantCommands", "chatParticipants", "chat.viewContainer.label", "chat.viewContainer.label"]], ["vs/workbench/contrib/chat/browser/chatPasteProviders", ["pastedImageName", "pastedImageAttachment", "pastedCodeAttachment", "pastedAttachment.oneLine", "pastedAttachment.multipleLines"]], ["vs/workbench/contrib/chat/browser/chatSelectedTools", ["tool.1", "tool.0"]], ["vs/workbench/contrib/chat/browser/chatSetup", ["settings", "chatDescription", "editsDescription", "agentDescription", "setupToolDisplayName", "setupToolsDescription", "setupToolsDescription", "settingUpCopilotNeeded", "waitingCopilot", "copilotUnavailableWarning", "waitingCopilot2", "copilotTookLongWarning", "copilotFailedWarning", "setupChatSignIn2", "installingCopilot", "copilotSetupSuccess", "copilotSetupError", "maybeLater", "setupWithProvider", "setupWithEnterpriseProvider", "signInButton", "useCopilotButton", "signUp", "signUpFree", "copilotProTitle", "copilotTitle", "copilotFreeTitle", "headerDialog", "terms", "setupErrorDialog", "retry", "setupCopilot", "hideChatSetupConfirm", "hideChatSetupDetail", "hideChatSetupButton", "setupChatProgress", "copilotWorkspaceTrust", "unknownSignInError", "unknownSignInErrorDetail", "retry", "unknownSetupError", "retry", "enterpriseInstance", "enterpriseInstancePlaceholder", "willResolveTo", "invalidEnterpriseInstance", "enterpriseSetupError", "retry", "vscodeAgentDescription", "triggerChatSetup", "triggerChatSetupFromAccounts", "hideChatSetup", "managePlan", "chat.category", "manageOverages", "chat.category"]], ["vs/workbench/contrib/chat/browser/chatStatus", ["gaugeBackground", "gaugeForeground", "gaugeBorder", "gaugeWarningBackground", "gaugeWarningForeground", "gaugeErrorBackground", "gaugeErrorForeground", "chatStatus", "notSignedIntoCopilot", "chatQuotaExceededStatus", "completionsQuotaExceededStatus", "chatAndCompletionsQuotaExceededStatus", "completionsDisabledStatus", "chatStatus", "usageTitle", "quota<PERSON><PERSON><PERSON>", "quotaTooltip", "completionsLabel", "chatsLabel", "premiumChatsLabel", "limit<PERSON>uo<PERSON>", "upgradeToCopilotPro", "settingsTitle", "settingsLabel", "settingsTooltip", "activateDescription", "signInDescription", "activateCopilotButton", "signInToUseCopilotButton", "learnMore", "learnMore", "enableAdditionalUsage", "quotaUnlimited", "quotaDisplayWithOverage", "quotaDisplay", "additionalUsageEnabled", "additionalUsageDisabled", "settings.codeCompletions", "settings.codeCompletionsLanguage", "settings.nextEditSuggestions"]], ["vs/workbench/contrib/chat/browser/chatWidget", ["scrollDownButtonLabel", "chatWidget.tips", "chatWidget.tips.withoutParticipants", "chatMessage", "chatDescription", "editsTitle", "editsMessage", "editsTitle", "agentMessage"]], ["vs/workbench/contrib/chat/browser/codeBlockPart", ["chat.codeBlockHelp", "chat.codeBlock.toolbarVerbose", "chat.codeBlock.toolbar", "chat.codeBlockLabel", "chat.codeBlockToolbarLabel", "vulnerabilitiesPlural", "vulnerabilitiesSingular", "chat.codeBlockHelp", "original", "modified", "chat.codeBlock.toolbar", "chat.compareCodeBlockLabel", "chat.edits.1", "chat.edits.rejected", "chat.edits.N", "interactive.compare.apply.confirm", "interactive.compare.apply.confirm.detail"]], ["vs/workbench/contrib/chat/browser/contrib/chatInputCompletions", ["promptFileDescription", "promptFileDescriptionWorkspace", "installLabel", "fileEntryDescription", "desc"]], ["vs/workbench/contrib/chat/browser/contrib/chatInputEditorHover", ["hoverAccessibilityChatAgent"]], ["vs/workbench/contrib/chat/browser/contrib/chatInputRelatedFilesContrib", ["relatedFile"]], ["vs/workbench/contrib/chat/browser/contrib/screenshot", ["screenshot"]], ["vs/workbench/contrib/chat/browser/modelPicker/modelPickerActionItem", ["chat.moreModels", "chat.moreModels.tooltip", "chat.modelPicker.label"]], ["vs/workbench/contrib/chat/browser/promptSyntax/contributions/attachInstructionsCommand", ["attach-instructions.capitalized.ellipses"]], ["vs/workbench/contrib/chat/browser/promptSyntax/contributions/createPromptCommand/createPromptCommand", ["workbench.command.instructions.create.location.placeholder", "workbench.command.prompt.create.location.placeholder", "workbench.command.prompts.create.user.enable-sync-notification", "enable.capitalized", "learnMore.capitalized", "commands.new.instructions.local.title", "commands.new.prompt.local.title"]], ["vs/workbench/contrib/chat/browser/promptSyntax/contributions/createPromptCommand/dialogs/askForPromptName", ["askForInstructionsFileName.placeholder", "askForPromptFileName.placeholder", "askForPromptFileName.error.empty", "askForPromptFileName.error.invalid", "askForPromptFileName.error.exists"]], ["vs/workbench/contrib/chat/browser/promptSyntax/contributions/createPromptCommand/dialogs/askForPromptSourceFolder", ["commands.prompts.create.source-folder.user", "commands.prompts.create.source-folder.current-workspace", "commands.prompts.create.ask-folder.empty.docs-label", "commands.prompts.create.ask-folder.empty.placeholder"]], ["vs/workbench/contrib/chat/browser/promptSyntax/contributions/createPromptCommand/errors", ["workbench.command.prompts.create.error.folder-exists", "workbench.command.prompts.create.error.invalid-prompt-name"]], ["vs/workbench/contrib/chat/browser/viewsWelcome/chatViewsWelcomeHandler", ["vscode.extension.contributes.chatViewsWelcome", "chatViewsWelcome.icon", "chatViewsWelcome.title", "chatViewsWelcome.content", "chatViewsWelcome.when"]], ["vs/workbench/contrib/chat/browser/viewsWelcome/chatViewWelcomeController", ["agentModeSubtitle"]], ["vs/workbench/contrib/chat/common/chatColors", ["chat.requestBorder", "chat.requestBackground", "chat.slashCommandBackground", "chat.slashCommandForeground", "chat.avatarBackground", "chat.avatarForeground", "chat.editedFileForeground"]], ["vs/workbench/contrib/chat/common/chatContextKeys", ["interactiveSessionResponseVote", "chatSessionResponseDetectedAgentOrCommand", "chatResponseSupportsIssueReporting", "chatResponseFiltered", "chatResponseErrored", "interactiveSessionRequestInProgress", "chatRequestIsPaused", "chatCanRequestBePaused", "chatResponse", "chatRequest", "chatItemId", "chatLastItemId", "chatEditApplied", "interactiveInputHasText", "interactiveInputHasFocus", "inInteractiveInput", "inChat", "chatPromptFileAttachedContextDescription", "chatMode", "chatIsEnabled", "chatPanelExtensionParticipantRegistered", "chatParticipantRegistered", "chatEditingParticipantRegistered", "chatEditingCanUndo", "chatEditingCanRedo", "chatExtensionInvalid", "inQuickChat", "chatHasFileAttachments", "chatModelsAreUserSelectable", "chatAgentModeDisallowed", "chatEditingHasToolConfirmation", "toolsCount"]], ["vs/workbench/contrib/chat/common/chatEditingService", ["chatEditingWidgetFileState", "chatEditingAgentSupportsReadonlyReferences"]], ["vs/workbench/contrib/chat/common/chatEntitlementService", ["signUpNoResponseError", "signUpUnexpectedStatusError", "signUpNoResponseContentsError", "signUpInvalidResponseError", "unknownSignUpError", "retry", "unprocessableSignUpError", "ok", "learnMore"]], ["vs/workbench/contrib/chat/common/chatModel", ["chat.attachment.problems.all", "chat.attachment.problems.inFile", "editsSummary", "codeCitation", "codeCitations"]], ["vs/workbench/contrib/chat/common/chatProgressTypes/chatToolInvocation", ["toolInvocationMessage"]], ["vs/workbench/contrib/chat/common/chatServiceImpl", ["newChat", "newChat", "emptyResponse"]], ["vs/workbench/contrib/chat/common/chatSessionStore", ["join.chatSessionStore", "newChat"]], ["vs/workbench/contrib/chat/common/languageModels", ["vscode.extension.contributes.languageModels.vendor", "vscode.extension.contributes.languageModels", "vscode.extension.contributes.languageModels.chatProviderRequired", "vscode.extension.contributes.languageModels.vendorAlreadyRegistered", "vscode.extension.contributes.languageModels.emptyVendor", "vscode.extension.contributes.languageModels.whitespaceVendor"]], ["vs/workbench/contrib/chat/common/languageModelStats", ["Language Models", "languageModels", "chat"]], ["vs/workbench/contrib/chat/common/modelPicker/modelPickerWidget", ["chat.modelPicker.other"]], ["vs/workbench/contrib/chat/common/promptSyntax/contributions/languageFeatures/providers/decorationsProvider/decorations/frontMatterDecoration", ["chat.prompt.frontMatter.background.description", "chat.prompt.frontMatter.inactiveBackground.description"]], ["vs/workbench/contrib/chat/common/promptSyntax/parsers/promptHeader/header", ["prompt.header.diagnostics.unexpected-token", "prompt.header.metadata.diagnostics.duplicate-record", "prompt.header.metadata.diagnostics.unknown-record", "prompt.header.metadata.mode.diagnostics.incompatible-with-tools", "prompt.header.diagnostics.parsing-error"]], ["vs/workbench/contrib/chat/common/promptSyntax/parsers/promptHeader/metadata/applyTo", ["prompt.header.metadata.string.diagnostics.invalid-language", "prompt.header.metadata.applyTo.diagnostics.non-valid-glob"]], ["vs/workbench/contrib/chat/common/promptSyntax/parsers/promptHeader/metadata/mode", ["prompt.header.metadata.mode.diagnostics.invalid-value"]], ["vs/workbench/contrib/chat/common/promptSyntax/parsers/promptHeader/metadata/record", ["prompt.header.metadata.string.diagnostics.invalid-value-type"]], ["vs/workbench/contrib/chat/common/promptSyntax/parsers/promptHeader/metadata/tools", ["prompt.header.metadata.tools.diagnostics.invalid-value-type", "prompt.header.metadata.tools.diagnostics.invalid-tool-name-type", "prompt.header.metadata.tools.diagnostics.empty-tool-name", "prompt.header.metadata.tools.diagnostics.duplicate-tool-name"]], ["vs/workbench/contrib/chat/common/promptSyntax/parsers/topError", ["workbench.reusable-prompts.top-error.more-issues-label", "workbench.reusable-prompts.top-error.open-failed", "workbench.reusable-prompts.top-error.cannot-read", "workbench.reusable-prompts.top-error.recursive-reference", "workbench.reusable-prompts.top-error.child.direct", "workbench.reusable-prompts.top-error.child.indirect", "recursive", "broken", "workbench.reusable-prompts.top-error.child.final-message"]], ["vs/workbench/contrib/chat/common/promptSyntax/service/promptsService", ["prompt.file.detail", "prompt.file.detail"]], ["vs/workbench/contrib/chat/common/tools/languageModelToolsContribution", ["vscode.extension.contributes.tools", "toolName", "toolName2", "toolDisplayName", "toolUserDescription", "toolModelDescription", "parametersSchema", "canBeReferencedInPrompt", "icon", "icon.light", "icon.dark", "condition", "toolTags", "toolTableName", "toolTableDisplayName", "toolTableDescription", "langModelTools"]], ["vs/workbench/contrib/chat/common/voiceChatService", ["voiceChatInProgress"]], ["vs/workbench/contrib/chat/electron-sandbox/actions/chatDeveloperActions", ["workbench.action.chat.openStorageFolder.label"]], ["vs/workbench/contrib/chat/electron-sandbox/actions/voiceChatActions", ["scopedVoiceChatGettingReady", "scopedVoiceChatInProgress", "listening", "scopedChatSynthesisInProgress", "voice.keywordActivation.off", "voice.keywordActivation.chatInView", "voice.keywordActivation.quickChat", "voice.keywordActivation.inlineChat", "voice.keywordActivation.chatInContext", "voice.keywordActivation", "keywordActivation.status.name", "keywordActivation.status.active", "keywordActivation.status.inactive", "unknownSetupError", "retry", "installProviderForVoiceChat.justification", "workbench.action.chat.voiceChatInView.label", "workbench.action.chat.holdToVoiceChatInChatView.label", "workbench.action.chat.inlineVoiceChat", "workbench.action.chat.quickVoiceChat.label", "workbench.action.chat.startVoiceChat.label", "workbench.action.chat.stopListening.label", "workbench.action.chat.stopListeningAndSubmit.label", "workbench.action.chat.readChatResponseAloud", "workbench.action.speech.stopReadAloud", "workbench.action.chat.stopReadChatItemAloud", "workbench.action.chat.installProviderForVoiceChat.label"]], ["vs/workbench/contrib/chat/electron-sandbox/tools/fetchPageTool", ["fetchWebPage.modelDescription", "fetchWebPage.urlsDescription", "fetchWebPage.noValidUrls", "fetchWebPage.pastTenseMessage.plural", "fetchWebPage.pastTenseMessage.singular", "fetchWebPage.pastTenseMessageResult.plural", "fetchWebPage.invocationMessage.plural", "fetchWebPage.pastTenseMessageResult.singularAsLink", "fetchWebPage.invocationMessage.singularAsLink", "fetchWebPage.pastTenseMessageResult.singular", "fetchWebPage.invocationMessage.singular", "fetchWebPage.confirmationTitle.singular", "fetchWebPage.confirmationTitle.plural", "fetchWebPage.invalidUrl"]], ["vs/workbench/contrib/codeActions/browser/codeActionsContribution", ["alwaysSave", "explicitSave", "neverSave", "explicitSaveBoolean", "neverSaveBoolean", "explicit", "never", "explicitBoolean", "neverBoolean", "editor.codeActionsOnSave", "notebook.codeActionsOnSave", "codeActionsOnSave.generic", "codeActionsOnSave.generic"]], ["vs/workbench/contrib/codeEditor/browser/accessibility/accessibility", ["toggleScreenReaderMode", "toggleScreenReaderModeDescription"]], ["vs/workbench/contrib/codeEditor/browser/dictation/editorDictation", ["stopDictationShort1", "stopDictationShort2", "voiceCategory", "startDictation", "stopDictation"]], ["vs/workbench/contrib/codeEditor/browser/diffEditorAccessibilityHelp", ["msg3", "msg5", "msg1", "msg2", "msg4"]], ["vs/workbench/contrib/codeEditor/browser/diffEditorHelper", ["hintWhitespace", "hintTimeout", "removeTimeout"]], ["vs/workbench/contrib/codeEditor/browser/emptyTextEditorHint/emptyTextEditorHint", ["disableEditorEmptyHint", "disableEditorEmptyHint", "emptyTextEditorHintWithInlineChat", "emptyTextEditorHintWithoutInlineChat", "defaultHintAriaLabelWithInlineChat", "defaultHintAriaLabelWithoutInlineChat", "disableHint"]], ["vs/workbench/contrib/codeEditor/browser/find/simpleFindWidget", ["label.find", "placeholder.find", "label.previousMatchButton", "label.nextMatchButton", "label.closeButton", "ariaSearchNoInput", "ariaSearchNoResultEmpty", "ariaSearchNoResult", "ariaSearchNoResultWithLineNumNoCurrentMatch", "simpleFindWidget.sashBorder"]], ["vs/workbench/contrib/codeEditor/browser/inspectEditorTokens/inspectEditorTokens", ["inspectTMScopesWidget.loading", "inspectEditorTokens"]], ["vs/workbench/contrib/codeEditor/browser/inspectKeybindings", ["workbench.action.inspectKeyMap", "workbench.action.inspectKeyMapJSON"]], ["vs/workbench/contrib/codeEditor/browser/largeFileOptimizations", ["largeFile", "removeOptimizations", "reopenFilePrompt"]], ["vs/workbench/contrib/codeEditor/browser/outline/documentSymbolsOutline", ["document"]], ["vs/workbench/contrib/codeEditor/browser/outline/documentSymbolsTree", ["title.template", "1.problem", "N.problem", "deep.problem"]], ["vs/workbench/contrib/codeEditor/browser/quickaccess/gotoLineQuickAccess", ["gotoLineQuickAccessPlaceholder", "gotoLineQuickAccess", "gotoLine"]], ["vs/workbench/contrib/codeEditor/browser/quickaccess/gotoSymbolQuickAccess", ["empty", "miGotoSymbolInEditor", "gotoSymbolQuickAccessPlaceholder", "gotoSymbolQuickAccess", "gotoSymbolByCategoryQuickAccess", "gotoSymbol"]], ["vs/workbench/contrib/codeEditor/browser/saveParticipants", ["formatting2", "codeaction", "codeaction.get2", "codeAction.apply"]], ["vs/workbench/contrib/codeEditor/browser/toggleColumnSelection", ["miColumnSelection", "toggleColumnSelection"]], ["vs/workbench/contrib/codeEditor/browser/toggleMinimap", ["miMinimap", "toggleMinimap"]], ["vs/workbench/contrib/codeEditor/browser/toggleMultiCursorModifier", ["miMultiCursorAlt", "miMultiCursorCmd", "miMultiCursorCtrl", "toggleLocation"]], ["vs/workbench/contrib/codeEditor/browser/toggleOvertype", ["mitoggleOvertypeInsertMode", "toggleOvertypeInsertMode", "toggleOvertypeMode.description"]], ["vs/workbench/contrib/codeEditor/browser/toggleRenderControlCharacter", ["miToggleRenderControlCharacters", "toggleRenderControlCharacters"]], ["vs/workbench/contrib/codeEditor/browser/toggleRenderWhitespace", ["miToggleRenderWhitespace", "toggleRenderWhitespace"]], ["vs/workbench/contrib/codeEditor/browser/toggleWordWrap", ["editor<PERSON><PERSON>W<PERSON>", "unwrapMinified", "wrapMinified", "miToggleWordWrap", "toggle.wordwrap"]], ["vs/workbench/contrib/codeEditor/common/languageConfigurationExtensionPoint", ["parseErrors", "formatError", "schema.openBracket", "schema.closeBracket", "schema.comments", "schema.blockComments", "schema.blockComment.begin", "schema.blockComment.end", "schema.lineComment", "schema.brackets", "schema.colorizedBracketPairs", "schema.autoClosingPairs", "schema.autoClosingPairs.notIn", "schema.autoCloseBefore", "schema.surroundingPairs", "schema.wordPattern", "schema.wordPattern.pattern", "schema.wordPattern.flags", "schema.wordPattern.flags.errorMessage", "schema.indentationRules", "schema.indentationRules.increaseIndentPattern", "schema.indentationRules.increaseIndentPattern.pattern", "schema.indentationRules.increaseIndentPattern.flags", "schema.indentationRules.increaseIndentPattern.errorMessage", "schema.indentationRules.decreaseIndentPattern", "schema.indentationRules.decreaseIndentPattern.pattern", "schema.indentationRules.decreaseIndentPattern.flags", "schema.indentationRules.decreaseIndentPattern.errorMessage", "schema.indentationRules.indentNextLinePattern", "schema.indentationRules.indentNextLinePattern.pattern", "schema.indentationRules.indentNextLinePattern.flags", "schema.indentationRules.indentNextLinePattern.errorMessage", "schema.indentationRules.unIndentedLinePattern", "schema.indentationRules.unIndentedLinePattern.pattern", "schema.indentationRules.unIndentedLinePattern.flags", "schema.indentationRules.unIndentedLinePattern.errorMessage", "schema.folding", "schema.folding.offSide", "schema.folding.markers", "schema.folding.markers.start", "schema.folding.markers.end", "schema.onEnterRules", "schema.onEnterRules", "schema.onEnterRules.beforeText", "schema.onEnterRules.beforeText.pattern", "schema.onEnterRules.beforeText.flags", "schema.onEnterRules.beforeText.errorMessage", "schema.onEnterRules.afterText", "schema.onEnterRules.afterText.pattern", "schema.onEnterRules.afterText.flags", "schema.onEnterRules.afterText.errorMessage", "schema.onEnterRules.previousLineText", "schema.onEnterRules.previousLineText.pattern", "schema.onEnterRules.previousLineText.flags", "schema.onEnterRules.previousLineText.errorMessage", "schema.onEnterRules.action", "schema.onEnterRules.action.indent", "schema.onEnterRules.action.indent.none", "schema.onEnterRules.action.indent.indent", "schema.onEnterRules.action.indent.indentOutdent", "schema.onEnterRules.action.indent.outdent", "schema.onEnterRules.action.appendText", "schema.onEnterRules.action.removeText"]], ["vs/workbench/contrib/codeEditor/electron-sandbox/selectionClipboard", ["actions.pasteSelectionClipboard"]], ["vs/workbench/contrib/codeEditor/electron-sandbox/startDebugTextMate", ["startDebugTextMate"]], ["vs/workbench/contrib/commands/common/commands.contribution", ["runCommands.description", "runCommands.commands", "runCommands.invalidArgs", "runCommands.noCommandsToRun", "runCommands"]], ["vs/workbench/contrib/comments/browser/commentColors", ["resolvedCommentIcon", "unresolvedCommentIcon", "commentReplyInputBackground", "resolvedCommentBorder", "unresolvedCommentBorder", "commentThreadRangeBackground", "commentThreadActiveRangeBackground"]], ["vs/workbench/contrib/comments/browser/commentGlyphWidget", ["editorGutterCommentRangeForeground", "editorOverviewRuler.commentForeground", "editorOverviewRuler.commentUnresolvedForeground", "editorGutterCommentGlyphForeground", "editorGutterCommentUnresolvedGlyphForeground"]], ["vs/workbench/contrib/comments/browser/commentNode", ["commentToggleReaction", "commentToggleReactionError", "commentToggleReactionDefaultError", "commentDeleteReactionError", "commentDeleteReactionDefaultError", "commentAddReactionError", "commentAddReactionDefaultError"]], ["vs/workbench/contrib/comments/browser/commentReply", ["reply", "newComment", "reply", "reply"]], ["vs/workbench/contrib/comments/browser/comments.contribution", ["collapseAll", "expandAll", "reply", "commentsConfigurationTitle", "openComments", "comments.openPanel.deprecated", "comments.openView.never", "comments.openView.file", "comments.openView.firstFile", "comments.openView.firstFileUnresolved", "comments.openView", "useRelativeTime", "comments.visible", "comments.maxHeight", "collapseOnResolve", "confirmOnCollapse.whenHasUnsubmittedComments", "confirmOnCollapse.never", "confirmOnCollapse", "totalUnresolvedComments"]], ["vs/workbench/contrib/comments/browser/commentsAccessibility", ["intro", "introWidget", "commentCommands", "escape", "next", "previous", "nextCommentThreadKb", "previousCommentThreadKb", "nextCommentedRangeKb", "previousCommentedRangeKb", "addCommentNoKb", "submitComment"]], ["vs/workbench/contrib/comments/browser/commentsController", ["commentRange", "commentRangeStart", "hasCommentRangesKb", "hasCommentRangesNoKb", "hasCommentRanges", "comments.addCommand.error", "comments.addFileCommentCommand.error", "pickCommentService"]], ["vs/workbench/contrib/comments/browser/commentsEditorContribution", ["comments.NextCommentedRange", "commentsCategory", "comments.previousCommentedRange", "commentsCategory", "comments.nextCom<PERSON>ingRange", "commentsCategory", "comments.previousC<PERSON><PERSON>ingRange", "commentsCategory", "comments.toggleCommenting", "commentsCategory", "comments.addCommand", "commentsCategory", "comments.focusCommentOnCurrentLine", "commentsCategory", "comments.focusCommand.error", "comments.collapseAll", "commentsCategory", "comments.expandAll", "commentsCategory", "comments.expandUnresolved", "commentsCategory", "comments.focusCommand.error"]], ["vs/workbench/contrib/comments/browser/commentsModel", ["noComments"]], ["vs/workbench/contrib/comments/browser/commentsTreeViewer", ["commentsCountReplies", "commentsCountReply", "commentCount", "imageWithLabel", "image", "outdated", "commentLine", "commentRange", "lastReplyFrom", "comments.view.title"]], ["vs/workbench/contrib/comments/browser/commentsView", ["comments.filter.placeholder", "comments.filter.ariaLabel", "showing filtered results", "fileCommentLabel", "oneLineCommentLabel", "multiLineCommentLabel", "accessibleViewHint", "acessibleViewHintNoKbOpen", "resourceWithCommentLabelOutdated", "resourceWithCommentLabel", "resourceWithRepliesLabel", "replyCount", "rootCommentsLabel", "resourceWithCommentThreadsLabel"]], ["vs/workbench/contrib/comments/browser/commentsViewActions", ["focusCommentsList", "commentsClearFilterText", "focusCommentsFilter", "toggle unresolved", "comments", "unresolved", "toggle resolved", "comments", "resolved", "comment sorts", "toggle sorting by updated at", "comments", "sorting by updated at", "toggle sorting by resource", "comments", "sorting by position in file"]], ["vs/workbench/contrib/comments/browser/commentThreadBody", ["commentThreadAria.withRange", "commentThreadAria.document", "commentThreadAria"]], ["vs/workbench/contrib/comments/browser/commentThreadHeader", ["collapseIcon", "label.collapse", "startThread"]], ["vs/workbench/contrib/comments/browser/commentThreadWidget", ["comment<PERSON>abel", "commentLabelWithKeybinding", "commentLabelWithKeybindingNoKeybinding"]], ["vs/workbench/contrib/comments/browser/commentThreadZoneWidget", ["confirmCollapse", "discard", "neverAskAgain"]], ["vs/workbench/contrib/comments/browser/reactionsAction", ["pickReactions", "comment.toggleableReaction", "comment.reactionLabelNone", "comment.reactionLabelOne", "comment.reactionLabelMany", "comment.reactionLessThanTen", "comment.reactionMoreThanTen"]], ["vs/workbench/contrib/comments/common/commentContextKeys", ["hasCommentingRange", "hasComment", "editorHasCommentingRange", "hasCommentingProvider", "commentThreadIsEmpty", "commentIsEmpty", "comment", "commentThread", "commentController", "commentFocused", "commentingEnabled"]], ["vs/workbench/contrib/customEditor/browser/customEditorInput", ["editorUnsupportedInWindow", "reopenInOriginalWindow", "editor<PERSON><PERSON><PERSON><PERSON><PERSON>"]], ["vs/workbench/contrib/customEditor/common/contributedCustomEditors", ["builtinProviderDisplayName"]], ["vs/workbench/contrib/customEditor/common/customEditor", ["context.customEditor"]], ["vs/workbench/contrib/customEditor/common/customTextEditorModel", ["vetoExtHostRestart"]], ["vs/workbench/contrib/customEditor/common/extensionPoint", ["contributes.customEditors", "contributes.viewType", "contributes.displayName", "contributes.selector", "contributes.selector.filenamePattern", "contributes.priority", "contributes.priority.default", "contributes.priority.option", "customEditors view type", "customEditors priority", "customEditors filenamePattern", "customEditors"]], ["vs/workbench/contrib/debug/browser/baseDebugView", ["debug.lazyButton.tooltip"]], ["vs/workbench/contrib/debug/browser/breakpointEditorContribution", ["breakpoint<PERSON><PERSON><PERSON>", "logPoint", "breakpoint", "breakpointHasConditionDisabled", "message", "condition", "breakpointHasConditionEnabled", "message", "condition", "removeLogPoint", "disableLogPoint", "disable", "enable", "logPoint", "breakpoint", "removeBreakpoint", "editBreakpoint", "disableBreakpoint", "enableBreakpoint", "removeBreakpoints", "removeInlineBreakpointOnColumn", "removeLineBreakpoint", "editBreakpoints", "editInlineBreakpointOnColumn", "editLineBreakpoint", "enableDisableBreakpoints", "disableInlineColumnBreakpoint", "disableBreakpointOnLine", "enableBreakpoints", "enableBreakpointOnLine", "addBreakpoint", "addConditionalBreakpoint", "addLogPoint", "addTriggeredBreakpoint", "runToLine", "debugIcon.breakpointForeground", "debugIcon.breakpointDisabledForeground", "debugIcon.breakpointUnverifiedForeground", "debugIcon.breakpointCurrentStackframeForeground", "debugIcon.breakpointStackframeForeground"]], ["vs/workbench/contrib/debug/browser/breakpointsView", ["unverifiedExceptionBreakpoint", "expressionCondition", "expressionAndHitCount", "functionBreakpointsNotSupported", "dataBreakpointsNotSupported", "read", "write", "access", "expressionAndHitCount", "functionBreakpointPlaceholder", "functionBreakPointInputAriaLabel", "functionBreakpointExpressionPlaceholder", "functionBreakPointExpresionAriaLabel", "functionBreakpointHitCountPlaceholder", "functionBreakPointHitCountAriaLabel", "dataBreakpointExpressionPlaceholder", "dataBreakPointExpresionAriaLabel", "dataBreakpointHitCountPlaceholder", "dataBreakPointHitCountAriaLabel", "exceptionBreakpointAriaLabel", "exceptionBreakpointPlaceholder", "breakpoints", "disabledLogpoint", "disabledBreakpoint", "unverifiedLogpoint", "unverifiedBreakpoint", "dataBreakpointUnsupported", "dataBreakpoint", "functionBreakpointUnsupported", "functionBreakpoint", "expression", "hitCount", "instructionBreakpointUnsupported", "instructionBreakpointAtAddress", "instructionBreakpoint", "hitCount", "breakpointUnsupported", "logMessage", "expression", "hitCount", "triggered<PERSON>y", "breakpoint", "miFunctionBreakpoint", "dataBreakpointError", "dataBreakpointAccessType", "dataBreakpointMemoryRangePrompt", "dataBreakpointMemoryRangePlaceholder", "dataBreakpointAddrFormat", "dataBreakpointAddrStartEnd", "miDataBreakpoint", "removeBreakpoint", "miRemoveAllBreakpoints", "miEnableAllBreakpoints", "miDisableAllBreakpoints", "editCondition", "editCondition", "editHitCount", "editBreakpoint", "editHitCount", "editMode", "selectBreakpointMode", "addFunctionBreakpoint", "addDataBreakpointOnAddress", "editDataBreakpointOnAddress", "activateBreakpoints", "removeAllBreakpoints", "enableAllBreakpoints", "disableAllBreakpoints", "reapplyAllBreakpoints"]], ["vs/workbench/contrib/debug/browser/breakpointWidget", ["breakpointWidgetLogMessagePlaceholder", "breakpointWidgetHitCountPlaceholder", "breakpointWidgetExpressionPlaceholder", "expression", "hitCount", "logMessage", "triggered<PERSON>y", "breakpointType", "bpMode", "noTriggerByBreakpoint", "triggerByLoading", "noBpSource", "selectBreakpoint", "ok"]], ["vs/workbench/contrib/debug/browser/callStackEditorContribution", ["topStackFrameLineHighlight", "focusedStackFrameLineHighlight"]], ["vs/workbench/contrib/debug/browser/callStackView", ["running", "showMoreStackFrames2", "session", "running", "restartFrame", "loadAllStackFrames", "showMoreAndOrigin", "showMoreStackFrames", "pausedOn", "paused", "callStackAriaLabel", "threadAriaLabel", "stackFrameAriaLabel", "running", "<PERSON><PERSON><PERSON><PERSON>", "showMoreStackFrames", "collapse"]], ["vs/workbench/contrib/debug/browser/callStackWidget", ["stackTraceLabel", "stackTrace", "stackFrameLocation", "failedToLoadFrames", "goToFile"]], ["vs/workbench/contrib/debug/browser/debug.contribution", ["debugCategory", "startDebugPlaceholder", "startDebuggingHelp", "tasksQuickAccessPlaceholder", "tasksQuickAccessHelp", "terminateThread", "restartFrame", "copyStackTrace", "viewMemory", "setValue", "breakWhenValueIsRead", "breakWhenValueChanges", "breakWhenValueIsAccessed", "viewMemory", "breakWhenValueIsRead", "breakWhenValueChanges", "breakWhenValueIsAccessed", "editWatchExpression", "setValue", "copyValue", "viewMemory", "removeWatchExpression", "mRun", "miStartDebugging", "miRun", "miStopDebugging", "miRestart Debugging", "miAddConfiguration", "miStepOver", "miStepInto", "miStepOut", "miContinue", "miInlineBreakpoint", "miNewBreakpoint", "miToggleBreakpoint", "miInstallAdditionalDebuggers", "miToggleDebugConsole", "miViewRun", "disassembly", "debugConfigurationTitle", "showVariableTypes", "allowBreakpointsEverywhere", "gutterMiddleClickAction", "debug.gutterMiddleClickAction.logpoint", "debug.gutterMiddleClickAction.conditionalBreakpoint", "debug.gutterMiddleClickAction.triggeredBreakpoint", "debug.gutterMiddleClickAction.none", "openExplorerOnEnd", "closeReadonlyTabsOnEnd", "inlineValues", "inlineValues.on", "inlineValues.off", "inlineValues.focusNoScroll", "toolBarLocation", "debugToolBar.floating", "debugToolBar.docked", "debugToolBar.commandCenter", "debugToolBar.hidden", "never", "always", "onFirstSessionStart", "showInStatusBar", "debug.console.closeOnEnd", "debug.terminal.clearBeforeReusing", "openDebug", "showSubSessionsInToolBar", "debug.console.fontSize", "debug.console.fontFamily", "debug.console.lineHeight", "debug.console.wordWrap", "debug.console.historySuggestions", "debug.console.collapseIdenticalLines", "debug.console.acceptSuggestionOnEnter", "launch", "debug.focusWindowOnBreak", "debug.focusEditorOnBreak", "debugAnyway", "showErrors", "prompt", "cancel", "debug.onTaskErrors", "showBreakpointsInOverviewRuler", "showInlineBreakpointCandidates", "debug.saveBeforeStart", "debug.saveBeforeStart.allEditorsInActiveGroup", "debug.saveBeforeStart.nonUntitledEditorsInActiveGroup", "debug.saveBeforeStart.none", "debug.confirmOnExit", "debug.confirmOnExit.never", "debug.confirmOnExit.always", "debug.disassemblyView.showSourceCode", "debug.autoExpandLazyVariables.auto", "debug.autoExpandLazyVariables.on", "debug.autoExpandLazyVariables.off", "debug.autoExpandLazyVariables", "debug.enableStatusBarColor", "debug.hideLauncherWhileDebugging", "debug.hideSlowPreLaunchWarning", "terminateThread", "jumpToCursor", "SetNextStatement", "inlineBreakpoint", "run", "runMenu", "debugPanel", "debugPanel", "run and debug", "variables", "watch", "callStack", "breakpoints", "loadedScripts"]], ["vs/workbench/contrib/debug/browser/debugActionViewItems", ["debugLaunchConfigurations", "noConfigurations", "addConfigTo", "addConfiguration", "commentLabelWithKeybinding", "commentLabelWithKeybindingNoKeybinding", "debugSession"]], ["vs/workbench/contrib/debug/browser/debugAdapterManager", ["debugNoType", "debugName", "debugServer", "debugPrelaunchTask", "debugPostDebugTask", "suppressMultipleSessionWarning", "CouldNotFindLanguage", "findExtension", "suggestedDebuggers", "moreOptionsForDebugType", "installLanguage", "installExt", "selectDebug"]], ["vs/workbench/contrib/debug/browser/debugColors", ["debugToolBarBackground", "debugToolBarBorder", "debugIcon.startForeground", "debugIcon.pauseForeground", "debugIcon.stopForeground", "debugIcon.disconnectForeground", "debugIcon.restartForeground", "debugIcon.stepOverForeground", "debugIcon.stepIntoForeground", "debugIcon.stepOutForeground", "debugIcon.continueForeground", "debugIcon.stepBackForeground"]], ["vs/workbench/contrib/debug/browser/debugCommands", ["openLaunchJson", "chooseLocation", "noExecutableCode", "jumpToCursor", "editor.debug.action.stepIntoTargets.none", "addInlineBreakpoint", "debug", "restartDebug", "stepOverDebug", "stepIntoDebug", "stepIntoTargetDebug", "stepOutDebug", "pauseDebug", "disconnect", "disconnectSuspend", "stop", "continueDebug", "focusSession", "selectAndStartDebugging", "startDebug", "startWithoutDebugging", "nextDebugConsole", "prevDebugConsole", "openLoadedScript", "callStackTop", "callStackBottom", "callStackUp", "callStackDown", "copyAsExpression", "copyValue", "copyAddress", "addToWatchExpressions", "selectDebugConsole", "selectDebugSession", "addConfiguration"]], ["vs/workbench/contrib/debug/browser/debugConfigurationManager", ["selectConfiguration", "editLaunchConfig", "DebugConfig.failed", "workspace", "user settings"]], ["vs/workbench/contrib/debug/browser/debugConsoleQuickAccess", ["workbench.action.debug.startDebug"]], ["vs/workbench/contrib/debug/browser/debugEditorActions", ["miToggleBreakpoint", "miConditionalBreakpoint", "miLogPoint", "triggerByBreakpointEditorAction", "miTriggerByBreakpoint", "EditBreakpointEditorAction", "miEditBreakpoint", "miDisassemblyView", "mitogglesource", "editor.debug.action.stepIntoTargets.notAvailable", "stepIntoTargets", "toggleBreakpointAction", "conditionalBreakpointEditorAction", "logPointEditorAction", "openDisassemblyView", "toggleDisassemblyViewSourceCode", "toggleDisassemblyViewSourceCodeDescription", "runToCursor", "evaluateInDebugConsole", "addToWatch", "showDebugHover", "goToNextBreakpoint", "goToPreviousBreakpoint", "closeExceptionWidget"]], ["vs/workbench/contrib/debug/browser/debugEditorContribution", ["editor.inlineValuesForeground", "editor.inlineValuesBackground"]], ["vs/workbench/contrib/debug/browser/debugHover", ["quickTip", "treeAriaLabel", "variableAriaLabel"]], ["vs/workbench/contrib/debug/browser/debugIcons", ["debugConsoleViewIcon", "runViewIcon", "variablesViewIcon", "watchViewIcon", "callStackViewIcon", "breakpointsViewIcon", "loadedScriptsViewIcon", "debugBreakpoint", "debugBreakpointDisabled", "debugBreakpointUnverified", "debugBreakpointPendingOnTrigger", "debugBreakpointFunction", "debugBreakpointFunctionDisabled", "debugBreakpointFunctionUnverified", "debugBreakpointConditional", "debugBreakpointConditionalDisabled", "debugBreakpointConditionalUnverified", "debugBreakpointData", "debugBreakpointDataDisabled", "debugBreakpointDataUnverified", "debugBreakpointLog", "debugBreakpointLogDisabled", "debugBreakpointLogUnverified", "debugBreakpointHint", "debugBreakpointUnsupported", "debugStackframe", "debugStackframeFocused", "debugGripper", "debugRestartFrame", "debugStop", "debugDisconnect", "debugRestart", "debugStepOver", "debugStepInto", "debugStepOut", "debugStepBack", "debugPause", "debugContinue", "debugReverseContinue", "debugRun", "debugStart", "debugConfigure", "debugConsole", "debugRemoveConfig", "debugCollapseAll", "callstackViewSession", "debugConsoleClearAll", "watchExpressionsRemoveAll", "watchExpressionRemove", "watchExpressionsAdd", "watchExpressionsAddFuncBreakpoint", "watchExpressionsAddDataBreakpoint", "breakpointsRemoveAll", "breakpointsActivate", "debugConsoleEvaluationInput", "debugConsoleEvaluationPrompt", "debugInspectMemory"]], ["vs/workbench/contrib/debug/browser/debugQuickAccess", ["noDebugResults", "customizeLaunchConfig", "mostRecent", "contributed", "removeLaunchConfig", "providerAriaLabel", "configure", "addConfigTo", "addConfiguration"]], ["vs/workbench/contrib/debug/browser/debugService", ["1activeSession", "nActiveSessions", "active debug session", "runTrust", "debugTrust", "compoundMustHaveConfigurations", "noConfigurationNameInWorkspace", "multipleConfigurationNamesInWorkspace", "noFolderWithName", "configMissing", "launchJsonDoesNotExist", "debugRequestNotSupported", "debugRequesMissing", "debugTypeNotSupported", "debugTypeMissing", "installAdditionalDebuggers", "noFolderWorkspaceDebugError", "multipleSession", "debugAdapterCrash", "debuggingPaused", "breakpointAdded", "breakpoint<PERSON><PERSON><PERSON>d"]], ["vs/workbench/contrib/debug/browser/debugSession", ["noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "sessionDoesNotSupporBytesBreakpoints", "noDebugAdapter", "sessionNotReadyForBreakpoints", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "noDebugAdapter", "debuggingStartedNoDebug", "debuggingStarted", "debuggingStopped"]], ["vs/workbench/contrib/debug/browser/debugSessionPicker", ["moveFocusedView.selectView", "workbench.action.debug.startDebug", "workbench.action.debug.spawnFrom"]], ["vs/workbench/contrib/debug/browser/debugStatus", ["status.debug", "debugTarget", "selectAndStartDebug"]], ["vs/workbench/contrib/debug/browser/debugTaskRunner", ["abort", "debugAnyway", "debugAnywayNoMemo", "preLaunchTaskErrors", "preLaunchTaskError", "preLaunchTaskExitCode", "preLaunchTaskTerminated", "showErrors", "remember", "debugAnyway", "rememberTask", "invalidTaskReference", "DebugTaskNotFoundWithTaskId", "DebugTaskNotFound", "taskNotTracked", "runningTask", "configureTask"]], ["vs/workbench/contrib/debug/browser/debugToolBar", ["notebook.moreRunActionsLabel", "stepBackDebug", "reverseContinue"]], ["vs/workbench/contrib/debug/browser/debugViewlet", ["miOpenConfigurations", "selectWorkspaceFolder", "debugPanel", "startAdditionalSession", "openLaunchConfigDescription"]], ["vs/workbench/contrib/debug/browser/disassemblyView", ["instructionNotAvailable", "disassemblyTableColumnLabel", "editorOpenedFromDisassemblyDescription", "disassemblyView", "instructionAddress", "instructionBytes", "instructionText"]], ["vs/workbench/contrib/debug/browser/exceptionWidget", ["debugExceptionWidgetBorder", "debugExceptionWidgetBackground", "exceptionThrownWithId", "exceptionThrown", "close"]], ["vs/workbench/contrib/debug/browser/linkDetector", ["followForwardedLink", "followLink", "fileLinkWithPathMac", "fileLinkWithPath", "fileLinkMac", "fileLink"]], ["vs/workbench/contrib/debug/browser/loadedScriptsView", ["loadedScriptsSession", "loadedScriptsAriaLabel", "loadedScriptsRootFolderAriaLabel", "loadedScriptsSessionAriaLabel", "loadedScriptsFolderAriaLabel", "loadedScriptsSourceAriaLabel", "collapse"]], ["vs/workbench/contrib/debug/browser/rawDebugSession", ["noDebugAdapterStart", "canNotStart", "continue", "noDebugAdapter", "moreInfo"]], ["vs/workbench/contrib/debug/browser/repl", ["workbench.debug.filter.placeholder", "showing filtered repl lines", "debugConsole", "commentLabelWithKeybinding", "commentLabelWithKeybindingNoKeybinding", "startDebugFirst", "repl.action.filter", "repl.action.find", "actions.repl.copyAll", "selectRepl", "collapse", "paste", "copyAll", "copy", "actions.repl.acceptInput", "clearRepl", "clearRepl.descriotion", "debugFocusConsole"]], ["vs/workbench/contrib/debug/browser/replAccessibilityHelp", ["repl.help", "repl.output", "repl.input", "repl.history", "repl.accessible<PERSON>iew", "repl.showRunAndDebug", "repl.clear", "repl.lazyVariables"]], ["vs/workbench/contrib/debug/browser/replViewer", ["debugConsole", "replVariableAriaLabel", "occurred", "replRawObjectAriaLabel", "replGroup"]], ["vs/workbench/contrib/debug/browser/runAndDebugAccessibilityHelp", ["debug.showRunAndDebug", "debug.startDebugging", "debug.help", "onceDebugging", "debug.restartDebugging", "debug.stopDebugging", "debug.continue", "debug.stepInto", "debug.stepOver", "debug.stepOut", "debug.views", "debug.focusBreakpoints", "debug.focusCallStack", "debug.focusVariables", "debug.focusWatch", "debug.watchSetting"]], ["vs/workbench/contrib/debug/browser/statusbarColorProvider", ["statusBarDebuggingBackground", "statusBarDebuggingForeground", "statusBarDebuggingBorder", "commandCenter-activeBackground"]], ["vs/workbench/contrib/debug/browser/variablesView", ["variableValueAriaLabel", "removeVisualizer", "variableValueAriaLabel", "useVisualizer", "variablesAriaTreeLabel", "variableScopeAriaLabel", "variableAriaLabel", "viewMemory.prompt", "collapse"]], ["vs/workbench/contrib/debug/browser/watchExpressionsView", ["typeNewValue", "watchExpressionInputAriaLabel", "watchExpressionPlaceholder", "watchAriaTreeLabel", "watchExpressionAriaLabel", "watchVariableAriaLabel", "collapse", "addWatchExpression", "removeAllWatchExpressions", "copyWatchExpression"]], ["vs/workbench/contrib/debug/browser/welcomeView", ["openAFileWhichCanBeDebugged", "runAndDebugAction", "customizeRunAndDebug", "customizeRunAndDebugOpenFolder", "allDebuggersDisabled", "run"]], ["vs/workbench/contrib/debug/common/abstractDebugAdapter", ["timeout"]], ["vs/workbench/contrib/debug/common/debug", ["debugType", "debugConfigurationType", "debugState", "debugUX", "hasDebugged", "inDebugMode", "inDebugRepl", "breakpointWidgetVisibile", "inBreakpointWidget", "breakpointsFocused", "watchExpressionsFocused", "watchExpressionsExist", "variablesFocused", "expressionSelected", "breakpointInputFocused", "callStackItemType", "callStackSessionIsAttach", "callStackItemStopped", "callStackSessionHasOneThread", "callStackFocused", "watchItemType", "canViewMemory", "breakpointItemType", "breakpointItemIsDataBytes", "breakpointHasModes", "breakpointSupportsCondition", "loadedScriptsSupported", "loadedScriptsItemType", "focusedSessionIsAttach", "focusedSessionIsNoDebug", "stepBackSupported", "restartFrameSupported", "stackFrameSupportsRestart", "jumpToCursorSupported", "stepIntoTargetsSupported", "breakpointsExist", "debuggersAvailable", "debugExtensionsAvailable", "debugProtocolVariableMenuContext", "debugSetVariableSupported", "debugSetDataBreakpointAddressSupported", "debugSetExpressionSupported", "breakWhenValueChangesSupported", "breakWhenValueIsAccessedSupported", "breakWhenValueIsReadSupported", "terminateDebuggeeSupported", "suspendDebuggeeSupported", "variableEvaluateNamePresent", "variableIsReadonly", "variableValue", "variableType", "variableInterfaces", "variableName", "variableLanguage", "variableExtensionId", "exceptionWidgetVisible", "multiSessionRepl", "multiSessionDebug", "disassembleRequestSupported", "disassemblyViewFocus", "languageSupportsDisassembleRequest", "focusedStackFrameHasInstructionReference", "debuggerDisabled", "internalConsoleOptions"]], ["vs/workbench/contrib/debug/common/debugContentProvider", ["unable", "canNotResolveSourceWithError", "canNotResolveSource"]], ["vs/workbench/contrib/debug/common/debugger", ["cannot.find.da", "launch.config.comment1", "launch.config.comment2", "launch.config.comment3", "debugType", "debugTypeNotRecognised", "node2NotSupported", "debugRequest", "debugWindowsConfiguration", "debugOSXConfiguration", "debugLinuxConfiguration"]], ["vs/workbench/contrib/debug/common/debugLifecycle", ["debug.debugSessionCloseConfirmationSingular", "debug.debugSessionCloseConfirmationPlural", "debug.stop"]], ["vs/workbench/contrib/debug/common/debugModel", ["invalidVariableAttributes", "startDebugFirst", "notAvailable", "pausedOn", "paused", "running", "breakpointDirtydHover"]], ["vs/workbench/contrib/debug/common/debugSchemas", ["vscode.extension.contributes.debuggers", "vscode.extension.contributes.debuggers.type", "vscode.extension.contributes.debuggers.label", "vscode.extension.contributes.debuggers.program", "vscode.extension.contributes.debuggers.args", "vscode.extension.contributes.debuggers.runtime", "vscode.extension.contributes.debuggers.runtimeArgs", "vscode.extension.contributes.debuggers.variables", "vscode.extension.contributes.debuggers.initialConfigurations", "vscode.extension.contributes.debuggers.languages", "vscode.extension.contributes.debuggers.configurationSnippets", "vscode.extension.contributes.debuggers.configurationAttributes", "vscode.extension.contributes.debuggers.when", "vscode.extension.contributes.debuggers.hiddenWhen", "vscode.extension.contributes.debuggers.deprecated", "vscode.extension.contributes.debuggers.windows", "vscode.extension.contributes.debuggers.windows.runtime", "vscode.extension.contributes.debuggers.osx", "vscode.extension.contributes.debuggers.osx.runtime", "vscode.extension.contributes.debuggers.linux", "vscode.extension.contributes.debuggers.linux.runtime", "vscode.extension.contributes.debuggers.strings", "vscode.extension.contributes.debuggers.strings.unverifiedBreakpoints", "vscode.extension.contributes.breakpoints", "vscode.extension.contributes.breakpoints.language", "vscode.extension.contributes.breakpoints.when", "presentation", "presentation.hidden", "presentation.group", "presentation.order", "app.launch.json.title", "app.launch.json.version", "app.launch.json.configurations", "app.launch.json.compounds", "app.launch.json.compound.name", "useUniqueNames", "app.launch.json.compound.name", "app.launch.json.compound.folder", "app.launch.json.compounds.configurations", "app.launch.json.compound.stopAll", "compoundPrelaunchTask", "debugger name", "debugger type", "debuggers"]], ["vs/workbench/contrib/debug/common/debugSource", ["unknownSource"]], ["vs/workbench/contrib/debug/common/disassemblyViewInput", ["disassemblyEditorLabelIcon", "disassemblyInputName"]], ["vs/workbench/contrib/debug/common/loadedScriptsPicker", ["moveFocusedView.selectView"]], ["vs/workbench/contrib/debug/common/replModel", ["consoleCleared"]], ["vs/workbench/contrib/debug/node/debugAdapter", ["debugAdapterBinNotFound", "debugAdapterCannotDetermineExecutable", "unableToLaunchDebugAdapter", "unableToLaunchDebugAdapterNoArgs"]], ["vs/workbench/contrib/dropOrPasteInto/browser/commands", ["configureDefaultPaste.label", "configureDefaultDrop.label"]], ["vs/workbench/contrib/dropOrPasteInto/browser/configurationSchema", ["dropPreferredDescription", "dropKind", "pastePreferredDescription", "pasteKind"]], ["vs/workbench/contrib/editSessions/browser/editSessions.contribution", ["continueOn.installAdditional", "resuming working changes window", "autoStoreWorkingChanges", "check for pending cloud changes", "store working changes", "store your working changes", "storing working changes", "checkingForWorkingChanges", "no cloud changes", "no cloud changes for ref", "client too old", "resume edit session warning many", "resume edit session warning 1", "resume failed", "editSessionPartialMatch", "resume", "payload too large", "no working changes to store", "payload too large", "payload failed", "continue with cloud changes", "with cloud changes", "without cloud changes", "continueEditSession.openLocalFolder.title.v2", "continueWorkingOn.existingLocalFolder", "continueEditSessionPick.title.v2", "continueEditSessionItem.openInLocalFolder.v2", "continueEditSessionItem.builtin", "learnMoreTooltip", "continueEditSessionExtPoint", "continueEditSessionExtPoint.command", "continueEditSessionExtPoint.group", "continueEditSessionExtPoint.qualifiedName", "continueEditSessionExtPoint.description", "continueEditSessionExtPoint.remoteGroup", "continueEditSessionExtPoint.when", "autoStoreWorkingChanges.onShutdown", "autoStoreWorkingChanges.off", "autoStoreWorkingChangesDescription", "autoResumeWorkingChanges.onReload", "autoResumeWorkingChanges.off", "autoResumeWorkingChanges", "continueOnCloudChanges.promptForAuth", "continueOnCloudChanges.off", "continueOnCloudChanges", "cloudChangesPartialMatchesEnabled", "continue working on", "continue edit session in local folder", "show log", "show cloud changes", "resume latest cloud changes", "resume cloud changes", "store working changes in cloud"]], ["vs/workbench/contrib/editSessions/browser/editSessionsStorageService", ["choose account read placeholder", "choose account placeholder", "signed in", "others", "sign in using account", "sign in", "sign in badge", "reset auth.v3", "sign out of cloud changes clear data prompt", "delete all cloud changes"]], ["vs/workbench/contrib/editSessions/browser/editSessionsViews", ["noStoredChanges", "storeWorkingChangesTitle", "workbench.editSessions.actions.resume.v2", "workbench.editSessions.actions.store.v2", "workbench.editSessions.actions.delete.v2", "confirm delete.v2", "confirm delete detail.v2", "workbench.editSessions.actions.deleteAll", "confirm delete all", "confirm delete all detail", "compare changes", "local copy", "cloud changes", "open file"]], ["vs/workbench/contrib/editSessions/common/editSessions", ["editSessionViewIcon", "cloud changes", "cloud changes"]], ["vs/workbench/contrib/editSessions/common/editSessionsLogService", ["cloudChangesLog"]], ["vs/workbench/contrib/emmet/browser/actions/expandAbbreviation", ["miEmmetExpandAbbreviation", "expandAbbreviationAction"]], ["vs/workbench/contrib/extensions/browser/abstractRuntimeExtensionsEditor", ["starActivation", "workspaceContainsGlobActivation", "workspaceContainsFileActivation", "workspaceContainsTimeout", "startupFinishedActivation", "languageActivation", "workspaceGenericActivation", "extensionActivating", "unresponsive.title", "errors", "requests count", "session requests count", "requests count title", "runtimeExtensions", "copy id", "disable workspace", "disable", "showRuntimeExtensions"]], ["vs/workbench/contrib/extensions/browser/configBasedRecommendations", ["exeBasedRecommendation"]], ["vs/workbench/contrib/extensions/browser/exeBasedRecommendations", ["exeBasedRecommendation"]], ["vs/workbench/contrib/extensions/browser/extensionEditor", ["extension version", "name", "preview", "preview", "builtin", "details", "<PERSON><PERSON><PERSON><PERSON>", "features", "featurestooltip", "changelog", "changelogtooltip", "dependencies", "dependenciestooltip", "extensionpack", "extensionpacktooltip", "noReadme", "Readme title", "extension pack", "noReadme", "Readme title", "no<PERSON><PERSON><PERSON><PERSON>", "Changelog title", "noDependencies", "categories", "Marketplace", "issues", "repository", "license", "resources", "Install Info", "id", "Version", "last updated", "vsix", "other", "source", "size when installed", "size", "disk space used", "cache size", "Marketplace Info", "id", "Version", "published", "last released", "find", "find next", "find previous"]], ["vs/workbench/contrib/extensions/browser/extensionEnablementWorkspaceTrustTransitionParticipant", ["restartExtensionHost.reason"]], ["vs/workbench/contrib/extensions/browser/extensionFeaturesTab", ["activation", "label", "chartDescription", "uncaught errors", "messaages", "runtime", "noFeatures", "extension features list", "revoked", "accessExtensionFeature", "disableAccessExtensionFeatureMessage", "enableAccessExtensionFeatureMessage", "revoke", "grant", "cancel", "revoke", "enable"]], ["vs/workbench/contrib/extensions/browser/extensionRecommendationNotificationService", ["ignoreExtensionRecommendations", "ignoreAll", "no", "this repository", "extensionFromPublisher", "extensionsFromMultiplePublishers", "extensionsFromPublishers", "extensionsFromPublisher", "recommended", "exeRecommended", "donotShowAgain", "donotShowAgainExtension", "donotShowAgainExtensionSingle", "install", "install and do no sync", "show recommendations"]], ["vs/workbench/contrib/extensions/browser/extensions.contribution", ["manageExtensionsQuickAccessPlaceholder", "manageExtensionsHelp", "extension", "miViewExtensions", "extensionsConfigurationTitle", "all", "enabled", "none", "extensions.autoUpdate.true", "extensions.autoUpdate.enabled", "extensions.autoUpdate.false", "extensions.autoUpdate", "extensionsCheckUpdates", "extensionsIgnoreRecommendations", "extensionsShowRecommendationsOnlyOnDemand_Deprecated", "extensionsCloseExtensionDetailsOnViewChange", "handleUriConfirmedExtensions", "extensionsWebWorker.true", "extensionsWebWorker.false", "extensionsWebWorker.auto", "extensionsWebWorker", "extensions.supportVirtualWorkspaces", "extensions.affinity", "extensions.supportUntrustedWorkspaces", "extensions.supportUntrustedWorkspaces.true", "extensions.supportUntrustedWorkspaces.false", "extensions.supportUntrustedWorkspaces.limited", "extensions.supportUntrustedWorkspaces.supported", "extensions.supportUntrustedWorkspaces.version", "extensionsDeferredStartupFinishedActivation", "extensionsInQuickAccess", "extensions.verifySignature", "autoRestart", "extensions.gallery.useUnpkgResourceApi", "extensions.gallery.serviceUrl", "notFound", "workbench.extensions.installExtension.description", "workbench.extensions.installExtension.arg.decription", "workbench.extensions.installExtension.option.installOnlyNewlyAddedFromExtensionPackVSIX", "workbench.extensions.installExtension.option.installPreReleaseVersion", "workbench.extensions.installExtension.option.donotSync", "workbench.extensions.installExtension.option.justification", "workbench.extensions.installExtension.option.enable", "workbench.extensions.installExtension.option.context", "notFound", "workbench.extensions.uninstallExtension.description", "workbench.extensions.uninstallExtension.arg.name", "id required", "notInstalled", "builtin", "workbench.extensions.search.description", "workbench.extensions.search.arg.name", "installExtensionQuickAccessPlaceholder", "installExtensionQuickAccessHelp", "miPreferencesExtensions", "showExtensions", "importKeyboardShortcutsFroms", "noUpdatesAvailable", "installFromVSIX", "installButton", "installVSIX", "InstallVSIXs.successReload", "InstallVSIXAction.successReload", "InstallVSIXAction.reloadNow", "InstallVSIXs.successRestart", "InstallVSIXAction.successRestart", "InstallVSIXAction.restartExtensions", "InstallVSIXs.successNoReload", "InstallVSIXAction.successNoReload", "installFromLocation", "install button", "installFromLocationPlaceHolder", "installFromLocation", "filterExtensions", "featured filter", "most popular filter", "most popular recommended", "recently published filter", "filter by category", "builtin filter", "extension updates filter", "workspace unsupported filter", "enabled filter", "disabled filter", "sorty by", "sort by installs", "sort by rating", "sort by name", "sort by published date", "sort by update date", "installWorkspaceRecommendedExtensions", "enablePreRleaseLabel", "disablePreRleaseLabel", "install", "install installAndDonotSync", "installPrereleaseAndDonotSync", "extensionInfoName", "extensionInfoId", "extensionInfoDescription", "extensionInfoVersion", "extensionInfoPublisher", "extensionInfoVSMarketplaceLink", "download VSIX", "download pre-release", "trustedPublishers", "trustedPublishersPlaceholder", "extensions", "focusExtensions", "installExtensions", "showRecommendedKeymapExtensionsShort", "showLanguageExtensionsShort", "checkForUpdates", "enableAutoUpdate", "disableAutoUpdate", "updateAll", "enableAll", "enableAllWorkspace", "disableAll", "disableAllWorkspace", "InstallFromVSIX", "installExtensionFromLocation", "showFeaturedExtensions", "showPopularExtensions", "showRecommendedExtensions", "recentlyPublishedExtensions", "showBuiltInExtensions", "extensionUpdates", "showWorkspaceUnsupportedExtensions", "showEnabledExtensions", "showDisabledExtensions", "clearExtensionsSearchResults", "refreshExtension", "show pre-release version", "show released version", "workbench.extensions.action.copyExtension", "workbench.extensions.action.copyExtensionId", "workbench.extensions.action.copyLink", "workbench.extensions.action.configure", "workbench.extensions.action.changeAccountPreference", "workbench.extensions.action.configureKeybindings", "workbench.extensions.action.toggleApplyToAllProfiles", "workbench.extensions.action.toggleIgnoreExtension", "workbench.extensions.action.ignoreRecommendation", "workbench.extensions.action.undoIgnoredRecommendation", "workbench.extensions.action.addExtensionToWorkspaceRecommendations", "workbench.extensions.action.removeExtensionFromWorkspaceRecommendations", "workbench.extensions.action.addToWorkspaceRecommendations", "workbench.extensions.action.addToWorkspaceFolderRecommendations", "workbench.extensions.action.addToWorkspaceIgnoredRecommendations", "workbench.extensions.action.addToWorkspaceFolderIgnoredRecommendations", "workbench.extensions.action.manageTrustedPublishers"]], ["vs/workbench/contrib/extensions/browser/extensions.web.contribution", ["runtimeExtension"]], ["vs/workbench/contrib/extensions/browser/extensionsActions", ["VS Code for Web", "cannot be installed", "more information", "close", "install prerelease", "cancel", "not signed", "install anyway", "verification failed", "learn more", "install donot verify", "verification failed", "learn more", "report issue", "report issue title", "report issue body", "install donot verify", "update operation", "install operation", "check logs", "download", "install vsix", "installVSIX", "install", "not signed", "not signed detail", "install anyway", "deprecated message", "install anyway", "deprecated with alternate extension message", "Show alternate extension", "deprecated with alternate settings message", "configure in settings", "install confirmation", "installExtensionStart", "installExtensionComplete", "install workspace version", "install pre-release", "install pre-release version", "install", "install release version", "install", "installing", "install", "installing", "installExtensionStart", "install in remote", "install locally", "install browser", "uninstallAction", "Uninstalling", "uninstallExtensionStart", "uninstallExtensionComplete", "update", "update to", "update", "updateExtensionConsentTitle", "updateExtensionConsent", "update", "review", "cancel", "updateExtensionStart", "updateExtensionComplete", "enableAutoUpdate", "disableAutoUpdate", "toggleAutoUpdatesForPublisherLabel", "ignoreExtensionUpdatePublisher", "enableAutoUpdate", "disableAutoUpdate", "migrateExtension", "migrate to", "migrate", "manage", "manage", "togglePreRleaseLabel", "togglePreRleaseDisableLabel", "togglePreRleaseDisableTooltip", "switchToPreReleaseLabel", "switchToPreReleaseTooltip", "install another version", "no versions", "pre-release", "current", "selectVersion", "enableForWorkspaceAction", "enableForWorkspaceActionToolTip", "enableGloballyAction", "enableGloballyActionToolTip", "disableForWorkspaceAction", "disableForWorkspaceActionToolTip", "disableGloballyAction", "disableGloballyActionToolTip", "reload window", "restart extensions", "restart product", "update product", "current", "select color theme", "select file icon theme", "select product icon theme", "showRecommendedExtension", "installRecommendedExtension", "ignoreExtensionRecommendation", "undo", "OpenExtensionsFile.failed", "configureWorkspaceRecommendedExtensions", "configureWorkspaceFolderRecommendedExtensions", "installed", "updated", "uninstalled", "enabled", "disabled", "ignored", "synced", "sync", "do not sync", "malicious tooltip", "not signed tooltip", "deprecated with alternate extension tooltip", "settings", "deprecated with alternate settings tooltip", "deprecated tooltip", "auto update message", "disabled - not allowed", "disabled by environment", "enabled by environment", "disabled because of virtual workspace", "extension limited because of virtual workspace", "extension disabled because of trust requirement", "extension limited because of trust requirement", "Install in remote server to enable", "learn more", "Install in local server to enable", "learn more", "Defined to run in desktop", "learn more", "Cannot be enabled", "learn more", "manage access", "Install language pack also in remote server", "Install language pack also locally", "enabled remotely", "learn more", "enabled locally", "learn more", "enabled in web worker", "learn more", "extension disabled because of dependency", "dependencies", "workspace enabled", "extension enabled on remote", "globally disabled", "workspace disabled", "install previous version", "selectExtension", "select extensions to install", "no local extensions", "installing extensions", "finished installing", "select and install local extensions", "install local extensions title", "select and install remote extensions", "install remote extensions", "extensionButtonBackground", "extensionButtonForeground", "extensionButtonHoverBackground", "extensionButtonSeparator", "extensionButtonProminentBackground", "extensionButtonProminentForeground", "extensionButtonProminentHoverBackground", "enableAutoUpdateLabel", "workbench.extensions.action.setColorTheme", "workbench.extensions.action.setFileIconTheme", "workbench.extensions.action.setProductIconTheme", "workbench.extensions.action.setDisplayLanguage", "workbench.extensions.action.clearLanguage"]], ["vs/workbench/contrib/extensions/browser/extensionsActivationProgress", ["activation"]], ["vs/workbench/contrib/extensions/browser/extensionsCompletionItemsProvider", ["exampleExtension"]], ["vs/workbench/contrib/extensions/browser/extensionsDependencyChecker", ["extensions", "auto install missing deps", "finished installing missing deps", "reload", "no missing deps"]], ["vs/workbench/contrib/extensions/browser/extensionsIcons", ["extensionsViewIcon", "manageExtensionIcon", "clearSearchResultsIcon", "refreshIcon", "filterIcon", "installLocalInRemoteIcon", "installWorkspaceRecommendedIcon", "configureRecommendedIcon", "syncEnabledIcon", "syncIgnoredIcon", "remoteIcon", "installCountIcon", "lockIcon", "ratingIcon", "preReleaseIcon", "sponsorIcon", "starFullIcon", "starHalfIcon", "starEmptyIcon", "errorIcon", "warningIcon", "infoIcon", "trustIcon", "activationtimeIcon"]], ["vs/workbench/contrib/extensions/browser/extensionsQuickAccess", ["type", "searchFor", "install", "manage"]], ["vs/workbench/contrib/extensions/browser/extensionsViewer", ["extensions", "error", "Unknown Extension", "extensions"]], ["vs/workbench/contrib/extensions/browser/extensionsViewlet", ["installed", "searchExtensions", "click show", "show", "dismiss", "dismiss", "extensionFoundInSection", "extensionFound", "extensionsFoundInSection", "extensionsFound", "suggestProxyError", "open user settings", "extensionToUpdate", "extensionsToUpdate", "extensionToReload", "extensionsToReload", "restartNow", "reloadNow", "learnMore", "malicious warning", "remote", "select and install local extensions", "install remote in local", "popularExtensions", "recommendedExtensions", "enabledExtensions", "disabledExtensions", "marketPlace", "installed", "recently updated", "enabled", "disabled", "availableUpdates", "builtin", "workspaceUnsupported", "workspaceRecommendedExtensions", "otherRecommendedExtensions", "builtinFeatureExtensions", "builtInThemesExtensions", "builtinProgrammingLanguageExtensions", "untrustedUnsupportedExtensions", "untrustedPartiallySupportedExtensions", "virtualUnsupportedExtensions", "virtualPartiallySupportedExtensions", "deprecated"]], ["vs/workbench/contrib/extensions/browser/extensionsViews", ["showingExtensionsForFeature", "no extensions found", "offline error", "error", "no local extensions", "extension.arialabel.verifiedPublisher", "extension.arialabel.publisher", "extension.arialabel.deprecated", "extension.arialabel.rating"]], ["vs/workbench/contrib/extensions/browser/extensionsWidgets", ["install count", "ratedLabel", "publisher", "verified publisher", "sponsor", "remote extension title", "privateExtension", "workspace extension", "local extension", "syncingore.label", "feature access label", "privateExtension", "sponsor", "workspace extension", "local extension", "publisher verified tooltip", "updateRequired", "activation", "startup", "uncaught error", "uncaught errors", "message", "messages", "feature usage label", "total", "Show prerelease version", "has prerelease", "recommendationHasBeenIgnored", "extensionIconStarForeground", "extensionPreReleaseForeground", "extensionIcon.sponsorForeground", "extensionIcon.private"]], ["vs/workbench/contrib/extensions/browser/extensionsWorkbenchService", ["Manifest is not found", "confirmEnableDisableAutoUpdate", "confirmEnableAutoUpdate", "confirmDisableAutoUpdate", "confirmEnableDisableAutoUpdateDetail", "updatingExtensions", "disallowed extensions by policy", "disallowed extensions", "incompatibleExtensions", "invalidExtensions", "deprecated extensions", "restart", "extensionsAutoRestart", "reload", "restart extensions", "postUninstallTooltip", "postUpdateDownloadTooltip", "postUpdateUpdateTooltip", "postUpdateRestartTooltip", "postUpdateTooltip", "enable locally", "enable remote", "postEnableTooltip", "postEnableTooltip", "postDisableTooltip", "postEnableTooltip", "postEnableTooltip", "extension not found", "allplatforms", "platform placeholder", "download title", "download", "downloading...", "download.completed", "consentRequiredToUpdateRepublishedExtension", "consentRequiredToUpdate", "not an extension", "malicious", "disallowed", "not signed", "cannot be installed", "cannot be installed", "malicious", "report issue", "not found version", "not found", "installButtonLabel", "installButtonLabelWithAction", "open", "installExtensionTitle", "installExtensionMessage", "installVSIXMessage", "sync extension", "unknown", "enableExtensionTitle", "enableExtensionMessage", "enableButtonLabel", "enableButtonLabelWithAction", "incompatible", "uninstallDependents", "uninstallAll", "uninstallingExtension", "singleDependentUninstallError", "twoDependentsUninstallError", "multipleDependentsUninstallError", "installing named extension", "installing extension", "disableDependents", "disable all", "singleDependentError", "twoDependentsError", "multipleDependentsError"]], ["vs/workbench/contrib/extensions/browser/fileBasedRecommendations", ["fileBasedRecommendation", "languageName"]], ["vs/workbench/contrib/extensions/browser/webRecommendations", ["reason"]], ["vs/workbench/contrib/extensions/browser/workspaceRecommendations", ["workspaceRecommendation", "workspaceRecommendation"]], ["vs/workbench/contrib/extensions/common/extensions", ["extensions"]], ["vs/workbench/contrib/extensions/common/extensionsFileTemplate", ["app.extensions.json.title", "app.extensions.json.recommendations", "app.extension.identifier.errorMessage", "app.extensions.json.unwantedRecommendations", "app.extension.identifier.errorMessage"]], ["vs/workbench/contrib/extensions/common/extensionsInput", ["extensionsEditorLabelIcon", "extensionsInputName"]], ["vs/workbench/contrib/extensions/common/extensionsUtils", ["disableOtherKeymapsConfirmation", "yes", "no"]], ["vs/workbench/contrib/extensions/common/reportExtensionIssueAction", ["reportExtensionIssue"]], ["vs/workbench/contrib/extensions/common/runtimeExtensionsInput", ["runtimeExtensionEditorLabelIcon", "extensionsInputName"]], ["vs/workbench/contrib/extensions/common/searchExtensionsTool", ["searchExtensionsTool.displayName", "searchExtensionsTool.modelDescription", "searchExtensionsTool.noInput"]], ["vs/workbench/contrib/extensions/electron-sandbox/debugExtensionHostAction", ["debugExtensionHost", "restart1", "restart2", "restart3", "debugExtensionHost.progress", "debugExtensionHost.launch.name"]], ["vs/workbench/contrib/extensions/electron-sandbox/extensionProfileService", ["status.profiler", "profilingExtensionHost", "profilingExtensionHost", "selectAndStartDebug", "profilingExtensionHostTime", "restart1", "restart2", "restart3"]], ["vs/workbench/contrib/extensions/electron-sandbox/extensions.contribution", ["runtimeExtension"]], ["vs/workbench/contrib/extensions/electron-sandbox/extensionsActions", ["openExtensionsFolder", "cleanUpExtensionsFolder"]], ["vs/workbench/contrib/extensions/electron-sandbox/extensionsAutoProfiler", ["unresponsive-exthost", "show"]], ["vs/workbench/contrib/extensions/electron-sandbox/extensionsSlowActions", ["cmd.reportOrShow", "cmd.report", "attach.title", "attach.msg", "cmd.show", "attach.title", "attach.msg2"]], ["vs/workbench/contrib/extensions/electron-sandbox/runtimeExtensionsEditor", ["extensionHostProfileStart", "stopExtensionHostProfileStart", "openExtensionHostProfile", "saveExtensionHostProfile", "saveprofile.dialogTitle"]], ["vs/workbench/contrib/externalTerminal/browser/externalTerminal.contribution", ["scopedConsoleAction.Integrated", "scopedConsoleAction.external", "scopedConsoleAction.wt"]], ["vs/workbench/contrib/externalTerminal/electron-sandbox/externalTerminal.contribution", ["terminal.kind.integrated", "terminal.kind.external", "terminal.kind.both", "terminalConfigurationTitle", "explorer.openInTerminalKind", "sourceControlRepositories.openInTerminalKind", "terminal.external.windowsExec", "terminal.external.osxExec", "terminal.external.linuxExec", "globalConsoleAction"]], ["vs/workbench/contrib/externalUriOpener/common/configuration", ["externalUriOpeners", "externalUriOpeners.uri", "externalUriOpeners.uri", "externalUriOpeners.defaultId"]], ["vs/workbench/contrib/externalUriOpener/common/externalUriOpenerService", ["selectOpenerDefaultLabel.web", "selectOpenerDefaultLabel", "selectOpenerConfigureTitle", "selectOpenerPlaceHolder"]], ["vs/workbench/contrib/files/browser/editors/binaryFileEditor", ["binaryFileEditor"]], ["vs/workbench/contrib/files/browser/editors/textFileEditor", ["textFileEditor", "openFolder", "reveal", "fileIsDirectory", "fileTooLargeForHeapErrorWithSize", "fileTooLargeForHeapErrorWithoutSize", "unavailableResourceErrorEditorText", "createFile"]], ["vs/workbench/contrib/files/browser/editors/textFileSaveErrorHandler", ["userGuide", "staleSaveError", "readonlySaveErrorAdmin", "readonlySaveError<PERSON>udo", "readonlySaveError", "permissionDeniedSaveError", "permissionDeniedSaveErrorSudo", "genericSaveError", "learnMore", "dontShowAgain", "compareChanges", "saveConflictDiffLabel", "overwriteElevated", "overwriteElevatedSudo", "saveElevated", "saveElevatedSudo", "retry", "revert", "overwrite", "overwrite", "configure"]], ["vs/workbench/contrib/files/browser/explorerViewlet", ["explorerViewIcon", "openEditorsIcon", "miViewExplorer", "openFolder", "addAFolder", "openRecent", "noWorkspaceHelp", "noFolderHelpWeb", "remoteNoFolderHelp", "noFolderButEditorsHelp", "noFolderHelp", "folders", "explore", "explore"]], ["vs/workbench/contrib/files/browser/fileActions.contribution", ["copyPath", "copyRelativePath", "revealInSideBar", "acceptLocalChanges", "revertLocalChanges", "openToSide", "reopenWith", "revert", "saveAll", "compareWithSaved", "compareWithSelected", "compareSource", "compareSelected", "close", "closeOthers", "closeSaved", "closeAll", "explorer<PERSON><PERSON><PERSON><PERSON>", "cut", "deleteFile", "deleteFile", "newFile", "openFile", "miNewFile", "miSave", "miSaveAs", "miSaveAll", "miAutoSave", "miRevert", "miCloseEditor", "miGotoFile", "copyPathOfActive", "copyRelativePathOfActive", "saveAllInGroup", "saveFiles", "revert", "compareActiveWithSaved", "compareActiveWithSavedMeta", "newFolderDescription"]], ["vs/workbench/contrib/files/browser/fileActions", ["rename", "delete", "copyFile", "pasteFile", "download", "upload", "deleteButtonLabelRecycleBin", "deleteButtonLabelTrash", "deleteButtonLabel", "dirtyMessageFilesDelete", "dirtyMessageFolderOneDelete", "dirtyMessageFolderDelete", "dirtyMessageFileDelete", "dirtyWarning", "readonlyMessageFilesDelete", "readonlyMessageFolderOneDelete", "readonlyMessageFolderDelete", "continueDetail", "continueButtonLabel", "irreversible", "restorePlural", "restore", "undoBinFiles", "undoBin", "undoTrashFiles", "undoTrash", "doNotAskAgain", "deleteBulkEdit", "deleteFileBulkEdit", "deletingBulkEdit", "deletingFileBulkEdit", "binFailed", "trashFailed", "deletePermanentlyButtonLabel", "retryButtonLabel", "confirmMoveTrashMessageFilesAndDirectories", "confirmMoveTrashMessageMultipleDirectories", "confirmMoveTrashMessageMultiple", "confirmMoveTrashMessageFolder", "confirmMoveTrashMessageFile", "confirmDeleteMessageFilesAndDirectories", "confirmDeleteMessageMultipleDirectories", "confirmDeleteMessageMultiple", "confirmDeleteMessageFolder", "confirmDeleteMessageFile", "confirmOverwrite", "replaceButtonLabel", "saveAllInGroup", "closeGroup", "openFileToShowInNewWindow.unsupportedschema", "emptyFileNameError", "fileNameStartsWithSlashError", "fileNameExistsError", "invalidFileNameError", "fileNameWhitespaceWarning", "clipboardComparisonLabel", "retry", "createBulkEdit", "creatingBulkEdit", "renameBulkEdit", "renamingBulkEdit", "confirmMultiPasteNative", "confirmPasteNative", "doNotAskAgain", "pasteButtonLabel", "fileIsAncestor", "movingBulkEdit", "movingFileBulkEdit", "moveBulkEdit", "moveFileBulkEdit", "fileDeleted", "copyingBulkEdit", "copyingFileBulkEdit", "copyBulkEdit", "copyFileBulkEdit", "newFile", "newFolder", "globalCompareFile", "compareFileWithMeta", "toggleAutoSave", "toggleAutoSaveDescription", "focusFilesExplorer", "focusFilesExplorerMetadata", "showInExplorer", "showInExplorerMetadata", "openFileInEmptyWorkspace", "openFileInEmptyWorkspaceMetadata", "compareNewUntitledTextFiles", "compareNewUntitledTextFilesMeta", "compareWithClipboard", "compareWithClipboardMeta", "setActiveEditorReadonlyInSession", "setActiveEditorWriteableInSession", "toggleActiveEditorReadonlyInSession", "resetActiveEditorReadonlyInSession"]], ["vs/workbench/contrib/files/browser/fileCommands", ["modifiedLabel", "retry", "revertAll", "revert", "genericSaveError", "genericRevertError", "newFileCommand.saveLabel"]], ["vs/workbench/contrib/files/browser/fileConstants", ["removeFolderFromWorkspace", "saveAs", "save", "saveWithoutFormatting", "saveAll", "newUntitledFile"]], ["vs/workbench/contrib/files/browser/fileImportExport", ["uploadingFiles", "overwrite", "overwriting", "uploadProgressSmallMany", "uploadProgressLarge", "copyingFiles", "copyFolders", "copyFolder", "addFolders", "addFolder", "dropFolders", "dropFolder", "copyfolders", "copyfolder", "filesInaccessible", "fileInaccessible", "importFile", "importnFile", "copyingFile", "copyingnFile", "downloadingFiles", "downloadProgressSmallMany", "downloadProgressLarge", "downloadButton", "chooseWhereToDownload", "downloadBulkEdit", "downloadingBulkEdit", "confirmOverwrite", "irreversible", "replaceButtonLabel", "confirmManyOverwrites", "irreversible", "replaceButtonLabel"]], ["vs/workbench/contrib/files/browser/files.contribution", ["textFileEditor", "binaryFileEditor", "hotExit.off", "hotExit.onExit", "hotExit.onExitAndWindowClose", "hotExit", "hotExit.off", "hotExit.onExitAndWindowCloseBrowser", "hotExit", "filesConfigurationTitle", "exclude", "trueDescription", "falseDescription", "files.exclude.boolean", "files.exclude.when", "associations", "encoding", "autoGuessEncoding", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eol.LF", "eol.CRLF", "eol.auto", "eol", "useTrash", "trimTrailingWhitespace", "trimTrailingWhitespaceInRegexAndStrings", "insertFinalNewline", "trimFinalNewlines", "files.autoSave.off", "files.autoSave.afterDelay", "files.autoSave.onFocusChange", "files.autoSave.onWindowChange", "autoSave", "autoSaveDelay", "autoSaveWorkspaceFilesOnly", "autoSaveWhenNoErrors", "watcherExclude", "watcherInclude", "defaultLanguage", "filesReadonlyInclude", "filesReadonlyExclude", "filesReadonlyFromPermissions", "files.restoreUndoStack", "askUser", "overwriteFileOnDisk", "files.saveConflictResolution", "defaultPathErrorMessage", "fileDialogDefaultPath", "files.simpleDialog.enable", "files.participants.timeout", "formatOnSave", "everything", "modification", "modificationIfAvailable", "formatOnSaveMode", "explorerConfigurationTitle", "openEditorsVisible", "openEditorsVisibleMin", "openEditorsSortOrder", "sortOrder.editorOrder", "sortOrder.alphabetical", "sortOrder.fullPath", "autoReveal.on", "autoReveal.off", "autoReveal.focusNoScroll", "autoReveal", "autoRevealExclude", "explorer.autoRevealExclude.boolean", "explorer.autoRevealExclude.when", "enableDragAndDrop", "confirmDragAndDrop", "confirmPasteNative", "confirmDelete", "enableUndo", "confirmUndo", "enableUndo.verbose", "enableUndo.default", "enableUndo.light", "expandSingleFolderWorkspaces", "sortOrder.default", "sortOrder.mixed", "sortOrder.filesFirst", "sortOrder.type", "sortOrder.modified", "sortOrder.foldersNestsFiles", "sortOrder", "sortOrderLexicographicOptions.default", "sortOrderLexicographicOptions.upper", "sortOrderLexicographicOptions.lower", "sortOrderLexicographicOptions.unicode", "sortOrderLexicographicOptions", "sortOrderReverse", "explorer.decorations.colors", "explorer.decorations.badges", "simple", "smart", "disabled", "explorer.incrementalNaming", "autoOpenDroppedFile", "compressSingleChildFolders", "copyRelativePathSeparator.slash", "copyRelativePathSeparator.backslash", "copyRelativePathSeparator.auto", "copyRelativePathSeparator", "copyPathSeparator.slash", "copyPathSeparator.backslash", "copyPathSeparator.auto", "copyPathSeparator", "excludeGitignore", "fileNestingEnabled", "fileNestingExpand", "fileNestingPatterns", "fileNesting.description"]], ["vs/workbench/contrib/files/browser/views/emptyView", ["noWorkspace"]], ["vs/workbench/contrib/files/browser/views/explorerDecorationsProvider", ["canNotResolve", "symbolicLlink", "unknown", "label"]], ["vs/workbench/contrib/files/browser/views/explorerView", ["explorerSection", "createNewFile", "createNewFolder", "refreshExplorer", "refreshExplorerMetadata", "collapseExplorerFolders", "collapseExplorerFoldersMetadata"]], ["vs/workbench/contrib/files/browser/views/explorer<PERSON>iewer", ["searchMaxResultsWarning", "searchMaxResultsWarning", "treeAriaLabel", "explorerHighlightFolderBadgeTitle", "fileInputAriaLabel", "confirmRootsMove", "confirmMultiMove", "confirmRootMove", "confirmMove", "doNotAskAgain", "moveButtonLabel", "copy", "copying", "move", "moving", "numberOfFolders", "numberOfFiles"]], ["vs/workbench/contrib/files/browser/views/openEditorsView", ["<PERSON><PERSON>ounter", "openEditors", "miToggleEditorLayout", "openEditors", "flipLayout", "miToggleEditorLayoutWithoutMnemonic", "newUntitledFile"]], ["vs/workbench/contrib/files/browser/workspaceWatcher", ["enospcError", "learnMore", "eshutdownError", "reload"]], ["vs/workbench/contrib/files/common/dirtyFilesIndicator", ["dirtyFile", "dirtyFiles"]], ["vs/workbench/contrib/files/common/files", ["explorerViewletVisible", "foldersViewVisible", "explorerResourceIsFolder", "explorerResourceReadonly", "explorerResourceParentReadonly", "explorerResourceIsRoot", "explorerResourceCut", "explorerResourceMoveableToTrash", "filesExplorerFocus", "openEditorsFocus", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "explorerFindProviderActive", "explorerViewletCompressedFocus", "explorerViewletCompressedFirstFocus", "explorerViewletCompressedLastFocus", "viewHasSomeCollapsibleItem"]], ["vs/workbench/contrib/files/electron-sandbox/fileActions.contribution", ["miShare", "revealInWindows", "revealInMac", "openContainer", "filesCategory"]], ["vs/workbench/contrib/folding/browser/folding.contribution", ["null", "nullFormatterDescription", "formatter.default"]], ["vs/workbench/contrib/format/browser/formatActionsMultiple", ["null", "nullFormatterDescription", "miss.1", "miss.2", "config.needed", "config.bad", "miss", "do.config", "do.config.notification", "select", "do.config.command", "summary", "formatter", "formatter.default", "def", "config", "format.placeHolder", "select", "formatDocument.label.multiple", "formatSelection.label.multiple"]], ["vs/workbench/contrib/format/browser/formatActionsNone", ["too.large", "no.provider", "install.formatter", "formatDocument.label.multiple"]], ["vs/workbench/contrib/format/browser/formatModified", ["formatChanges"]], ["vs/workbench/contrib/inlayHints/browser/inlayHintsAccessibilty", ["isReadingLineWithInlayHints", "description", "read.title", "stop.title"]], ["vs/workbench/contrib/inlineChat/browser/inlineChat.contribution", ["send.edit", "send.generate", "cancel", "cancelShort"]], ["vs/workbench/contrib/inlineChat/browser/inlineChatActions", ["startInlineChat", "arrowUp", "arrowDown", "apply2", "discard", "rerun", "close", "viewInChat", "run", "focus", "unstash", "cat", "apply1", "chat.rerun.label", "configure", "moveToNextHunk", "moveToPreviousHunk", "showChanges", "cat", "Keep", "Undo", "close2", "reveal", "cancel"]], ["vs/workbench/contrib/inlineChat/browser/inlineChatController", ["create.fail", "welcome.2", "empty", "err.apply", "err.discard"]], ["vs/workbench/contrib/inlineChat/browser/inlineChatCurrentLine", ["inlineChatShowingHint", "defaultTitle", "title2", "title1", "disableHint", "startWithCurrentLine", "showHint", "hideHint"]], ["vs/workbench/contrib/inlineChat/browser/inlineChatWidget", ["inlineChat.accessibilityHelp", "inlineChat.accessibilityHelpNoKb", "aria-label"]], ["vs/workbench/contrib/inlineChat/browser/inlineChatZoneWidget", ["inlineChatClosed"]], ["vs/workbench/contrib/inlineChat/common/inlineChat", ["finishOnType", "holdToSpeech", "accessibleDiffView", "accessibleDiffView.auto", "accessibleDiffView.on", "accessibleDiffView.off", "emptyLineHint", "lineSuffixHint", "enableV2", "hideOnRequest", "inlineChatHasPossible", "inlineChatHasProvider", "inlineChatHasEditsAgent", "inlineChatVisible", "inlineChatFocused", "inlineChatEditing", "inlineChatResponseFocused", "inlineChatEmpty", "inlineChatInnerCursorFirst", "inlineChatInnerCursorLast", "inlineChatOuterCursorPosition", "inlineChatHasStashedSession", "inlineChatChangeHasDiff", "inlineChatChangeShowsDiff", "inlineChatRequestInProgress", "inlineChatResponseTypes", "inlineChat.foreground", "inlineChat.background", "inlineChat.border", "inlineChat.shadow", "inlineChatInput.border", "inlineChatInput.focusBorder", "inlineChatInput.placeholderForeground", "inlineChatInput.background", "inlineChatDiff.inserted", "editorOverviewRuler.inlineChatInserted", "editorMinimap.inlineChatInserted", "inlineChatDiff.removed", "editorOverviewRuler.inlineChatRemoved"]], ["vs/workbench/contrib/inlineChat/electron-sandbox/inlineChatActions", ["holdForSpeech"]], ["vs/workbench/contrib/inlineCompletions/browser/inlineCompletionLanguageStatusBarContribution", ["inlineSuggestionLoading", "inlineCompletionAvailable", "inlineEditAvailable", "noInlineSuggestionAvailable", "inlineSuggestionsSmall", "inlineSuggestions"]], ["vs/workbench/contrib/interactive/browser/interactive.contribution", ["interactive.open", "interactiveScrollToTop", "interactiveScrollToBottom", "interactive.activeCodeBorder", "interactive.inactiveCodeBorder", "interactiveWindow.alwaysScrollOnNewCell", "interactiveWindow.promptToSaveOnClose", "interactiveWindow.executeWithShiftEnter", "interactiveWindow.showExecutionHint", "interactiveWindow", "interactive.open", "interactive.execute", "interactive.input.clear", "interactive.history.previous", "interactive.history.next", "interactive.input.focus", "interactive.history.focus"]], ["vs/workbench/contrib/interactive/browser/replInputHintContentWidget", ["emptyHintText", "ReplInputAriaLabelHelp", "ReplInputAriaLabelHelpNoKb", "disableHint"]], ["vs/workbench/contrib/issue/browser/baseIssueReporterService", ["selectExtension", "hide", "show", "vscodePlaceholder", "extensionPlaceholder", "marketplacePlaceholder", "undefinedPlaceholder", "acknowledge", "createOnGitHub", "previewOnGitHub", "loadingData", "rateLimited", "similarIssues", "open", "closed", "open", "closed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "featureRequest", "performanceIssue", "selectSource", "vscode", "extension", "marketplace", "unknown", "saveExtensionData", "handlesIssuesElsewhere", "elsewhereDescription", "openIssueReporter", "stepsToReproduce", "bugDescription", "stepsToReproduce", "performanceIssueDesciption", "description", "featureRequestDescription", "pasteData", "disabledExtensions"]], ["vs/workbench/contrib/issue/browser/issue.contribution", ["statusUnsupported"]], ["vs/workbench/contrib/issue/browser/issueFormService", ["confirmCloseIssueReporter", "yes", "cancel", "issueReporterWriteToClipboard", "ok", "cancel"]], ["vs/workbench/contrib/issue/browser/issueQuickAccess", ["reportExtensionMarketplace", "extensions", "contributedIssuePage"]], ["vs/workbench/contrib/issue/browser/issueReporterPage", ["sendSystemInfo", "sendProcessInfo", "sendWorkspaceInfo", "sendExtensions", "sendExperiments", "sendExtensionData", "acknowledgements", "reviewGuidanceLabel", "completeInEnglish", "issueTypeLabel", "issueSourceLabel", "issueSourceEmptyValidation", "disableExtensionsLabelText", "disableExtensions", "chooseExtension", "extensionWithNonstandardBugsUrl", "extensionWithNoBugsUrl", "issueTitleLabel", "issueTitleRequired", "titleEmptyValidation", "titleLengthValidation", "details", "descriptionEmptyValidation", "descriptionTooShortValidation", "show", "downloadExtensionData", "extensionData", "show", "show", "show", "show", "show"]], ["vs/workbench/contrib/issue/browser/issueReporterService", ["undefinedPlaceholder"]], ["vs/workbench/contrib/issue/browser/issueTroubleshoot", ["troubleshoot issue", "detail.start", "msg", "profile.extensions.disabled", "empty.profile", "issue is with configuration", "issue is in core", "I cannot reproduce", "This is Bad", "Stop", "troubleshoot issue", "use insiders", "troubleshoot issue", "download insiders", "report anyway", "ask to download insiders", "troubleshoot issue", "good", "bad", "stop", "ask to reproduce issue", "troubleshootIssue", "title.stop"]], ["vs/workbench/contrib/issue/common/issue.contribution", ["miReportIssue", "reportIssueInEnglish"]], ["vs/workbench/contrib/issue/electron-sandbox/issue.contribution", ["tasksQuickAccessPlaceholder", "openIssueReporter", "reportPerformanceIssue"]], ["vs/workbench/contrib/issue/electron-sandbox/issueReporterService", ["updateAvailable", "undefinedPlaceholder", "saveExtensionData", "pasteData", "noCurrentExperiments"]], ["vs/workbench/contrib/issue/electron-sandbox/process.contribution", ["miOpenProcessExplorerer", "stopTracing.message", "stopTracing.button", "stopTracing.title", "stopTracing.detail", "openProcessExplorer", "stopTracing"]], ["vs/workbench/contrib/keybindings/browser/keybindings.contribution", ["toggleKeybindingsLog"]], ["vs/workbench/contrib/languageDetection/browser/languageDetection.contribution", ["status.autoDetectLanguage", "langDetection.name", "langDetection.aria", "noDetection", "detectlang"]], ["vs/workbench/contrib/languageStatus/browser/languageStatus", ["langStatus.name", "langStatus.aria", "unpin", "pin", "aria.1", "aria.2", "name.pattern", "reset"]], ["vs/workbench/contrib/limitIndicator/browser/limitIndicator.contribution", ["status.button.configure", "colorDecoratorsStatusItem.name", "status.limitedColorDecorators.short", "colorDecoratorsStatusItem.source", "foldingRangesStatusItem.name", "status.limitedFoldingRanges.short", "foldingRangesStatusItem.source", "status.limited.details"]], ["vs/workbench/contrib/list/browser/listResizeColumnAction", ["list.resizeColumn", "list"]], ["vs/workbench/contrib/list/browser/tableColumnResizeQuickPick", ["table.column.selection", "table.column.resizeValue.placeHolder", "table.column.resizeValue.prompt", "table.column.resizeValue.invalidType", "table.column.resizeValue.invalidRange"]], ["vs/workbench/contrib/localHistory/browser/localHistory", ["localHistoryIcon", "localHistoryRestore"]], ["vs/workbench/contrib/localHistory/browser/localHistoryCommands", ["localHistoryRestore.source", "confirmRestoreMessage", "confirmRestoreDetail", "restoreButtonLabel", "unableToRestore", "restoreViaPicker.filePlaceholder", "restoreViaPicker.entryPlaceholder", "renameLocalHistoryEntryTitle", "renameLocalHistoryPlaceholder", "confirmDeleteMessage", "confirmDeleteDetail", "deleteButtonLabel", "confirmDeleteAllMessage", "confirmDeleteAllDetail", "deleteAllButtonLabel", "createLocalHistoryEntryTitle", "createLocalHistoryPlaceholder", "localHistoryEditorLabel", "localHistoryCompareToFileEditorLabel", "localHistoryCompareToPreviousEditorLabel", "localHistory.category", "localHistory.compareWithFile", "localHistory.compareWithPrevious", "localHistory.selectForCompare", "localHistory.compareWithSelected", "localHistory.open", "localHistory.restore", "localHistory.restoreViaPicker", "localHistory.restoreViaPickerMenu", "localHistory.rename", "localHistory.delete", "localHistory.deleteAll", "localHistory.create"]], ["vs/workbench/contrib/localHistory/browser/localHistoryTimeline", ["localHistory"]], ["vs/workbench/contrib/localHistory/electron-sandbox/localHistoryCommands", ["revealInWindows", "revealInMac", "openContainer"]], ["vs/workbench/contrib/localization/common/localization.contribution", ["vscode.extension.contributes.localizations", "vscode.extension.contributes.localizations.languageId", "vscode.extension.contributes.localizations.languageName", "vscode.extension.contributes.localizations.languageNameLocalized", "vscode.extension.contributes.localizations.translations", "vscode.extension.contributes.localizations.translations.id", "vscode.extension.contributes.localizations.translations.id.pattern", "vscode.extension.contributes.localizations.translations.path", "language id", "localizations language name", "localizations localized language name", "localizations"]], ["vs/workbench/contrib/localization/common/localizationsActions", ["chooseLocale", "installed", "available", "moreInfo", "configureLocale", "configureLocaleDescription", "clearDisplayLanguage"]], ["vs/workbench/contrib/localization/electron-sandbox/localization.contribution", ["updateLocale", "changeAndRestart", "neverAgain"]], ["vs/workbench/contrib/localization/electron-sandbox/minimalTranslations", ["showLanguagePackExtensions", "searchMarketplace", "installAndRestartMessage", "installAndRestart"]], ["vs/workbench/contrib/logs/common/logs.contribution", ["remote name", "setDefaultLogLevel", "show window log"]], ["vs/workbench/contrib/logs/common/logsActions", ["all", "extensionLogs", "loggers", "selectlog", "selectLogLevelFor", "selectLogLevel", "resetLogLevel", "default", "current", "sessions placeholder", "log placeholder", "setLogLevel", "openSessionLogFile"]], ["vs/workbench/contrib/logs/electron-sandbox/logsActions", ["openLogsFolder", "openExtensionLogsFolder"]], ["vs/workbench/contrib/markdown/browser/markdownSettingRenderer", ["viewInSettings", "viewInSettingsDetailed", "restorePreviousValue", "alreadysetBoolTrue", "alreadysetBoolFalse", "trueMessage", "falseMessage", "alreadysetString", "stringValue", "alreadysetNum", "numberValue", "changeSettingTitle", "copySettingId", "copySettingId"]], ["vs/workbench/contrib/markers/browser/markers.contribution", ["markersViewIcon", "<PERSON><PERSON><PERSON><PERSON>", "viewAsTree", "viewAsTable", "show errors", "problems", "show warnings", "problems", "show infos", "problems", "show active file", "problems", "show excluded files", "problems", "focusProblemsList", "focusProblemsFilter", "problems", "problems", "clearFiltersText", "problems", "collapseAll", "status.problems", "status.problemsVisibilityOff", "status.problemsVisibility", "totalErrors", "totalWarnings", "totalInfos", "noProblems", "manyProblems", "totalProblems", "viewAsTreeDescription", "viewAsTableDescription", "toggleErrorsDescription", "toggleWarningsDescription", "toggleInfosDescription", "toggleActiveFileDescription", "toggleExcludedFilesDescription", "copyMarker", "copyMessage", "copyMessage", "show multiline", "show singleline"]], ["vs/workbench/contrib/markers/browser/markersFileDecorations", ["label", "tooltip.1", "tooltip.N", "markers.showOnFile"]], ["vs/workbench/contrib/markers/browser/markersTable", ["codeColumnLabel", "messageColumnLabel", "fileColumnLabel", "sourceColumnLabel"]], ["vs/workbench/contrib/markers/browser/markersTreeViewer", ["<PERSON><PERSON><PERSON><PERSON>", "expandedIcon", "collapsedIcon", "single line", "multi line"]], ["vs/workbench/contrib/markers/browser/markersView", ["showing filtered problems", "No problems filtered", "problems filtered", "clearFilter"]], ["vs/workbench/contrib/markers/browser/messages", ["problems.view.toggle.label", "problems.panel.configuration.title", "problems.panel.configuration.autoreveal", "problems.panel.configuration.viewMode", "problems.panel.configuration.showCurrentInStatus", "problems.panel.configuration.compareOrder", "problems.panel.configuration.compareOrder.severity", "problems.panel.configuration.compareOrder.position", "markers.panel.no.problems.build", "markers.panel.no.problems.activeFile.build", "markers.panel.no.problems.filters", "markers.panel.action.moreFilters", "markers.panel.filter.showErrors", "markers.panel.filter.showWarnings", "markers.panel.filter.showInfos", "markers.panel.filter.useFilesExclude", "markers.panel.filter.activeFile", "markers.panel.action.filter", "markers.panel.action.quickfix", "markers.panel.filter.ariaLabel", "markers.panel.filter.placeholder", "markers.panel.filter.errors", "markers.panel.filter.warnings", "markers.panel.filter.infos", "markers.panel.single.error.label", "markers.panel.multiple.errors.label", "markers.panel.single.warning.label", "markers.panel.multiple.warnings.label", "markers.panel.single.info.label", "markers.panel.multiple.infos.label", "markers.panel.single.unknown.label", "markers.panel.multiple.unknowns.label", "markers.panel.at.ln.col.number", "problems.tree.aria.label.resource", "problems.tree.aria.label.marker.relatedInformation", "problems.tree.aria.label.error.marker", "problems.tree.aria.label.error.marker.nosource", "problems.tree.aria.label.warning.marker", "problems.tree.aria.label.warning.marker.nosource", "problems.tree.aria.label.info.marker", "problems.tree.aria.label.info.marker.nosource", "problems.tree.aria.label.marker", "problems.tree.aria.label.marker.nosource", "problems.tree.aria.label.relatedinfo.message", "errors.warnings.show.label", "problems.view.focus.label", "markers.panel.title.problems"]], ["vs/workbench/contrib/mcp/browser/mcpCommands", ["mcp.selectServer", "mcp.addServer", "mcp.addServer.description", "mcp.start", "mcp.stop", "mcp.restart", "mcp.showOutput", "mcp.config", "mcp.selectAction", "mcp.newTools", "mcp.toolError", "mcp.toolRefresh", "mcp.list", "mcp.options", "mcp.resetTrust", "mcp.resetCachedTools", "mcp.addConfiguration", "mcp.addConfiguration.description", "mcp.resetCachedTools", "mcp.editStoredInput", "mcp.command.showConfiguration", "mcp.command.showOutput", "mcp.command.restartServer", "mcp.command.startServer", "mcp.command.stopServer", "mcp.command.installFromActivation"]], ["vs/workbench/contrib/mcp/browser/mcpCommandsAddConfiguration", ["mcp.npm.title", "mcp.npm.placeholder", "mcp.serverType.npm", "mcp.serverType.npm.description", "mcp.pip.title", "mcp.pip.placeholder", "mcp.serverType.pip", "mcp.serverType.pip.description", "mcp.docker.title", "mcp.docker.placeholder", "mcp.serverType.docker", "mcp.serverType.docker.description", "mcp.serverType.command", "mcp.serverType.command.description", "mcp.serverType.http", "mcp.serverType.http.description", "mcp.serverType.manual", "mcp.serverType.copilot", "mcp.serverType.placeholder", "mcp.command.title", "mcp.command.placeholder", "mcp.url.title", "mcp.url.placeholder", "mcp.serverId.title", "mcp.serverId.placeholder", "mcp.target.user", "mcp.target.user.description", "mcp.target.remote", "mcp.target..remote.description", "mcp.target.workspace", "mcp.target.workspace.description", "mcp.target.title", "mcp.loading.title", "mcp.error.retry", "cancel", "mcp.confirmPublish", "allow", "cancel", "install.title", "install.start", "install.description", "install.show", "install.rename", "cancel", "install.error", "install.newName"]], ["vs/workbench/contrib/mcp/browser/mcpLanguageFeatures", ["mcp.variableNotFound", "server.error", "mcp.restart", "server.starting", "cancel", "server.running", "mcp.stop", "mcp.restart", "server.toolCount", "mcp.start", "server.toolCountCached", "edit", "clear", "clearAll"]], ["vs/workbench/contrib/mcp/common/discovery/extensionMcpDiscovery", ["invalidData", "invalidId", "<PERSON><PERSON><PERSON><PERSON>"]], ["vs/workbench/contrib/mcp/common/discovery/nativeMcpDiscoveryAbstract", ["onRemoteLabel"]], ["vs/workbench/contrib/mcp/common/mcpConfigPathsService", ["mcp.configuration.userLocalValue"]], ["vs/workbench/contrib/mcp/common/mcpConfiguration", ["mcp.discovery.source.claude-desktop", "mcp.discovery.source.windsurf", "mcp.discovery.source.cursor-global", "mcp.discovery.source.cursor-workspace", "app.mcp.json.type", "app.mcp.json.command", "app.mcp.args.command", "app.mcp.envFile.command", "app.mcp.env.command", "app.mcp.json.title", "app.mcp.json.type", "app.mcp.json.url", "app.mcp.json.headers", "vscode.extension.contributes.mcp", "vscode.extension.contributes.mcp.id", "vscode.extension.contributes.mcp.label"]], ["vs/workbench/contrib/mcp/common/mcpContextKeys", ["mcp.serverCount.description", "mcp.hasUnknownTools.description", "mcp.hasServersWithErrors.description", "mcp.toolsCount.description"]], ["vs/workbench/contrib/mcp/common/mcpRegistry", ["trustTitleWithOrigin", "mcp.trust.details", "mcp.trust.yes", "mcp.trust.no", "mcp.launchError", "mcp.launchError.openConfig"]], ["vs/workbench/contrib/mcp/common/mcpServer", ["mcp.command.showOutput", "mcpServerInstall", "mcpServerNotFound", "mcpServerError", "mcpBadSchema.tool", "mcpBadSchema", "mcpBadSchema.show"]], ["vs/workbench/contrib/mcp/common/mcpServerConnection", ["mcpServer.starting", "mcpServer.state", "mcpServer.stopping"]], ["vs/workbench/contrib/mcp/common/mcpService", ["mcp.tool.warning", "msg.subtitle", "msg.title", "msg.msg", "msg.run", "msg.ran"]], ["vs/workbench/contrib/mcp/common/mcpTypes", ["mcpstate.stopped", "mcpstate.starting", "mcpstate.running", "mcpstate.error"]], ["vs/workbench/contrib/mergeEditor/browser/commands/commands", ["mergeEditor.compareWithBase", "mergeEditor.compareWithBase", "mergeEditor.resetResultToBaseAndAutoMerge.short", "mergeEditor.acceptMerge.unhandledConflicts.message", "mergeEditor.acceptMerge.unhandledConflicts.detail", "mergeEditor.acceptMerge.unhandledConflicts.accept", "title", "layout.mixed", "layout.column", "showNonConflictingChanges", "layout.showBase", "layout.showBaseTop", "layout.showBaseCenter", "mergeEditor", "openfile", "merge.goToNextUnhandledConflict", "merge.goToPreviousUnhandledConflict", "merge.toggleCurrentConflictFromLeft", "merge.toggleCurrentConflictFromRight", "mergeEditor.compareInput1WithBase", "mergeEditor.compareInput2WithBase", "merge.openBaseEditor", "merge.acceptAllInput1", "merge.acceptAllInput2", "mergeEditor.resetResultToBaseAndAutoMerge", "mergeEditor.resetChoice", "mergeEditor.acceptAllCombination", "mergeEditor.acceptMerge", "mergeEditor.toggleBetweenInputs"]], ["vs/workbench/contrib/mergeEditor/browser/commands/devCommands", ["mergeEditor.name", "mergeEditor.noActiveMergeEditor", "mergeEditor.name", "mergeEditor.successfullyCopiedMergeEditorContents", "mergeEditor.name", "mergeEditor.noActiveMergeEditor", "mergeEditor.selectFolderToSaveTo", "mergeEditor.name", "mergeEditor.successfullySavedMergeEditorContentsToFolder", "mergeEditor.selectFolderToSaveTo", "mergeEditor", "merge.dev.copyState", "merge.dev.saveContentsToFolder", "merge.dev.loadContentsFromFolder"]], ["vs/workbench/contrib/mergeEditor/browser/mergeEditor.contribution", ["name", "diffAlgorithm.legacy", "diffAlgorithm.advanced"]], ["vs/workbench/contrib/mergeEditor/browser/mergeEditorAccessibilityHelp", ["msg1", "msg2", "msg3", "msg4", "msg5"]], ["vs/workbench/contrib/mergeEditor/browser/mergeEditorInput", ["name", "mergeEditor.input1", "mergeEditor.input2", "mergeEditor.result"]], ["vs/workbench/contrib/mergeEditor/browser/mergeEditorInputModel", ["messageN", "message1", "saveWithConflict", "save", "discard", "detailNConflicts", "detail1Conflicts", "detailN", "detail1", "saveTempFile.message", "saveTempFile.detail", "acceptMerge", "merge-editor.source", "workspace.messageN", "workspace.message1", "workspace.detailN.unhandled", "workspace.detail1.unhandled", "workspace.detailN.handled", "workspace.detail1.handled", "workspace.saveWithConflict", "workspace.save", "workspace.doNotSave", "workspace.messageN.nonDirty", "workspace.message1.nonDirty", "workspace.detailN.unhandled.nonDirty", "workspace.detail1.unhandled.nonDirty", "workspace.closeWithConflicts", "workspace.close", "no<PERSON><PERSON><PERSON><PERSON>n"]], ["vs/workbench/contrib/mergeEditor/browser/mergeMarkers/mergeMarkersController", ["conflictingLine", "conflictingLines"]], ["vs/workbench/contrib/mergeEditor/browser/model/mergeEditorModel", ["setInputHandled", "undoMarkAsHandled"]], ["vs/workbench/contrib/mergeEditor/browser/view/colors", ["mergeEditor.change.background", "mergeEditor.change.word.background", "mergeEditor.changeBase.background", "mergeEditor.changeBase.word.background", "mergeEditor.conflict.unhandledUnfocused.border", "mergeEditor.conflict.unhandledFocused.border", "mergeEditor.conflict.handledUnfocused.border", "mergeEditor.conflict.handledFocused.border", "mergeEditor.conflict.handled.minimapOverViewRuler", "mergeEditor.conflict.unhandled.minimapOverViewRuler", "mergeEditor.conflictingLines.background", "mergeEditor.conflict.input1.background", "mergeEditor.conflict.input2.background"]], ["vs/workbench/contrib/mergeEditor/browser/view/conflictActions", ["accept", "acceptTooltip", "acceptBoth0First", "acceptBoth", "acceptBothTooltip", "append", "appendTooltip", "combine", "acceptBothTooltip", "ignore", "markAsHandledTooltip", "manualResolution", "manualResolutionTooltip", "noC<PERSON>esAccepted", "noChangesAcceptedTooltip", "remove", "removeTooltip", "remove", "removeTooltip", "resetToBase", "resetToBaseTooltip"]], ["vs/workbench/contrib/mergeEditor/browser/view/editors/baseCodeEditorView", ["base", "compareWith", "compareWithTooltip"]], ["vs/workbench/contrib/mergeEditor/browser/view/editors/inputCodeEditorView", ["input1", "input2", "mergeEditor.accept", "mergeEditor.accept", "mergeEditor.acceptBoth", "mergeEditor.swap", "mergeEditor.markAsHandled", "accept.excluded", "accept.conflicting", "accept.first", "accept.second"]], ["vs/workbench/contrib/mergeEditor/browser/view/editors/resultCodeEditorView", ["result", "mergeEditor.remainingConflicts", "mergeEditor.remainingConflict", "goToNextConflict", "allConflictHandled"]], ["vs/workbench/contrib/mergeEditor/browser/view/mergeEditor", ["mergeEditor"]], ["vs/workbench/contrib/mergeEditor/browser/view/viewModel", ["noConflictMessage"]], ["vs/workbench/contrib/mergeEditor/common/mergeEditor", ["is", "isr", "editor<PERSON><PERSON>out", "showBase", "showBaseAtTop", "showNonConflictingChanges", "baseUri", "result<PERSON>ri"]], ["vs/workbench/contrib/mergeEditor/electron-sandbox/devCommands", ["mergeEditor.enterJSON", "mergeEditor", "merge.dev.openState", "merge.dev.openSelectionInTemporaryMergeEditor"]], ["vs/workbench/contrib/multiDiffEditor/browser/actions", ["goToFile", "collapseAllDiffs", "ExpandAllDiffs"]], ["vs/workbench/contrib/multiDiffEditor/browser/icons.contribution", ["multiDiffEditorLabelIcon"]], ["vs/workbench/contrib/multiDiffEditor/browser/multiDiffEditor.contribution", ["name"]], ["vs/workbench/contrib/multiDiffEditor/browser/multiDiffEditorInput", ["name", "nameWithOneFile", "nameWithFiles"]], ["vs/workbench/contrib/multiDiffEditor/browser/scmMultiDiffSourceResolver", ["openChanges"]], ["vs/workbench/contrib/notebook/browser/contrib/cellCommands/cellCommands", ["notebookActions.toggleOutputs", "notebookActions.moveCellUp", "notebookActions.moveCellDown", "notebookActions.copyCellUp", "notebookActions.copyCellDown", "notebookActions.splitCell", "notebookActions.joinCellAbove", "notebookActions.joinCellBelow", "notebookActions.joinSelectedCells", "notebookActions.changeCellToCode", "notebookActions.changeCellToMarkdown", "notebookActions.collapseCellInput", "notebookActions.expandCellInput", "notebookActions.collapseCellOutput", "notebookActions.expandCellOutput", "notebookActions.toggleOutputs", "notebookActions.collapseAllCellInput", "notebookActions.expandAllCellInput", "notebookActions.collapseAllCellOutput", "notebookActions.expandAllCellOutput", "notebookActions.toggleScrolling"]], ["vs/workbench/contrib/notebook/browser/contrib/cellDiagnostics/cellDiagnosticsActions", ["cellCommands.quickFix.noneMessage", "notebookActions.cellFailureActions", "notebookActions.chatFixCellError", "notebookActions.chatExplainCellError"]], ["vs/workbench/contrib/notebook/browser/contrib/cellDiagnostics/diagnosticCellStatusBarContrib", ["notebook.cell.status.diagnostic"]], ["vs/workbench/contrib/notebook/browser/contrib/cellStatusBar/executionStatusBarItemController", ["notebook.cell.status.success", "notebook.cell.status.failed", "notebook.cell.status.pending", "notebook.cell.status.executing", "notebook.cell.statusBar.timerTooltip.reportIssueFootnote", "notebook.cell.statusBar.timerTooltip", "notebook.cell.statusBar.timerVerbose"]], ["vs/workbench/contrib/notebook/browser/contrib/cellStatusBar/statusBarProviders", ["notebook.cell.status.searchLanguageExtensions", "notebook.cell.status.language", "notebook.cell.status.autoDetectLanguage"]], ["vs/workbench/contrib/notebook/browser/contrib/chat/notebookChatUtils", ["notebookOutputCellLabel"]], ["vs/workbench/contrib/notebook/browser/contrib/clipboard/notebookClipboard", ["notebookActions.copy", "notebookActions.cut", "notebookActions.paste", "notebookActions.pasteAbove", "notebook.cell.output.selectAll", "toggleNotebookClipboardLog"]], ["vs/workbench/contrib/notebook/browser/contrib/editorStatusBar/editorStatusBar", ["notebook.info", "tooltop", "notebook.select", "kernel.select.label", "kernel.select.label", "notebook.activeCellStatusName", "notebook.multiActiveCellIndicator", "notebook.singleActiveCellIndicator", "notebook.indentation", "selectNotebookIndentation"]], ["vs/workbench/contrib/notebook/browser/contrib/find/notebookFind", ["notebookActions.hideFind", "notebookActions.findInNotebook"]], ["vs/workbench/contrib/notebook/browser/contrib/find/notebookFindReplaceWidget", ["label.find", "placeholder.find", "label.previousMatchButton", "label.nextMatchButton", "label.toggleSelectionFind", "label.closeButton", "label.toggleReplaceButton", "label.replace", "placeholder.replace", "label.replaceButton", "label.replaceAllButton", "findFilterIcon", "notebook.find.filter.filterAction", "notebook.find.filter.findInMarkupInput", "notebook.find.filter.findInMarkupPreview", "notebook.find.filter.findInCodeInput", "notebook.find.filter.findInCodeOutput"]], ["vs/workbench/contrib/notebook/browser/contrib/find/notebookFindWidget", ["ariaSearchNoResultEmpty", "ariaSearchNoResult", "ariaSearchNoResultWithLineNumNoCurrentMatch"]], ["vs/workbench/contrib/notebook/browser/contrib/format/formatting", ["label", "formatCells.label", "format.title", "formatCell.label"]], ["vs/workbench/contrib/notebook/browser/contrib/gettingStarted/notebookGettingStarted", ["workbench.notebook.layout.gettingStarted.label"]], ["vs/workbench/contrib/notebook/browser/contrib/layout/layoutActions", ["notebook.toggleCellToolbarPosition"]], ["vs/workbench/contrib/notebook/browser/contrib/multicursor/notebookMulticursor", ["selectAllFindMatches", "addFindMatchToSelection", "exitMultiSelection", "deleteLeftMultiSelection", "deleteRightMultiSelection"]], ["vs/workbench/contrib/notebook/browser/contrib/navigation/arrow", ["notebook.cell.webviewHandledEvents", "cursorMoveDown", "cursorMoveUp", "focusFirstCell", "focusLastCell", "focusOutput", "focusOutputOut", "notebookActions.centerActiveCell", "cursorPageUp", "cursorPageUpSelect", "cursorPageDown", "cursorPageDownSelect", "notebook.navigation.allowNavigateToSurroundingCells"]], ["vs/workbench/contrib/notebook/browser/contrib/notebookVariables/notebookInlineVariables", ["clearAllInlineValues"]], ["vs/workbench/contrib/notebook/browser/contrib/notebookVariables/notebookVariableCommands", ["copyWorkspaceVariableValue", "executeNotebookVariableProvider"]], ["vs/workbench/contrib/notebook/browser/contrib/notebookVariables/notebookVariables", ["notebookVariables"]], ["vs/workbench/contrib/notebook/browser/contrib/notebookVariables/notebookVariablesDataSource", ["notebook.indexedChildrenLimitReached"]], ["vs/workbench/contrib/notebook/browser/contrib/notebookVariables/notebookVariablesTree", ["debugConsole", "notebookVariableAriaLabel"]], ["vs/workbench/contrib/notebook/browser/contrib/notebookVariables/notebookVariablesView", ["notebook.notebookVariables", "notebook.ReplVariables"]], ["vs/workbench/contrib/notebook/browser/contrib/outline/notebookOutline", ["outline.showMarkdownHeadersOnly", "outline.showCodeCells", "outline.showCodeCellSymbols", "breadcrumbs.showCodeCells", "notebook.gotoSymbols.showAllSymbols", "filter", "toggleShowMarkdownHeadersOnly", "toggleCodeCells", "toggleCodeCellSymbols"]], ["vs/workbench/contrib/notebook/browser/contrib/profile/notebookProfile", ["setProfileTitle"]], ["vs/workbench/contrib/notebook/browser/contrib/saveParticipants/saveParticipants", ["notebookFormatSave.formatting", "formatNotebook", "trimNotebookWhitespace", "trimNotebookNewlines", "insertFinalNewLine", "notebookSaveParticipants.notebookCodeActions", "notebookSaveParticipants.cellCodeActions", "notebookSaveParticipants.formatCodeActions", "codeaction.get2", "codeAction.apply", "codeaction.get2", "codeAction.apply"]], ["vs/workbench/contrib/notebook/browser/contrib/troubleshoot/layout", ["workbench.notebook.toggleLayoutTroubleshoot", "workbench.notebook.inspectLayout", "workbench.notebook.clearNotebookEdtitorTypeCache"]], ["vs/workbench/contrib/notebook/browser/controller/cellOperations", ["notebookActions.joinSelectedCells", "notebookActions.joinSelectedCells.label"]], ["vs/workbench/contrib/notebook/browser/controller/cellOutputActions", ["notebookActions.showAllOutput", "notebookActions.copyOutput", "notebookActions.openOutputInEditor", "notebookActions.openOutputInNotebookOutputEditor"]], ["vs/workbench/contrib/notebook/browser/controller/chat/cellChatActions", ["arrowUp", "arrowDown", "focusChatWidget", "focusNextChatWidget", "apply2", "apply3", "discard", "notebookActions.menu.insertCodeCellWithChat", "notebookActions.menu.insertCodeCellWithChat.tooltip", "notebookActions.menu.insertCodeCellWithChat.tooltip", "notebookActions.menu.insertCodeCellWithChat", "notebookActions.menu.insertCodeCellWithChat.tooltip", "notebookActions.menu.insertCode.ontoolbar", "notebookActions.menu.insertCode.tooltip", "focusNotebookChat", "focusNextCell", "focusPreviousCell", "notebook.apply2", "notebook.apply3", "notebook.cell.chat.accept", "notebook.cell.chat.stop", "notebook.cell.chat.close", "apply1", "notebook.cell.chat.previousFromHistory", "notebook.cell.chat.nextFromHistory", "notebookActions.restoreCellprompt", "notebook.apply1"]], ["vs/workbench/contrib/notebook/browser/controller/chat/notebook.chat.contribution", ["pickKernelVariableLabel", "notebookActions.addOutputToChat"]], ["vs/workbench/contrib/notebook/browser/controller/chat/notebookChatContext", ["notebookCellChatFocused", "notebookChatHasActiveRequest", "notebookChatUserDidEdit", "notebookChatOuterFocusPosition", "notebookChatAgentRegistered"]], ["vs/workbench/contrib/notebook/browser/controller/chat/notebookChatController", ["default.placeholder", "welcome.1"]], ["vs/workbench/contrib/notebook/browser/controller/coreActions", ["notebookMenu.insertCell", "notebookMenu.cellTitle", "miShare", "notebookActions.category"]], ["vs/workbench/contrib/notebook/browser/controller/editActions", ["notebookActions.editCell", "notebookActions.quitEdit", "notebookActions.deleteCell", "confirmDeleteButton", "confirmDeleteButtonMessage", "doNotAskAgain", "clearCellOutputs", "clearAllCellsOutputs", "changeLanguage", "changeLanguage", "languageDescription", "languageDescriptionConfigured", "autoDetect", "languagesPicks", "pickLanguageToConfigure", "noDetection", "noNotebookEditor", "noWritableCodeEditor", "indentConvert", "indentView", "pickAction", "commentSelectedCells", "detectLanguage", "selectNotebookIndentation"]], ["vs/workbench/contrib/notebook/browser/controller/executeActions", ["notebookActions.renderMarkdown", "notebookActions.executeNotebook", "notebookActions.executeNotebook", "notebookActions.execute", "notebookActions.execute", "notebookActions.executeAbove", "notebookActions.executeBelow", "notebookActions.executeAndFocusContainer", "notebookActions.executeAndFocusContainer", "notebookActions.cancel", "notebookActions.cancel", "notebookActions.executeAndSelectBelow", "notebookActions.executeAndInsertBelow", "revealRunningCellShort", "revealRunningCell", "revealRunningCell", "revealRunningCell", "revealLastFailedCell", "revealLastFailedCell", "revealLastFailedCellShort", "notebookActions.cancelNotebook", "notebookActions.interruptNotebook"]], ["vs/workbench/contrib/notebook/browser/controller/foldingController", ["fold.cell", "unfold.cell", "fold.cell"]], ["vs/workbench/contrib/notebook/browser/controller/insertCellActions", ["notebookActions.insertCodeCellAbove", "notebookActions.insertCodeCellAboveAndFocusContainer", "notebookActions.insertCodeCellBelow", "notebookActions.insertCodeCellBelowAndFocusContainer", "notebookActions.insertMarkdownCellAbove", "notebookActions.insertMarkdownCellBelow", "notebookActions.insertCodeCellAtTop", "notebookActions.insertMarkdownCellAtTop", "notebookActions.menu.insertCode", "notebookActions.menu.insertCode.tooltip", "notebookActions.menu.insertCode.minimalToolbar", "notebookActions.menu.insertCode.tooltip", "notebookActions.menu.insertCode.ontoolbar", "notebookActions.menu.insertCode.tooltip", "notebookActions.menu.insertCode", "notebookActions.menu.insertCode.tooltip", "notebookActions.menu.insertCode.minimaltoolbar", "notebookActions.menu.insertCode.tooltip", "notebookActions.menu.insertMarkdown", "notebookActions.menu.insertMarkdown.tooltip", "notebookActions.menu.insertMarkdown.ontoolbar", "notebookActions.menu.insertMarkdown.tooltip", "notebookActions.menu.insertMarkdown", "notebookActions.menu.insertMarkdown.tooltip"]], ["vs/workbench/contrib/notebook/browser/controller/layoutActions", ["notebook.showLineNumbers", "notebook.placeholder", "saveTarget.machine", "saveTarget.workspace", "mitoggleNotebookStickyScroll", "notebookStickyScroll", "mitoggleNotebookStickyScroll", "workbench.notebook.layout.select.label", "workbench.notebook.layout.configure.label", "workbench.notebook.layout.configure.label", "customizeNotebook", "notebook.toggleLineNumbers", "notebook.toggleCellToolbarPosition", "notebook.toggleBreadcrumb", "notebook.saveMimeTypeOrder", "workbench.notebook.layout.webview.reset.label", "toggleStickyScroll"]], ["vs/workbench/contrib/notebook/browser/controller/notebookIndentationActions", ["indentUsingTabs", "indentUsingSpaces", "changeTabDisplaySize", "convertIndentationToSpaces", "convertIndentationToTabs", "selectTabWidth", "convertIndentation"]], ["vs/workbench/contrib/notebook/browser/controller/sectionActions", ["<PERSON><PERSON><PERSON><PERSON>", "runCell", "mirunCellsInSection", "runCellsInSection", "mifoldSection", "foldSection", "miexpandSection", "expandSection", "runCell", "runCellsInSection", "foldSection", "expandSection"]], ["vs/workbench/contrib/notebook/browser/controller/variablesActions", ["notebookActions.openVariablesView"]], ["vs/workbench/contrib/notebook/browser/diff/diffComponents", ["hiddenCell", "hidden<PERSON>ells", "showUnchangedCells", "hide<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]], ["vs/workbench/contrib/notebook/browser/diff/diffElementOutputs", ["mimeTypePicker", "empty", "noRenderer.2", "curruentActiveMimeType", "promptChooseMimeTypeInSecure.placeHolder", "promptChooseMimeType.placeHolder", "builtinRenderInfo"]], ["vs/workbench/contrib/notebook/browser/diff/notebookDiffActions", ["notebook.diff.revertMetadata", "notebook.diff.cell.revertInput", "notebook.diff.cell.revertOutputs", "notebook.diff.cell.revertMetadata", "notebook.diff.cell.switchOutputRenderingStyleToText", "notebook.diff.cell.revertOutputs", "ignoreTrimWhitespace.label", "notebook.diff.action.previous.title", "notebook.diff.action.next.title", "notebook.diff.inline.toggle.title", "notebook.diff.ignoreMetadata", "notebook.diff.ignoreOutputs", "notebook.diff.toggleInline", "notebook.diff.openFile", "notebook.diff.cell.toggleCollapseUnchangedRegions", "notebook.diff.switchToText", "showUnchangedCells", "hide<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goToCell", "notebook.diff.showOutputs", "notebook.diff.showMetadata"]], ["vs/workbench/contrib/notebook/browser/diff/notebookDiffEditor", ["notebookTreeAriaLabel"]], ["vs/workbench/contrib/notebook/browser/diff/notebookDiffEditorBrowser", ["notebook.diffEditor.allCollapsed", "notebook.diffEditor.hasUnchangedCells", "notebook.diffEditor.unchangedCellsAreHidden", "notebook.diffEditor.item.kind", "notebook.diffEditor.item.state"]], ["vs/workbench/contrib/notebook/browser/diff/notebookDiffList", ["notebook.diff.hiddenCells.expandAll"]], ["vs/workbench/contrib/notebook/browser/diff/notebookMultiDiffEditor", ["notebookCellLabel", "notebookCellMetadataLabel", "notebookCellOutputLabel"]], ["vs/workbench/contrib/notebook/browser/notebook.contribution", ["notebook.editorOptions.experimentalCustomization", "notebookConfigurationTitle", "notebook.displayOrder.description", "notebook.cellToolbarLocation.description", "notebook.cellToolbarLocation.viewType", "notebook.showCellStatusbar.description", "notebook.showCellStatusbar.hidden.description", "notebook.showCellStatusbar.visible.description", "notebook.showCellStatusbar.visibleAfterExecute.description", "notebook.cellExecutionTimeVerbosity.description", "notebook.cellExecutionTimeVerbosity.default.description", "notebook.cellExecutionTimeVerbosity.verbose.description", "notebook.diff.enablePreview.description", "notebook.diff.enableOverviewRuler.description", "notebook.cellToolbarVisibility.description", "notebook.undoRedoPerCell.description", "notebook.compactView.description", "notebook.focusIndicator.description", "notebook.insertToolbarPosition.description", "insertToolbarLocation.betweenCells", "insertToolbarLocation.notebookToolbar", "insertToolbarLocation.both", "insertToolbarLocation.hidden", "notebook.globalToolbar.description", "notebook.stickyScrollEnabled.description", "notebook.stickyScrollMode.description", "notebook.stickyScrollMode.flat", "notebook.stickyScrollMode.indented", "notebook.consolidatedOutputButton.description", "notebook.showFoldingControls.description", "showFoldingControls.always", "showFoldingControls.never", "showFoldingControls.mouseover", "notebook.dragAndDrop.description", "notebook.consolidatedRunButton.description", "notebook.globalToolbarShowLabel", "notebook.textOutputLineLimit", "notebook.disableOutputFilePathLinks", "notebook.minimalErrorRendering", "notebook.markup.fontSize", "notebook.markdown.lineHeight", "notebook.interactiveWindow.collapseCodeCells", "notebook.outputLineHeight", "notebook.outputFontSize", "notebook.outputFontFamily", "notebook.outputScrolling", "notebook.outputWordWrap", "notebookFormatter.default", "notebook.formatOnSave", "notebook.insertFinalNewline", "notebook.formatOnCellExecution", "notebook.confirmDeleteRunningCell", "notebook.findFilters", "notebook.remoteSaving", "notebook.scrolling.revealNextCellOnExecute.description", "notebook.scrolling.revealNextCellOnExecute.fullCell.description", "notebook.scrolling.revealNextCellOnExecute.firstLine.description", "notebook.scrolling.revealNextCellOnExecute.none.description", "notebook.cellGenerate", "notebook.VariablesView.description", "notebook.inlineValues.description", "notebook.inlineValues.on", "notebook.inlineValues.auto", "notebook.inlineValues.off", "notebook.cellFailureDiagnostics", "notebook.backup.sizeLimit", "notebook.multiCursor.enabled", "notebook.markup.fontFamily"]], ["vs/workbench/contrib/notebook/browser/notebookAccessibilityHelp", ["notebook.overview", "notebook.cell.edit", "notebook.cell.quitEdit", "notebook.cell.focusInOutput", "notebook.focusNextEditor", "notebook.focusPreviousEditor", "notebook.cellNavigation", "notebook.cell.executeAndFocusContainer", "notebook.cell.insertCodeCellBelowAndFocusContainer", "notebook.changeCellType"]], ["vs/workbench/contrib/notebook/browser/notebookAccessibilityProvider", ["replHistoryTreeAriaLabel", "notebookTreeAriaLabel", "notebookTreeAriaLabelHelp", "notebookTreeAriaLabelHelpNoKb"]], ["vs/workbench/contrib/notebook/browser/notebookEditor", ["fail.noEditor", "fail.noEditor.extensionMissing", "notebookOpenEnableMissingViewType", "notebookOpenInstallMissingViewType", "notebookOpenAsText", "notebookTooLargeForHeapErrorWithSize", "notebookTooLargeForHeapErrorWithoutSize", "notebookOpenInTextEditor"]], ["vs/workbench/contrib/notebook/browser/notebookEditorWidget", ["notebook.cellBorderColor", "notebook.focusedEditorBorder", "notebookStatusSuccessIcon.foreground", "notebookEditorOverviewRuler.runningCellForeground", "notebookStatusErrorIcon.foreground", "notebookStatusRunningIcon.foreground", "notebook.outputContainerBorderColor", "notebook.outputContainerBackgroundColor", "notebook.cellToolbarSeparator", "focusedCellBackground", "selectedCellBackground", "notebook.cellHoverBackground", "notebook.selectedCellBorder", "notebook.inactiveSelectedCellBorder", "notebook.focusedCellBorder", "notebook.inactiveFocusedCellBorder", "notebook.cellStatusBarItemHoverBackground", "notebook.cellInsertionIndicator", "notebookScrollbarSliderBackground", "notebookScrollbarSliderHoverBackground", "notebookScrollbarSliderActiveBackground", "notebook.symbolHighlightBackground", "notebook.cellEditorBackground", "notebook.editorBackground"]], ["vs/workbench/contrib/notebook/browser/notebookExtensionPoint", ["contributes.notebook.provider", "contributes.notebook.provider.viewType", "contributes.notebook.provider.displayName", "contributes.notebook.provider.selector", "contributes.notebook.provider.selector.filenamePattern", "contributes.notebook.selector.provider.excludeFileNamePattern", "contributes.priority", "contributes.priority.default", "contributes.priority.option", "contributes.notebook.renderer", "contributes.notebook.renderer.viewType", "contributes.notebook.renderer.displayName", "contributes.notebook.renderer.hardDependencies", "contributes.notebook.renderer.optionalDependencies", "contributes.notebook.renderer.requiresMessaging.always", "contributes.notebook.renderer.requiresMessaging.optional", "contributes.notebook.renderer.requiresMessaging.never", "contributes.notebook.renderer.requiresMessaging", "contributes.notebook.selector", "contributes.notebook.renderer.entrypoint", "contributes.notebook.renderer.entrypoint", "contributes.notebook.renderer.entrypoint.extends", "contributes.notebook.renderer.entrypoint", "contributes.preload.provider", "contributes.preload.provider.viewType", "contributes.preload.entrypoint", "contributes.preload.localResourceRoots", "Notebook id", "Notebook name", "Notebook renderer name", "Notebook mimetypes", "notebooks", "notebook<PERSON><PERSON><PERSON>"]], ["vs/workbench/contrib/notebook/browser/notebookIcons", ["selectKernelIcon", "executeIcon", "executeAboveIcon", "executeBelowIcon", "stopIcon", "deleteCellIcon", "executeAllIcon", "editIcon", "stopEditIcon", "moveUpIcon", "moveDownIcon", "clearIcon", "splitCellIcon", "successStateIcon", "errorStateIcon", "pendingStateIcon", "executingStateIcon", "collapsedIcon", "expandedIcon", "openAsTextIcon", "revertIcon", "toggleWhitespace", "renderOutputIcon", "mimetypeIcon", "copyIcon", "previousChangeIcon", "nextChangeIcon", "variablesViewIcon"]], ["vs/workbench/contrib/notebook/browser/outputEditor/notebookOutputEditor", ["notebookOutputEditor", "empty", "noRenderer.2"]], ["vs/workbench/contrib/notebook/browser/outputEditor/notebookOutputEditorInput", ["notebookOutputEditorInput"]], ["vs/workbench/contrib/notebook/browser/services/notebookExecutionServiceImpl", ["notebookRunTrust"]], ["vs/workbench/contrib/notebook/browser/services/notebookKernelHistoryServiceImpl", ["workbench.notebook.clearNotebookKernelsMRUCache"]], ["vs/workbench/contrib/notebook/browser/services/notebookKeymapServiceImpl", ["disableOtherKeymapsConfirmation", "yes", "no"]], ["vs/workbench/contrib/notebook/browser/services/notebookLoggingServiceImpl", ["renderChannelName"]], ["vs/workbench/contrib/notebook/browser/services/notebookServiceImpl", ["notebookOpenInstallMissingViewType"]], ["vs/workbench/contrib/notebook/browser/view/cellParts/cellEditorOptions", ["notebook.lineNumbers", "notebook.showLineNumbers", "notebook.cell.toggleLineNumbers.title", "notebook.toggleLineNumbers"]], ["vs/workbench/contrib/notebook/browser/view/cellParts/cellOutput", ["empty", "noRenderer.2", "pickMimeType", "curruentActiveMimeType", "installJupyterPrompt", "promptChooseMimeTypeInSecure.placeHolder", "promptChooseMimeType.placeHolder", "unavailableRenderInfo"]], ["vs/workbench/contrib/notebook/browser/view/cellParts/codeCell", ["cellExpandInputButtonLabelWithDoubleClick", "cellExpandInputButtonLabel"]], ["vs/workbench/contrib/notebook/browser/view/cellParts/codeCellExecutionIcon", ["notebook.cell.status.success", "notebook.cell.status.failure", "notebook.cell.status.pending", "notebook.cell.status.executing"]], ["vs/workbench/contrib/notebook/browser/view/cellParts/codeCellRunToolbar", ["notebook.moreRunActionsLabel"]], ["vs/workbench/contrib/notebook/browser/view/cellParts/collapsedCellOutput", ["cellOutputsCollapsedMsg", "cellExpandOutputButtonLabelWithDoubleClick", "cellExpandOutputButtonLabel"]], ["vs/workbench/contrib/notebook/browser/view/cellParts/foldedCellHint", ["hiddenCellsLabel", "hiddenCellsLabelPlural"]], ["vs/workbench/contrib/notebook/browser/view/cellParts/markupCell", ["cellExpandInputButtonLabelWithDoubleClick", "cellExpandInputButtonLabel"]], ["vs/workbench/contrib/notebook/browser/view/renderers/backLayerWebView", ["notebook.emptyMarkdownPlaceholder", "notebook.error.rendererNotFound", "notebook.error.rendererFallbacksExhausted", "webview title"]], ["vs/workbench/contrib/notebook/browser/view/renderers/cellRenderer", ["cellExecutionOrderCountLabel"]], ["vs/workbench/contrib/notebook/browser/viewModel/notebookOutlineEntryFactory", ["empty"]], ["vs/workbench/contrib/notebook/browser/viewParts/notebookCellOverlays", ["workbench.notebook.developer.addCellOverlays"]], ["vs/workbench/contrib/notebook/browser/viewParts/notebookKernelQuickPickStrategy", ["current1", "current2", "prompt.placeholder.change", "prompt.placeholder.select", "installSuggestedKernel", "searchForKernels", "selectAnotherKernel.more", "select", "selectAnotherKernel", "selectKernel.placeholder", "learnMoreTooltip", "selectKernelFromExtension", "kernels.selectedKernelAndKernelDetectionRunning", "kernels.detecting", "kernels.detecting", "select"]], ["vs/workbench/contrib/notebook/browser/viewParts/notebookKernelView", ["notebookActions.selectKernel.args", "notebookActions.selectKernel"]], ["vs/workbench/contrib/notebook/browser/viewParts/notebookViewZones", ["workbench.notebook.developer.addViewZones"]], ["vs/workbench/contrib/notebook/common/notebookEditorInput", ["vetoAutoExtHostRestart", "vetoExtHostRestart"]], ["vs/workbench/contrib/outline/browser/outline.contribution", ["outlineViewIcon", "outlineConfigurationTitle", "outline.showIcons", "outline.initialState", "outline.initialState.collapsed", "outline.initialState.expanded", "outline.showProblem", "outline.problem.colors", "outline.problems.badges", "filteredTypes.file", "filteredTypes.module", "filteredTypes.namespace", "filteredTypes.package", "filteredTypes.class", "filteredTypes.method", "filteredTypes.property", "filteredTypes.field", "filteredTypes.constructor", "filteredTypes.enum", "filteredTypes.interface", "filteredTypes.function", "filteredTypes.variable", "filteredTypes.constant", "filteredTypes.string", "filteredTypes.number", "filteredTypes.boolean", "filteredTypes.array", "filteredTypes.object", "filteredTypes.key", "filteredTypes.null", "filteredTypes.enumMember", "filteredTypes.struct", "filteredTypes.event", "filteredTypes.operator", "filteredTypes.typeParameter", "name"]], ["vs/workbench/contrib/outline/browser/outlineActions", ["collapse", "expand", "followCur", "filterOnType", "sortByPosition", "sortByName", "sortByKind"]], ["vs/workbench/contrib/outline/browser/outlinePane", ["no-editor", "loading", "no-symbols"]], ["vs/workbench/contrib/output/browser/output.contribution", ["outputViewIcon", "miToggleOutput", "switchBetweenOutputs.label", "switchToOutput.label", "extensionLogs", "selectlog", "nocustumoutput", "selectlog", "selectOutput", "outputScrollOff", "outputScrollOn", "logLevel.label", "logLevelDefault.label", "extensionLogs", "selectlog", "logFile", "selectlogFile", "clearFiltersText", "extensionLogs", "userLogs", "selectlog", "importLogFile", "logFiles", "output", "output.smartScroll.enabled", "output", "output", "addCompoundLog", "output", "removeLog", "output", "showOutputChannels", "output", "clearOutput.label", "toggleAutoScroll", "openActiveOutputFile", "openActiveOutputFileInNewWindow", "saveActiveOutputAs", "showLogs", "openLogFile", "toggleTraceDescription", "exportLogs", "importLog"]], ["vs/workbench/contrib/output/browser/outputServices", ["saveLog.dialogTitle"]], ["vs/workbench/contrib/output/browser/outputView", ["outputView.filter.placeholder", "output model title", "channel", "output", "outputViewAriaLabel"]], ["vs/workbench/contrib/performance/browser/performance.contribution", ["show.label", "cycles", "insta.trace", "emitter"]], ["vs/workbench/contrib/performance/browser/perfviewEditor", ["name"]], ["vs/workbench/contrib/performance/electron-sandbox/performance.contribution", ["experimental.rendererProfiling"]], ["vs/workbench/contrib/performance/electron-sandbox/startupProfiler", ["prof.message", "prof.detail", "prof.<PERSON><PERSON>ndFileIssue", "prof.restart", "prof.thanks", "prof.detail.restart", "prof.restart.button"]], ["vs/workbench/contrib/preferences/browser/keybindingsEditor", ["recordKeysLabel", "sortByPrecedeneLabel", "SearchKeybindings.FullTextSearchPlaceholder", "SearchKeybindings.KeybindingsSearchPlaceholder", "clearInput", "recording", "command", "keybinding", "when", "source", "foundResults", "show sorted keybindings", "show keybindings", "changeLabel", "addLabel", "addLabel", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "reset<PERSON><PERSON><PERSON>", "showSameKeybindings", "copyLabel", "copyCommandLabel", "copyCommandTitleLabel", "error", "editKeybinding<PERSON>abel<PERSON>", "editKeybindingLabel", "addKeybindingLabelWith<PERSON>ey", "addKeybindingLabel", "title", "extension label", "keybindings<PERSON><PERSON>l", "noKeybinding", "noWhen", "keyboard shortcuts aria label"]], ["vs/workbench/contrib/preferences/browser/keybindingsEditorContribution", ["defineKeybinding.kbLayoutErrorMessage", "defineKeybinding.kbLayoutLocalAndUSMessage", "defineKeybinding.kbLayoutLocalMessage"]], ["vs/workbench/contrib/preferences/browser/keybindingWidgets", ["defineKeybinding.initial", "defineKeybinding.oneExists", "defineKeybinding.existing", "defineKeybinding.chordsTo"]], ["vs/workbench/contrib/preferences/browser/keyboardLayoutPicker", ["status.workbench.keyboardLayout", "keyboardLayout", "keyboardLayout", "keyboardLayout", "displayLanguage", "doc", "layoutPicks", "configureKeyboardLayout", "autoDetect", "pickKeyboardLayout", "fail.createSettings", "keyboard.chooseLayout"]], ["vs/workbench/contrib/preferences/browser/preferences.contribution", ["settingsEditor2", "keybindingsEditor", "miOpenSettings", "openFolderSettings", "miOpenOnlineSettings", "miOpenTelemetrySettings", "settings.focusFile", "settings.focusFile", "settings.focusSettingsList", "settings.focusSettingControl", "keyboardShortcuts", "keyboardShortcuts", "clear", "clearHistory", "miPreferences", "openSettings2", "openUserSettingsJson", "openApplicationSettingsJson", "settings", "openSettings2", "workbench.action.openSettingsJson.description", "openGlobalSettings", "openRawDefaultSettings", "openWorkspaceSettings", "openAccessibilitySettings", "openWorkspaceSettingsFile", "openFolderSettings", "openFolderSettingsFile", "filterUntrusted", "openRemoteSettings", "openRemoteSettingsJSON", "settings.focusSearch", "settings.clearResults", "settings.focusSettingsTOC", "settings.showContextMenu", "settings.focusLevelUp", "preferences", "openGlobalKeybindings", "openDefaultKeybindingsFile", "openGlobalKeybindingsFile", "showDefaultKeybindings", "showExtensionKeybindings", "showUserKeybindings", "defineKeybinding.start", "openSettingsJson"]], ["vs/workbench/contrib/preferences/browser/preferencesActions", ["languageDescriptionConfigured", "pickLanguage", "configureLanguageBasedSettings"]], ["vs/workbench/contrib/preferences/browser/preferencesIcons", ["settingsScopeDropDownIcon", "settingsMoreActionIcon", "keybindingsRecordKeysIcon", "keybindingsSortIcon", "keybindingsEditIcon", "keybindingsAddIcon", "settingsEditIcon", "settingsRemoveIcon", "preferencesDiscardIcon", "preferencesClearInput", "settingsFilter", "preferencesOpenSettings"]], ["vs/workbench/contrib/preferences/browser/preferencesRenderers", ["editTtile", "replaceDefaultValue", "copyDefaultValue", "replaceDefaultValue", "copyDefaultValue", "unsupportedPolicySetting", "unsupportLanguageOverrideSetting", "defaultProfileSettingWhileNonDefaultActive", "allProfileSettingWhileInNonDefaultProfileSetting", "unsupportedRemoteMachineSetting", "unsupportedWindowSetting", "unsupportedApplicationSetting", "unsupportedMachineSetting", "untrustedSetting", "unknown configuration setting", "manage workspace trust", "manage workspace trust", "unsupportedProperty"]], ["vs/workbench/contrib/preferences/browser/preferencesWidgets", ["userSettings", "userSettingsRemote", "workspaceSettings", "folderSettings", "settingsSwitcherBarAriaLabel", "userSettings", "userSettingsRemote", "workspaceSettings", "userSettings", "workspaceSettings"]], ["vs/workbench/contrib/preferences/browser/settingsEditor2", ["SearchSettings.AriaLabel", "clearInput", "filterInput", "noResults", "clearSearchFilters", "settings", "settings require trust", "suggestionsPrefix", "noResults", "oneResult", "moreThanOneResult", "turnOnSyncButton", "lastSyncedLabel"]], ["vs/workbench/contrib/preferences/browser/settingsEditorSettingIndicators", ["workspaceUntrustedLabel", "trustLabel", "manageWorkspaceTrust", "extensionSyncIgnoredLabel", "syncIgnoredTitle", "defaultOverriddenLabel", "previewLabel", "experimental<PERSON><PERSON>l", "user", "workspace", "remote", "policyLabelText", "policyDescription", "policyFilterLink", "applicationSetting", "applicationSettingDescription", "alsoConfiguredIn", "configuredIn", "alsoConfiguredElsewhere", "configured<PERSON><PERSON><PERSON><PERSON>", "alsoModifiedInScopes", "modifiedInScopes", "hasDefaultOverridesForLanguages", "defaultOverriddenDetails", "multipledefaultOverriddenDetails", "user", "workspace", "remote", "modifiedInScopeForLanguage", "user", "workspace", "remote", "modifiedInScopeForLanguageMidSentence", "previewLabel", "experimental<PERSON><PERSON>l", "workspaceUntrustedAriaLabel", "policyDescriptionAccessible", "applicationSettingDescriptionAccessible", "alsoConfiguredIn", "configuredIn", "syncIgnoredAriaLabel", "defaultOverriddenDetailsAriaLabel", "multipleDefaultOverriddenDetailsAriaLabel", "defaultOverriddenLanguagesList"]], ["vs/workbench/contrib/preferences/browser/settingsLayout", ["commonlyUsed", "textEditor", "cursor", "find", "font", "formatting", "diffEditor", "multiDiffEditor", "minimap", "suggestions", "files", "workbench", "appearance", "breadcrumbs", "editorManagement", "settings", "zenMode", "screencastMode", "window", "newWindow", "features", "accessibility.signals", "accessibility", "fileExplorer", "search", "debug", "testing", "scm", "extensions", "terminal", "task", "problems", "output", "comments", "remote", "timeline", "notebook", "mergeEditor", "chat", "issueReporter", "application", "proxy", "keyboard", "update", "telemetry", "settingsSync", "experimental", "other", "security", "workspace"]], ["vs/workbench/contrib/preferences/browser/settingsSearchMenu", ["modifiedSettingsSearch", "modifiedSettingsSearchTooltip", "extSettingsSearch", "extSettingsSearchTooltip", "featureSettingsSearch", "featureSettingsSearchTooltip", "tagSettingsSearch", "tagSettingsSearchTooltip", "langSettingsSearch", "langSettingsSearchTooltip", "onlineSettingsSearch", "onlineSettingsSearchTooltip", "policySettingsSearch", "policySettingsSearchTooltip"]], ["vs/workbench/contrib/preferences/browser/settingsTree", ["extensions", "modified", "settingsContextMenuTitle", "newExtensionsButtonLabel", "editInSettingsJson", "editLanguageSettingLabel", "settings.De<PERSON>ult", "modified", "showExtension", "dismiss", "resetSettingLabel", "validationError", "validationError", "settings.Modified", "settings", "copySettingIdLabel", "copySettingAsJSONLabel", "copySettingAsURLLabel", "stopSyncingSetting", "applyToAllProfiles"]], ["vs/workbench/contrib/preferences/browser/settingsWidgets", ["okButton", "cancelButton", "listValueHintLabel", "listSiblingHintLabel", "removeItem", "editItem", "addItem", "itemInputPlaceholder", "listSiblingInputPlaceholder", "excludePatternHintLabel", "excludeSiblingHintLabel", "excludeIncludeSource", "removeExcludeItem", "editExcludeItem", "addPattern", "excludePatternInputPlaceholder", "excludeSiblingInputPlaceholder", "includePatternHintLabel", "includeSiblingHintLabel", "excludeIncludeSource", "removeIncludeItem", "editIncludeItem", "addPattern", "includePatternInputPlaceholder", "includeSiblingInputPlaceholder", "okButton", "cancelButton", "objectKeyInputPlaceholder", "objectValueInputPlaceholder", "objectPairHintLabelWithSource", "objectPairHintLabel", "removeItem", "resetItem", "editItem", "addItem", "objectKeyHeader", "objectValueHeader", "objectPairHintLabel", "removeItem", "resetItem", "editItem", "addItem", "objectKeyHeader", "objectValueHeader"]], ["vs/workbench/contrib/preferences/browser/tocTree", ["settingsTOC", "groupRowAriaLabel"]], ["vs/workbench/contrib/preferences/common/preferences", ["previewIndicatorDescription", "experimentalIndicatorDescription"]], ["vs/workbench/contrib/preferences/common/preferencesContribution", ["splitSettingsEditorLabel", "enableNaturalLanguageSettingsSearch", "settingsSearchTocBehavior.hide", "settingsSearchTocBehavior.filter", "settingsSearchTocBehavior"]], ["vs/workbench/contrib/preferences/common/settingsEditorColorRegistry", ["headerForeground", "settingsHeaderHoverForeground", "modifiedItemForeground", "settingsHeaderBorder", "settingsSashBorder", "settingsDropdownBackground", "settingsDropdownForeground", "settingsDropdownBorder", "settingsDropdownListBorder", "settingsCheckboxBackground", "settingsCheckboxForeground", "settingsCheckboxBorder", "textInputBoxBackground", "textInputBoxForeground", "textInputBoxBorder", "numberInputBoxBackground", "numberInputBoxForeground", "numberInputBoxBorder", "focusedRowBackground", "settings.rowHoverBackground", "settings.focusedRowBorder"]], ["vs/workbench/contrib/quickaccess/browser/commandsQuickAccess", ["noCommandResults", "configure keybinding", "askXInChat", "commandWithCategory", "confirmClearMessage", "confirmClearDetail", "clearButtonLabel", "showTriggerActions", "clearCommandHistory"]], ["vs/workbench/contrib/quickaccess/browser/quickAccess.contribution", ["helpQuickAccessPlaceholder", "helpQuickAccess", "more", "viewQuickAccessPlaceholder", "viewQuickAccess", "commandsQuickAccessPlaceholder", "commandsQuickAccess", "miCommandPalette", "miShowAllCommands", "mi<PERSON><PERSON><PERSON>iew", "miGotoLine", "commandPalette", "commandPalette"]], ["vs/workbench/contrib/quickaccess/browser/viewQuickAccess", ["noViewResults", "views", "panels", "secondary side bar", "terminalTitle", "terminals", "debugConsoles", "channels", "openView", "quickOpenView"]], ["vs/workbench/contrib/relauncher/browser/relauncher.contribution", ["relaunchSettingMessage", "relaunchSettingMessageWeb", "relaunchSettingDetail", "relaunchSettingDetailWeb", "restart", "restartWeb", "restartExtensionHost.reason"]], ["vs/workbench/contrib/remote/browser/explorerViewItems", ["switchRemote.label"]], ["vs/workbench/contrib/remote/browser/remote", ["remote.help.getStarted", "remote.help.documentation", "remote.help.issues", "remote.help.report", "pickRemoteExtension", "remotehelp", "remote.explorer", "reconnectionWaitOne", "reconnectionWaitMany", "reconnectNow", "reloadWindow", "connectionLost", "reconnectionRunning", "reconnectionPermanentFailure", "reloadWindow.dialog", "remote.help", "remote.explorer"]], ["vs/workbench/contrib/remote/browser/remoteConnectionHealth", ["unsupportedGlibcWarning", "allow", "learnMore", "remember", "unsupportedGlibcBannerLearnMore", "unsupportedGlibcWarning.banner"]], ["vs/workbench/contrib/remote/browser/remoteExplorer", ["remoteNoPorts", "noRemoteNoPorts", "1forwardedPort", "nForwardedPorts", "remote.forwardedPorts.statusbarTextNone", "remote.forwardedPorts.statusbarTooltip", "status.forwardedPorts", "remote.autoForwardPortsSource.fallback", "remote.autoForwardPortsSource.fallback.switchBack", "remote.autoForwardPortsSource.fallback.showPortSourceSetting", "remote.tunnelsView.automaticForward", "remote.tunnelsView.notificationLink2", "remote.tunnelsView.elevationMessage", "remote.tunnelsView.makePublic", "remote.tunnelsView.elevationButton", "ports"]], ["vs/workbench/contrib/remote/browser/remoteIcons", ["getStartedIcon", "documentationIcon", "feedbackIcon", "reviewIssuesIcon", "reportIssuesIcon", "remoteExplorerViewIcon", "portsViewIcon", "portIcon", "privatePortIcon", "forwardPortIcon", "stopForwardIcon", "openBrowserIcon", "openPreviewIcon", "copyAddressIcon", "labelPortIcon", "forwardedPortWithoutProcessIcon", "forwardedPortWithProcessIcon"]], ["vs/workbench/contrib/remote/browser/remoteIndicator", ["miCloseRemote", "host.open", "host.open", "host.reconnecting", "disconnectedFrom", "host.tooltip", "workspace.tooltip", "workspace.tooltip2", "noHost.tooltip", "remoteHost", "networkStatusOfflineTooltip", "networkStatusHighLatencyTooltip", "unknownSetupError", "retry", "remote.startActions.help", "remote.startActions.install", "closeRemoteConnection.title", "reloadWindow", "closeVirtualWorkspace.title", "remoteActions", "remote.startActions.installingExtension", "remote.showExtensionRecommendations", "remote.category", "remote.showMenu", "remote.close", "remote.install"]], ["vs/workbench/contrib/remote/browser/remoteStartEntry", ["remote.category", "remote.showWebStartEntryActions"]], ["vs/workbench/contrib/remote/browser/tunnelFactory", ["tunnelPrivacy.private", "tunnelPrivacy.public"]], ["vs/workbench/contrib/remote/browser/tunnelView", ["remote.tunnelsView.addPort", "tunnelPrivacy.private", "tunnel.portColumn.label", "tunnel.portColumn.tooltip", "tunnel.addressColumn.label", "tunnel.addressColumn.tooltip", "portsLink.followLinkAlt.mac", "portsLink.followLinkAlt", "portsLink.followLinkCmd", "portsLink.followLinkCtrl", "tunnel.processColumn.label", "tunnel.processColumn.tooltip", "tunnel.originColumn.label", "tunnel.originColumn.tooltip", "tunnel.privacyColumn.label", "tunnel.privacyColumn.tooltip", "remote.tunnelsView.input", "tunnelView.runningProcess.inacessable", "remote.tunnel.tooltipForwarded", "remote.tunnel.tooltipCandidate", "tunnel.iconColumn.running", "tunnel.iconColumn.notRunning", "remote.tunnel.tooltipName", "tunnelPrivacy.unknown", "tunnelPrivacy.private", "tunnel.focusContext", "tunnelView", "remote.tunnel.label", "remote.tunnelsView.labelPlaceholder", "remote.tunnelsView.portNumberValid", "remote.tunnelsView.portNumberToHigh", "remote.tunnelView.inlineElevationMessage", "remote.tunnelView.alreadyForwarded", "remote.tunnel.forwardItem", "remote.tunnel.forwardPrompt", "remote.tunnel.forwardError", "remote.tunnel.forwardE<PERSON><PERSON><PERSON>rovided", "remote.tunnel.closeNoPorts", "remote.tunnel.closePlaceholder", "remote.tunnel.open", "remote.tunnel.openPreview", "remote.tunnel.openCommandPalette", "remote.tunnel.openCommandPaletteNone", "remote.tunnel.openCommandPaletteView", "remote.tunnel.openCommandPalettePick", "remote.tunnel.copyAddressInline", "remote.tunnel.copyAddressCommandPalette", "remote.tunnel.copyAddressPlaceholdter", "remote.tunnel.changeLocalPort", "remote.tunnelsView.portShouldBeNumber", "remote.tunnel.changeLocalPortNumber", "remote.tunnelsView.changePort", "remote.tunnel.protocolHttp", "remote.tunnel.protocolHttps", "tunnelContext.privacyMenu", "tunnelContext.protocolMenu", "portWithRunningProcess.foreground", "remote.tunnel", "remote.tunnel.forward", "remote.tunnel.close"]], ["vs/workbench/contrib/remote/common/remote.contribution", ["invalidWorkspaceMessage", "invalidWorkspaceDetail", "invalidWorkspacePrimary", "ui", "workspace", "remote", "remote.extensionKind", "remote.restoreForwardedPorts", "remote.autoForwardPorts", "remote.autoForwardPortsSource", "remote.autoForwardPortsSource.process", "remote.autoForwardPortsSource.output", "remote.autoForwardPortsSource.hybrid", "remote.autoForwardPortFallback", "remote.forwardOnClick", "remote.portsAttributes.port", "remote.portsAttributes.notify", "remote.portsAttributes.openBrowser", "remote.portsAttributes.openBrowserOnce", "remote.portsAttributes.openPreview", "remote.portsAttributes.silent", "remote.portsAttributes.ignore", "remote.portsAttributes.onForward", "remote.portsAttributes.elevateIfNeeded", "remote.portsAttributes.label", "remote.portsAttributes.labelDefault", "remote.portsAttributes.requireLocalPort", "remote.portsAttributes.protocol", "remote.portsAttributes.labelDefault", "remote.portsAttributes", "remote.portsAttributes.patternError", "remote.portsAttributes.notify", "remote.portsAttributes.openBrowser", "remote.portsAttributes.openPreview", "remote.portsAttributes.silent", "remote.portsAttributes.ignore", "remote.portsAttributes.onForward", "remote.portsAttributes.elevateIfNeeded", "remote.portsAttributes.label", "remote.portsAttributes.labelDefault", "remote.portsAttributes.requireLocalPort", "remote.portsAttributes.protocol", "remote.portsAttributes.defaults", "remote.localPortHost", "remote.defaultExtensionsIfInstalledLocally.markdownDescription", "remote.defaultExtensionsIfInstalledLocally.invalidFormat", "triggerReconnect", "pauseSocketWriting"]], ["vs/workbench/contrib/remote/electron-sandbox/remote.contribution", ["wslFeatureInstalled", "remote", "remote.downloadExtensionsLocally"]], ["vs/workbench/contrib/remoteTunnel/electron-sandbox/remoteTunnel.contribution", ["remoteTunnel.actions.turnOn", "remoteTunnel.actions.turnOff", "remoteTunnel.actions.showLog", "remoteTunnel.actions.configure", "remoteTunnel.actions.copyToClipboard", "remoteTunnel.actions.learnMore", "recommend.remoteExtension", "action.showExtension", "action.doNotShowAgain", "initialize.progress.title", "startTunnel.progress.title", "remoteTunnel.serviceInstallFailed", "accountPreference.placeholder", "signed in", "others", "sign in using account", "tunnel.preview", "enable", "tunnel.enable.placeholder", "tunnel.enable.session", "tunnel.enable.session.description", "tunnel.enable.service", "tunnel.enable.service.description", "progress.turnOn.final", "action.copyToClipboard", "action.showExtension", "progress.turnOn.failed", "remoteTunnel.actions.manage.on.v2", "remoteTunnel.actions.manage.connecting", "remoteTunnel.turnOffAttached.confirm", "remoteTunnel.turnOff.confirm", "manage.placeholder", "manage.title.attached", "manage.title.orunning", "manage.title.off", "manage.showLog", "manage.tunnelName", "remoteTunnelAccess.machineName", "remoteTunnelAccess.machineNameRegex", "remoteTunnelAccess.preventSleep", "remoteTunnel.category"]], ["vs/workbench/contrib/replNotebook/browser/repl.contribution", ["repl.focusLastReplOutput", "repl.input.focus", "repl.execute"]], ["vs/workbench/contrib/replNotebook/browser/replEditor", ["replEditorInput"]], ["vs/workbench/contrib/replNotebook/browser/replEditorAccessibilityHelp", ["replEditor.inputOverview", "replEditor.execute", "replEditor.configReadExecution", "replEditor.autoFocusRepl", "replEditor.focusLastItemAdded", "replEditor.inputAccessibilityView", "replEditor.focusReplInput", "replEditor.historyOverview", "replEditor.focusCellEditor", "replEditor.cellNavigation", "replEditor.accessibilityView", "replEditor.focusInOutput", "replEditor.focusReplInputFromHistory", "replEditor.focusLastItemAdded"]], ["vs/workbench/contrib/replNotebook/browser/replEditorInput", ["replEditorLabelIcon"]], ["vs/workbench/contrib/sash/browser/sash.contribution", ["sashSize", "sashHoverDelay"]], ["vs/workbench/contrib/scm/browser/activity", ["scmPendingChangesBadge", "status.scm", "status.scm.provider", "scmActiveResourceHasChanges", "scmActiveResourceRepository"]], ["vs/workbench/contrib/scm/browser/menus", ["miShare"]], ["vs/workbench/contrib/scm/browser/quickDiffDecorator", ["diffAdded", "diffModified", "diffDeleted"]], ["vs/workbench/contrib/scm/browser/quickDiffWidget", ["remotes", "quickDiff.base.switch", "changes", "change", "multiChanges", "multiChange", "label.close", "miGotoNextChange", "miGotoPreviousChange", "show previous change", "show next change", "move to previous change", "move to next change"]], ["vs/workbench/contrib/scm/browser/scm.contribution", ["sourceControlViewIcon", "source control view", "no open repo", "no open repo in an untrusted workspace", "manageWorkspaceTrustAction", "no history items", "source control repositories", "miViewSCM", "source control graph", "scmConfigurationTitle", "scm.diffDecorations.all", "scm.diffDecorations.gutter", "scm.diffDecorations.overviewRuler", "scm.diffDecorations.minimap", "scm.diffDecorations.none", "diffDecorations", "diff<PERSON><PERSON><PERSON><PERSON><PERSON>", "scm.diffDecorationsGutterVisibility.always", "scm.diffDecorationsGutterVisibility.hover", "scm.diffDecorationsGutterVisibility", "scm.diffDecorationsGutterAction.diff", "scm.diffDecorationsGutterAction.none", "scm.diffDecorationsGutterAction", "diffGutterPattern", "diffGutterPatternAdded", "diffGutterPatternModifed", "scm.diffDecorationsIgnoreTrimWhitespace.true", "scm.diffDecorationsIgnoreTrimWhitespace.false", "scm.diffDecorationsIgnoreTrimWhitespace.inherit", "diffDecorationsIgnoreTrimWhitespace", "alwaysShowActions", "scm.countBadge.all", "scm.countBadge.focused", "scm.countBadge.off", "scm.countBadge", "scm.providerCountBadge.hidden", "scm.providerCountBadge.auto", "scm.providerCountBadge.visible", "scm.providerCountBadge", "scm.defaultViewMode.tree", "scm.defaultViewMode.list", "scm.defaultViewMode", "scm.defaultViewSortKey.name", "scm.defaultViewSortKey.path", "scm.defaultViewSortKey.status", "scm.defaultViewSortKey", "autoReveal", "inputFontFamily", "inputFontSize", "inputMaxLines", "inputMinLines", "alwaysShowRepository", "scm.repositoriesSortOrder.discoveryTime", "scm.repositoriesSortOrder.name", "scm.repositoriesSortOrder.path", "repositoriesSortOrder", "providersVisible", "showActionButton", "showInputActionButton", "scm.workingSets.enabled", "scm.workingSets.default.empty", "scm.workingSets.default.current", "scm.workingSets.default", "scm.compactFolders", "scm.graph.pageOnScroll", "scm.graph.pageSize", "scm.graph.badges.all", "scm.graph.badges.filter", "scm.graph.badges", "scm accept", "scm view next commit", "scm view previous commit", "scmActiveRepositoryPlaceHolder", "scmActiveRepositoryAutoDescription", "open in external terminal", "open in integrated terminal", "quickDiffDecoration", "source control", "scmRepositories", "scmChanges", "scmGraph"]], ["vs/workbench/contrib/scm/browser/scmAccessibilityHelp", ["state-msg1", "state-msg2", "state-msg3", "state-msg4", "enabled", "disabled", "state-msg5", "state-msg6", "scm-repositories-msg1", "scm-repositories-msg2", "scm-repositories-msg3", "scm-repositories-msg4", "scm-repositories-msg5", "scm-repositories-msg6", "scm-msg1", "scm-msg2", "scm-msg3", "scm-msg4", "scm-msg5", "scm-graph-msg1", "scm-graph-msg2", "scm-graph-msg3", "scm-graph-msg4", "scm-graph-msg5"]], ["vs/workbench/contrib/scm/browser/scmHistory", ["scmGraphHistoryItemRefColor", "scmGraphHistoryItemRemoteRefColor", "scmGraphHistoryItemBaseRefColor", "scmGraphHistoryItemHoverDefaultLabelForeground", "scmGraphHistoryItemHoverDefaultLabelBackground", "scmGraphHistoryItemHoverLabelForeground", "scmGraph.HistoryItemHoverAdditionsForeground", "scmGraph.HistoryItemHoverDeletionsForeground", "scmGraphForeground1", "scmGraphForeground2", "scmGraphForeground3", "scmGraphForeground4", "scmGraphForeground5"]], ["vs/workbench/contrib/scm/browser/scmHistoryViewPane", ["all", "auto", "items", "allHistoryItemRefs", "repositoryPicker", "referencePicker", "goToCurrentHistoryItem", "refreshGraph", "openChanges", "historyItemChangesEditorTitle", "emailLinkTitle", "fileChanged", "filesChanged", "insertion", "insertions", "deletion", "deletions", "loadMore", "scm history", "auto", "activeRepository", "scmGraphRepository", "all", "allHistoryItemRefs", "auto", "currentHistoryItemRef", "scmGraphHistoryItemRef", "scmGraphViewOutdated"]], ["vs/workbench/contrib/scm/browser/scmRepositoriesViewPane", ["scm"]], ["vs/workbench/contrib/scm/browser/scmViewPane", ["scm", "scmInput", "scmInputRow.accessibilityHelp", "scmInputRow.accessibilityHelpNoKb", "sortAction", "repositories", "setListViewMode", "setTreeViewMode", "repositorySortByDiscoveryTime", "repositorySortByName", "repositorySortByPath", "sortChangesByName", "sortChangesByPath", "sortChangesByStatus", "collapse all", "expand all", "scmInputMoreActions", "scmInputCancelAction", "scmInput.accessibilityHelp", "scmInput.accessibilityHelpNoKb", "label.close"]], ["vs/workbench/contrib/scm/browser/scmViewService", ["auto"]], ["vs/workbench/contrib/scm/common/quickDiff", ["editorGutterModifiedBackground", "editorGutterModifiedSecondaryBackground", "editorGutterAddedBackground", "editorGutterAddedSecondaryBackground", "editorGutterDeletedBackground", "editorGutterDeletedSecondaryBackground", "minimapGutterModifiedBackground", "minimapGutterAddedBackground", "minimapGutterDeletedBackground", "overviewRulerModifiedForeground", "overviewRulerAddedForeground", "overviewRulerDeletedForeground", "editorGutterItemGlyphForeground", "editorGutterItemBackground"]], ["vs/workbench/contrib/scrollLocking/browser/scrollLocking", ["mouseScrolllingLocked", "mouseLockScrollingEnabled", "miToggleLockedScrolling", "synchronizeScrolling", "miHoldLockedScrolling", "toggleLockedScrolling", "holdLockedScrolling"]], ["vs/workbench/contrib/search/browser/anythingQuickAccess", ["noAnythingResults", "recentlyOpenedSeparator", "recentlyOpenedSeparator", "fileAndSymbolResultsSeparator", "fileResultsSeparator", "helpPickAriaLabel", "chat", "openToSide", "openToBottom", "closeEditor", "filePickAriaLabelDirty"]], ["vs/workbench/contrib/search/browser/patternInputWidget", ["defaultLabel", "onlySearchInOpenEditors", "useExcludesAndIgnoreFilesDescription"]], ["vs/workbench/contrib/search/browser/quickTextSearch/textSearchQuickAccess", ["goToSearch", "QuickSearchSeeMoreFiles", "QuickSearchOpenInFile", "QuickSearchMore", "showMore", "enterSearchTerm", "noAnythingResults"]], ["vs/workbench/contrib/search/browser/replaceService", ["searchReplace.source", "fileReplaceChanges"]], ["vs/workbench/contrib/search/browser/search.contribution", ["miViewSearch", "anythingQuickAccessPlaceholder", "anythingQuickAccess", "symbolsQuickAccessPlaceholder", "symbolsQuickAccess", "textSearchPickerPlaceholder", "textSearchPickerHelp", "searchConfigurationTitle", "exclude", "exclude.boolean", "exclude.when", "search.mode", "search.mode.view", "search.mode.reuseEditor", "search.mode.newEditor", "useRipgrep", "useRipgrepDeprecated", "maintainFileSearchCacheDeprecated", "search.maintainFileSearchCache", "useIgnoreFiles", "useGlobalIgnoreFiles", "useParentIgnoreFiles", "search.quickOpen.includeSymbols", "search.ripgrep.maxThreads", "search.quickOpen.includeHistory", "filterSortOrder.default", "filterSortOrder.recency", "filterSortOrder", "search.followSymlinks", "search.smartCase", "search.globalFindClipboard", "search.location", "search.location.deprecationMessage", "search.maxResults", "search.collapseResults.auto", "search.collapseAllResults", "search.useReplacePreview", "search.showLineNumbers", "search.usePCRE2", "usePCRE2Deprecated", "search.actionsPositionAuto", "search.actionsPositionRight", "search.actionsPosition", "search.searchOnType", "search.seedWithNearestWord", "search.seedOnFocus", "search.searchOnTypeDebouncePeriod", "search.searchEditor.doubleClickBehaviour.selectWord", "search.searchEditor.doubleClickBehaviour.goToLocation", "search.searchEditor.doubleClickBehaviour.openLocationToSide", "search.searchEditor.doubleClickBehaviour", "search.searchEditor.singleClickBehaviour.default", "search.searchEditor.singleClickBehaviour.peekDefinition", "search.searchEditor.singleClickBehaviour", "search.searchEditor.reusePriorSearchConfiguration", "search.searchEditor.defaultNumberOfContextLines", "search.searchEditor.focusResultsOnSearch", "searchSortOrder.default", "searchSortOrder.filesOnly", "searchSortOrder.type", "searchSortOrder.modified", "searchSortOrder.countDescending", "searchSortOrder.countAscending", "search.sortOrder", "search.decorations.colors", "search.decorations.badges", "scm.defaultViewMode.tree", "scm.defaultViewMode.list", "search.defaultViewMode", "search.quickAccess.preserveInput", "search.experimental.closedNotebookResults", "search", "search"]], ["vs/workbench/contrib/search/browser/searchActionsBase", ["search"]], ["vs/workbench/contrib/search/browser/searchActionsCopy", ["copyMatchLabel", "copyPathLabel", "copyAllLabel", "getSearchResultsLabel"]], ["vs/workbench/contrib/search/browser/searchActionsFind", ["search.expandRecursively", "miFindInFiles", "findInFiles.description", "findInFiles.args", "restrictResultsToFolder", "excludeFolderFromSearch", "revealInSideBar", "findInFiles", "findInFolder", "findInWorkspace"]], ["vs/workbench/contrib/search/browser/searchActionsNav", ["ToggleQueryDetailsAction.label", "CloseReplaceWidget.label", "ToggleCaseSensitiveCommandId.label", "ToggleWholeWordCommandId.label", "ToggleRegexCommandId.label", "TogglePreserveCaseId.label", "OpenMatch.label", "OpenMatchToSide.label", "AddCursorsAtSearchResults.label", "FocusNextInputAction.label", "FocusPreviousInputAction.label", "FocusSearchFromResults.label", "toggleTabs", "focusSearchListCommandLabel", "FocusNextSearchResult.label", "FocusPreviousSearchResult.label", "replaceInFiles"]], ["vs/workbench/contrib/search/browser/searchActionsRemoveReplace", ["RemoveAction.label", "match.replace.label", "file.replaceAll.label", "file.replaceAll.label"]], ["vs/workbench/contrib/search/browser/searchActionsSymbol", ["showTriggerActions", "miGotoSymbolInWorkspace", "showTriggerActions"]], ["vs/workbench/contrib/search/browser/searchActionsTextQuickAccess", ["quickTextSearch"]], ["vs/workbench/contrib/search/browser/searchActionsTopBar", ["clearSearchHistoryLabel", "CancelSearchAction.label", "RefreshAction.label", "CollapseDeepestExpandedLevelAction.label", "ExpandAllAction.label", "ClearSearchResultsAction.label", "ViewAsTreeAction.label", "ViewAsListAction.label", "SearchWithAIAction.label"]], ["vs/workbench/contrib/search/browser/searchFindInput", ["searchFindInputNotebookFilter.label"]], ["vs/workbench/contrib/search/browser/searchIcons", ["searchDetailsIcon", "searchSeeMoreIcon", "searchShowContextIcon", "searchHideReplaceIcon", "searchShowReplaceIcon", "searchReplaceAllIcon", "searchReplaceIcon", "searchRemoveIcon", "searchRefreshIcon", "searchCollapseAllIcon", "searchExpandAllIcon", "searchShowAsTree", "searchShowAsList", "searchClearIcon", "searchStopIcon", "searchViewIcon", "searchNewEditorIcon", "searchOpenInFile", "searchSparkleFilled", "searchSparkleEmpty"]], ["vs/workbench/contrib/search/browser/searchMessage", ["unable to open trust", "unable to open"]], ["vs/workbench/contrib/search/browser/searchResultsView", ["searchFolderMatch.plainText.label", "searchFolderMatch.aiText.label", "searchFolderMatch.other.label", "searchFolderMatch.other.label", "searchFileMatches", "searchFileMatch", "searchMatches", "searchMatch", "lineNumStr", "numLinesStr", "search", "folderMatchAriaLabel", "otherFilesAriaLabel", "fileMatchAriaLabel", "replacePreviewResultAria", "searchResultAria"]], ["vs/workbench/contrib/search/browser/searchView", ["searchCanceled", "moreSearch", "searchScope.includes", "placeholder.includes", "searchScope.excludes", "placeholder.excludes", "replaceAll.confirmation.title", "replaceAll.confirm.button", "replaceAll.occurrence.file.message", "removeAll.occurrence.file.message", "replaceAll.occurrence.files.message", "removeAll.occurrence.files.message", "replaceAll.occurrences.file.message", "removeAll.occurrences.file.message", "replaceAll.occurrences.files.message", "removeAll.occurrences.files.message", "removeAll.occurrence.file.confirmation.message", "replaceAll.occurrence.file.confirmation.message", "removeAll.occurrence.files.confirmation.message", "replaceAll.occurrence.files.confirmation.message", "removeAll.occurrences.file.confirmation.message", "replaceAll.occurrences.file.confirmation.message", "removeAll.occurrences.files.confirmation.message", "replaceAll.occurrences.files.confirmation.message", "emptySearch", "searchPathNotFoundError", "noResultsFallback", "triggerAISearch.tooltip", "searchWithAIButtonTooltip", "noOpenEditorResultsIncludesExcludes", "noOpenEditorResultsIncludes", "noOpenEditorResultsExcludes", "noOpenEditorResultsFound", "noResultsIncludesExcludes", "noResultsIncludes", "noResultsExcludes", "noResultsFound", "rerunSearch.message", "rerunSearchInAll.message", "openSettings.message", "openSettings.learnMore", "ariaSearchResultsStatus", "searchMaxResultsWarning", "forTerm", "useIgnoresAndExcludesDisabled", "excludes.enable", "useExcludesAndIgnoreFilesDescription", "onlyOpenEditors", "openEditors.disable", "disableOpenEditors", "openInEditor.tooltip", "openInEditor.message", "keywordSuggestion.message", "search.file.result", "search.files.result", "search.file.results", "search.files.results", "searchWithoutFolder", "openFolder"]], ["vs/workbench/contrib/search/browser/searchWidget", ["search.action.replaceAll.disabled.label", "search.action.replaceAll.enabled.label", "search.replace.toggle.button.title", "label.Search", "search.placeHolder", "showContext", "label.Replace", "search.replace.placeHolder"]], ["vs/workbench/contrib/search/browser/symbolsQuickAccess", ["noSymbolResults", "openToSide", "openToBottom"]], ["vs/workbench/contrib/searchEditor/browser/searchEditor.contribution", ["searchEditor", "promptOpenWith.searchEditor.displayName", "search.openNewEditor", "search", "searchEditor.deleteResultBlock", "search.openNewSearchEditor", "search.openSearchEditor", "search.openNewEditorToSide", "search.openResultsInEditor", "search.rerunSearchInEditor", "search.action.focusQueryEditorWidget", "search.action.focusFilesToInclude", "search.action.focusFilesToExclude", "searchEditor.action.toggleSearchEditorCaseSensitive", "searchEditor.action.toggleSearchEditorWholeWord", "searchEditor.action.toggleSearchEditorRegex", "searchEditor.action.toggleSearchEditorContextLines", "searchEditor.action.increaseSearchEditorContextLines", "searchEditor.action.decreaseSearchEditorContextLines", "searchEditor.action.selectAllSearchEditorMatches"]], ["vs/workbench/contrib/searchEditor/browser/searchEditor", ["moreSearch", "searchScope.includes", "label.includes", "searchScope.excludes", "label.excludes", "runSearch", "searchResultItem", "searchEditor", "textInputBoxBorder"]], ["vs/workbench/contrib/searchEditor/browser/searchEditorInput", ["searchEditorLabelIcon", "searchTitle.with<PERSON><PERSON>y", "searchTitle.with<PERSON><PERSON>y", "searchTitle"]], ["vs/workbench/contrib/searchEditor/browser/searchEditorSerialization", ["invalid<PERSON>ueryStringError", "numFiles", "oneFile", "numResults", "oneResult", "noResults", "searchMaxResultsWarning"]], ["vs/workbench/contrib/share/browser/share.contribution", ["generating link", "shareTextSuccess", "shareSuccess", "close", "open link", "experimental.share.enabled", "share"]], ["vs/workbench/contrib/share/browser/shareService", ["shareProviderCount", "type to filter", "toggle.share", "toggle.shareDescription"]], ["vs/workbench/contrib/snippets/browser/commands/abstractSnippetsActions", ["snippets"]], ["vs/workbench/contrib/snippets/browser/commands/configureSnippets", ["global.scope", "global.1", "detail.label", "name", "bad_name1", "bad_name2", "bad_name3", "miOpenSnippets", "new.global_scope", "new.global", "new.workspace_scope", "new.folder", "group.global", "new.global.sep", "new.global.sep", "openSnippet.pickLanguage", "openSnippet.label", "userSnippets"]], ["vs/workbench/contrib/snippets/browser/commands/fileTemplateSnippets", ["placeholder", "label"]], ["vs/workbench/contrib/snippets/browser/commands/insertSnippet", ["snippet.suggestions.label"]], ["vs/workbench/contrib/snippets/browser/commands/surroundWithSnippet", ["label"]], ["vs/workbench/contrib/snippets/browser/snippetCodeActionProvider", ["more", "codeAction", "overflow.start.title", "title"]], ["vs/workbench/contrib/snippets/browser/snippetCompletionProvider", ["detail.snippet", "snippetSuggest.longLabel", "snippetSuggest.longLabel"]], ["vs/workbench/contrib/snippets/browser/snippetPicker", ["sep.userSnippet", "sep.workspaceSnippet", "disableSnippet", "isDisabled", "enable.snippet", "pick.placeholder", "pick.noSnippetAvailable"]], ["vs/workbench/contrib/snippets/browser/snippets.contribution", ["editor.snippets.codeActions.enabled", "snippetSchema.json.prefix", "snippetSchema.json.isFileTemplate", "snippetSchema.json.body", "snippetSchema.json.description", "snippetSchema.json.default", "snippetSchema.json", "snippetSchema.json.default", "snippetSchema.json", "snippetSchema.json.scope"]], ["vs/workbench/contrib/snippets/browser/snippetsFile", ["source.workspaceSnippetGlobal", "source.userSnippetGlobal", "source.userSnippet"]], ["vs/workbench/contrib/snippets/browser/snippetsService", ["invalid.path.0", "invalid.language.0", "invalid.language", "invalid.path.1", "vscode.extension.contributes.snippets", "vscode.extension.contributes.snippets-language", "vscode.extension.contributes.snippets-path", "badVariableUse", "badFile"]], ["vs/workbench/contrib/speech/browser/speechService", ["vscode.extension.contributes.speechProvider", "speechProviderName", "speechProviderDescription"]], ["vs/workbench/contrib/speech/common/speechService", ["hasSpeechProvider", "speechToTextInProgress", "textToSpeechInProgress", "speechLanguage.da-DK", "speechLanguage.de-DE", "speechLanguage.en-AU", "speechLanguage.en-CA", "speechLanguage.en-GB", "speechLanguage.en-IE", "speechLanguage.en-IN", "speechLanguage.en-NZ", "speechLanguage.en-US", "speechLanguage.es-ES", "speechLanguage.es-MX", "speechLanguage.fr-CA", "speechLanguage.fr-FR", "speechLanguage.hi-IN", "speechLanguage.it-IT", "speechLanguage.ja-JP", "speechLanguage.ko-KR", "speechLanguage.nl-NL", "speechLanguage.pt-PT", "speechLanguage.pt-BR", "speechLanguage.ru-RU", "speechLanguage.sv-SE", "speechLanguage.tr-TR", "speechLanguage.zh-CN", "speechLanguage.zh-HK", "speechLanguage.zh-TW"]], ["vs/workbench/contrib/surveys/browser/languageSurveys.contribution", ["helpUs", "takeShortSurvey", "remindLater", "neverAgain"]], ["vs/workbench/contrib/surveys/browser/nps.contribution", ["surveyQuestion", "<PERSON><PERSON><PERSON><PERSON>", "remindLater", "neverAgain"]], ["vs/workbench/contrib/tasks/browser/abstractTaskService", ["tasks", "TaskService.pickBuildTaskForLabel", "taskEvent", "TaskService.skippingReconnection", "TaskService.notConnecting", "TaskService.reconnecting", "TaskService.reconnected", "TaskService.noTasks", "TaskService.reconnectingTasks", "runTask.arg", "runTask.label", "runTask.type", "runTask.task", "taskServiceOutputPrompt", "showOutput", "TaskServer.folderIgnored", "TaskService.providerUnavailable", "taskService.gettingCachedTasks", "taskService.getSavedTasks", "taskService.getSavedTasks.reading", "taskService.getSavedTasks.error", "taskService.getSavedTasks.resolved", "taskService.getSavedTasks.unresolved", "taskService.removePersistentTask", "taskService.setPersistentTask", "savePersistentTask", "TaskService.noTestTask1", "TaskService.noTestTask2", "TaskService.noBuildTask1", "TaskService.noBuildTask2", "TaskServer.noTask", "TaskService.associate", "TaskService.attachProblemMatcher.continueWithout", "TaskService.attachProblemMatcher.never", "TaskService.attachProblemMatcher.neverType", "TaskService.attachProblemMatcher.learnMoreAbout", "selectProblemMatcher", "customizeParseErrors", "tasksJsonComment", "moreThanOneBuildTask", "TaskSystem.saveBeforeRun.prompt.title", "detail", "saveBeforeRun.save", "saveBeforeRun.dontSave", "TaskSystem.activeSame.noBackground", "terminateTask", "restartTask", "TaskSystem.active", "TaskSystem.restartFailed", "unexpectedTaskType", "TaskService.providerUnavailable", "TaskService.noConfiguration", "TaskSystem.configurationErrors", "TaskSystem.invalidTaskJsonOther", "TasksSystem.locationWorkspaceConfig", "TaskSystem.versionWorkspaceFile", "TasksSystem.locationUserConfig", "TaskSystem.versionSettings", "TaskSystem.configurationErrors", "taskService.ignoringFolder", "TaskSystem.invalidTaskJson", "TerminateAction.label", "TaskSystem.unknownError", "configureTask", "recentlyUsed", "configured", "detected", "TaskService.ignoredFolder", "TaskService.notAgain", "TaskService.requestTrust", "TaskService.pickRunTask", "TaskService.noEntryToRun", "TaskService.noEntryToRun", "TaskService.fetchingBuildTasks", "TaskService.pickBuildTask", "TaskService.noBuildTask", "TaskService.fetchingTestTasks", "TaskService.pickTestTask", "TaskService.noTestTaskTerminal", "TaskService.taskToTerminate", "TaskService.noTaskRunning", "TaskService.terminateAllRunningTasks", "TerminateAction.noProcess", "TerminateAction.failed", "TaskService.taskToRestart", "TaskService.noTaskToRestart", "TaskService.template", "taskQuickPick.userSettings", "TaskService.createJsonFile", "TaskService.openJsonFile", "TaskService.pickTask", "TaskService.defaultBuildTaskExists", "TaskService.pickTask", "TaskService.pickDefaultBuildTask", "TaskService.defaultTestTaskExists", "TaskService.pickDefaultTestTask", "TaskService.pickShowTask", "TaskService.noTaskIsRunning", "taskService.upgradeVersion", "taskService.upgradeVersionPlural", "taskService.openDiff", "taskService.openDiffs", "ConfigureTaskRunnerAction.label"]], ["vs/workbench/contrib/tasks/browser/runAutomaticTasks", ["workbench.action.tasks.manageAutomaticRunning", "workbench.action.tasks.allowAutomaticTasks", "workbench.action.tasks.disallowAutomaticTasks"]], ["vs/workbench/contrib/tasks/browser/task.contribution", ["building", "status.runningTasks", "numberOfRunningTasks", "runningTasks", "miRunTask", "miBuildTask", "miRunningTask", "miRestartTask", "miTerminateTask", "miConfigureTask", "miConfigureBuildTask", "tasks", "tasksQuickAccessPlaceholder", "tasksQuickAccessHelp", "tasksConfigurationTitle", "task.problemMatchers.neverPrompt", "task.problemMatchers.neverPrompt.boolean", "task.problemMatchers.neverPrompt.array", "task.autoDetect", "task.slowProviderWarning", "task.slowProviderWarning.boolean", "task.slowProviderWarning.array", "task.quickOpen.history", "task.quickOpen.detail", "task.quickOpen.skip", "task.quickOpen.showAll", "task.allowAutomaticTasks.on", "task.allowAutomaticTasks.off", "task.allowAutomaticTasks", "task.reconnection", "task.saveBeforeRun", "task.saveBeforeRun.always", "task.saveBeforeRun.never", "task.SaveBeforeRun.prompt", "task.verboseLogging", "rerunTaskIcon", "workbench.action.tasks.openWorkspaceFileTasks", "ShowLogAction.label", "RunTaskAction.label", "ReRunTaskAction.label", "RestartTaskAction.label", "ShowTasksAction.label", "TerminateAction.label", "BuildAction.label", "TestAction.label", "ConfigureDefaultBuildTask.label", "ConfigureDefaultTestTask.label", "workbench.action.tasks.openUserTasks", "workbench.action.tasks.rerunForActiveTerminal"]], ["vs/workbench/contrib/tasks/browser/taskQuickPick", ["taskQuickPick.showAll", "configureTaskIcon", "removeTaskIcon", "configureTask", "contributedTasks", "taskType", "removeRecent", "recentlyUsed", "configured", "configured", "TaskQuickPick.changeSettingDetails", "TaskQuickPick.changeSettingNo", "TaskService.pickRunTask", "TaskQuickPick.changeSettingsOptions", "TaskQuickPick.goBack", "TaskQuickPick.noTasksForType", "noProviderForTask"]], ["vs/workbench/contrib/tasks/browser/taskService", ["taskService.processTaskSystem"]], ["vs/workbench/contrib/tasks/browser/tasksQuickAccess", ["noTaskResults", "TaskService.pickRunTask"]], ["vs/workbench/contrib/tasks/browser/taskTerminalStatus", ["taskTerminalStatus.active", "taskTerminalStatus.succeeded", "taskTerminalStatus.succeededInactive", "taskTerminalStatus.errors", "taskTerminalStatus.errorsInactive", "taskTerminalStatus.warnings", "taskTerminalStatus.warningsInactive", "taskTerminalStatus.infos", "taskTerminalStatus.infosInactive", "task.watchFirstError"]], ["vs/workbench/contrib/tasks/browser/terminalTaskSystem", ["rerunTask", "TerminalTaskSystem.unknownError", "TerminalTaskSystem.taskLoadReporting", "dependencyCycle", "dependencyFailed", "TerminalTaskSystem.nonWatchingMatcher", "task.executingInFolder", "task.executing.shellIntegration", "task.executingInFolder", "task.executing.shell-integration", "task.executing", "TerminalTaskSystem", "unknown<PERSON><PERSON>blemM<PERSON><PERSON>", "closeTerminal", "reuseTerminal"]], ["vs/workbench/contrib/tasks/common/jsonSchema_v1", ["JsonSchema.version.deprecated", "JsonSchema.version", "JsonSchema._runner", "JsonSchema.runner", "JsonSchema.windows", "JsonSchema.mac", "JsonSchema.linux", "JsonSchema.shell"]], ["vs/workbench/contrib/tasks/common/jsonSchema_v2", ["JsonSchema.shell", "JsonSchema.tasks.isShellCommand.deprecated", "JsonSchema.hide", "JsonSchema.tasks.dependsOn.identifier", "JsonSchema.tasks.dependsOn.string", "JsonSchema.tasks.dependsOn.array", "JsonSchema.tasks.dependsOn", "JsonSchema.tasks.dependsOrder.parallel", "JsonSchema.tasks.dependsOrder.sequence", "JsonSchema.tasks.dependsOrder", "JsonSchema.tasks.detail", "JsonSchema.tasks.icon", "JsonSchema.tasks.icon.id", "JsonSchema.tasks.icon.color", "JsonSchema.tasks.presentation", "JsonSchema.tasks.presentation.echo", "JsonSchema.tasks.presentation.focus", "JsonSchema.tasks.presentation.revealProblems.always", "JsonSchema.tasks.presentation.revealProblems.onProblem", "JsonSchema.tasks.presentation.revealProblems.never", "JsonSchema.tasks.presentation.revealProblems", "JsonSchema.tasks.presentation.reveal.always", "JsonSchema.tasks.presentation.reveal.silent", "JsonSchema.tasks.presentation.reveal.never", "JsonSchema.tasks.presentation.reveal", "JsonSchema.tasks.presentation.instance", "JsonSchema.tasks.presentation.showReuseMessage", "JsonSchema.tasks.presentation.clear", "JsonSchema.tasks.presentation.group", "JsonSchema.tasks.presentation.close", "JsonSchema.tasks.terminal", "JsonSchema.tasks.group.build", "JsonSchema.tasks.group.test", "JsonSchema.tasks.group.none", "JsonSchema.tasks.group.kind", "JsonSchema.tasks.group.isDefault", "JsonSchema.tasks.group.defaultBuild", "JsonSchema.tasks.group.defaultTest", "JsonSchema.tasks.group", "JsonSchema.tasks.type", "JsonSchema.commandArray", "JsonSchema.commandArray", "JsonSchema.command.quotedString.value", "JsonSchema.tasks.quoting.escape", "JsonSchema.tasks.quoting.strong", "JsonSchema.tasks.quoting.weak", "JsonSchema.command.quotesString.quote", "JsonSchema.command", "JsonSchema.args.quotedString.value", "JsonSchema.tasks.quoting.escape", "JsonSchema.tasks.quoting.strong", "JsonSchema.tasks.quoting.weak", "JsonSchema.args.quotesString.quote", "JsonSchema.tasks.args", "JsonSchema.tasks.label", "JsonSchema.version", "JsonSchema.tasks.identifier", "JsonSchema.tasks.identifier.deprecated", "JsonSchema.tasks.reevaluateOnRerun", "JsonSchema.tasks.runOn", "JsonSchema.tasks.instanceLimit", "JsonSchema.tasks.runOptions", "JsonSchema.tasks.taskLabel", "JsonSchema.tasks.taskName", "JsonSchema.tasks.taskName.deprecated", "JsonSchema.tasks.background", "JsonSchema.tasks.promptOnClose", "JsonSchema.tasks.matchers", "JsonSchema.customizations.customizes.type", "JsonSchema.tasks.customize.deprecated", "JsonSchema.tasks.taskName.deprecated", "JsonSchema.tasks.showOutput.deprecated", "JsonSchema.tasks.echoCommand.deprecated", "JsonSchema.tasks.suppressTaskName.deprecated", "JsonSchema.tasks.isBuildCommand.deprecated", "JsonSchema.tasks.isTestCommand.deprecated", "JsonSchema.tasks.type", "JsonSchema.tasks.suppressTaskName.deprecated", "JsonSchema.tasks.taskSelector.deprecated", "JsonSchema.windows", "JsonSchema.mac", "JsonSchema.linux"]], ["vs/workbench/contrib/tasks/common/jsonSchemaCommon", ["JsonSchema.options", "JsonSchema.options.cwd", "JsonSchema.options.env", "JsonSchema.tasks.matcherError", "JsonSchema.tasks.matcherError", "JsonSchema.shellConfiguration", "JsonSchema.shell.executable", "JsonSchema.shell.args", "JsonSchema.command", "JsonSchema.tasks.args", "JsonSchema.tasks.taskName", "JsonSchema.command", "JsonSchema.tasks.args", "JsonSchema.tasks.windows", "JsonSchema.tasks.matchers", "JsonSchema.tasks.mac", "JsonSchema.tasks.matchers", "JsonSchema.tasks.linux", "JsonSchema.tasks.matchers", "JsonSchema.tasks.suppressTaskName", "JsonSchema.tasks.showOutput", "JsonSchema.echoCommand", "JsonSchema.tasks.watching.deprecation", "JsonSchema.tasks.watching", "JsonSchema.tasks.background", "JsonSchema.tasks.promptOnClose", "JsonSchema.tasks.build", "JsonSchema.tasks.test", "JsonSchema.tasks.matchers", "JsonSchema.command", "JsonSchema.args", "JsonSchema.showOutput", "JsonSchema.watching.deprecation", "JsonSchema.watching", "JsonSchema.background", "JsonSchema.promptOnClose", "JsonSchema.echoCommand", "JsonSchema.suppressTaskName", "JsonSchema.taskSelector", "JsonSchema.matchers", "JsonSchema.tasks"]], ["vs/workbench/contrib/tasks/common/problemMatcher", ["ProblemPatternParser.problemPattern.missingRegExp", "ProblemPatternParser.loopProperty.notLast", "ProblemPatternParser.problemPattern.kindProperty.notFirst", "ProblemPatternParser.problemPattern.missingProperty", "ProblemPatternParser.problemPattern.missingLocation", "ProblemPatternParser.invalidRegexp", "ProblemPatternSchema.regexp", "ProblemPatternSchema.kind", "ProblemPatternSchema.file", "ProblemPatternSchema.location", "ProblemPatternSchema.line", "ProblemPatternSchema.column", "ProblemPatternSchema.endLine", "ProblemPatternSchema.endColumn", "ProblemPatternSchema.severity", "ProblemPatternSchema.code", "ProblemPatternSchema.message", "ProblemPatternSchema.loop", "NamedProblemPatternSchema.name", "NamedMultiLineProblemPatternSchema.name", "NamedMultiLineProblemPatternSchema.patterns", "WatchingPatternSchema.regexp", "WatchingPatternSchema.file", "PatternTypeSchema.name", "PatternTypeSchema.description", "ProblemMatcherSchema.base", "ProblemMatcherSchema.owner", "ProblemMatcherSchema.source", "ProblemMatcherSchema.severity", "ProblemMatcherSchema.applyTo", "ProblemMatcherSchema.fileLocation", "ProblemMatcherSchema.background", "ProblemMatcherSchema.background.activeOnStart", "ProblemMatcherSchema.background.beginsPattern", "ProblemMatcherSchema.background.endsPattern", "ProblemMatcherSchema.watching.deprecated", "ProblemMatcherSchema.watching", "ProblemMatcherSchema.watching.activeOnStart", "ProblemMatcherSchema.watching.beginsPattern", "ProblemMatcherSchema.watching.endsPattern", "LegacyProblemMatcherSchema.watchedBegin.deprecated", "LegacyProblemMatcherSchema.watchedBegin", "LegacyProblemMatcherSchema.watchedEnd.deprecated", "LegacyProblemMatcherSchema.watchedEnd", "NamedProblemMatcherSchema.name", "NamedProblemMatcherSchema.label", "ProblemPatternExtPoint", "ProblemPatternRegistry.error", "ProblemPatternRegistry.error", "ProblemMatcherParser.noProblemMatcher", "ProblemMatcherParser.noProblemPattern", "ProblemMatcherParser.noOwner", "ProblemMatcherParser.noFileLocation", "ProblemMatcherParser.unknownSeverity", "ProblemMatcherParser.noDefinedPatter", "ProblemMatcherParser.noIdentifier", "ProblemMatcherParser.noValidIdentifier", "ProblemMatcherParser.problemPattern.watchingMatcher", "ProblemMatcherParser.invalidRegexp", "ProblemMatcherExtPoint", "msCompile", "lessCompile", "gulp-tsc", "j<PERSON>t", "jshint-stylish", "eslint-compact", "eslint-stylish", "go"]], ["vs/workbench/contrib/tasks/common/taskConfiguration", ["ConfigurationParser.invalidCWD", "ConfigurationParser.inValidArg", "ConfigurationParser.noShell", "ConfigurationParser.noName", "ConfigurationParser.unknownMatcherKind", "ConfigurationParser.invalidVariableReference", "ConfigurationParser.noTaskType", "ConfigurationParser.noTypeDefinition", "ConfigurationParser.missingType", "ConfigurationParser.incorrectType", "ConfigurationParser.notCustom", "ConfigurationParser.noTaskName", "taskConfiguration.providerUnavailable", "taskConfiguration.noCommandOrDependsOn", "taskConfiguration.noCommand", "TaskParse.noOsSpecificGlobalTasks"]], ["vs/workbench/contrib/tasks/common/taskDefinitionRegistry", ["TaskDefinition.description", "TaskDefinition.properties", "TaskDefinition.when", "TaskTypeConfiguration.noType", "TaskDefinitionExtPoint"]], ["vs/workbench/contrib/tasks/common/tasks", ["tasks.taskRunningContext", "taskTerminalActive", "TaskDefinition.missingRequiredProperty", "tasksCategory"]], ["vs/workbench/contrib/tasks/common/taskService", ["tasks.customExecutionSupported", "tasks.shellExecutionSupported", "tasks.taskCommandsRegistered", "tasks.processExecutionSupported", "tasks.serverlessWebContext"]], ["vs/workbench/contrib/tasks/common/taskTemplates", ["dotnetCore", "msbuild", "externalCommand", "<PERSON><PERSON>"]], ["vs/workbench/contrib/tasks/electron-sandbox/taskService", ["TaskSystem.runningTask", "TaskSystem.terminateTask", "TaskSystem.noProcess", "TaskSystem.exitAnyways"]], ["vs/workbench/contrib/telemetry/browser/telemetry.contribution", ["showTelemetry"]], ["vs/workbench/contrib/terminal/browser/baseTerminalBackend", ["ptyHostStatus", "ptyHostStatus.short", "nonResponsivePtyHost", "ptyHostStatus.ariaLabel"]], ["vs/workbench/contrib/terminal/browser/environmentVariableInfo", ["extensionEnvironmentContributionInfoStale", "relaunchTerminalLabel", "extensionEnvironmentContributionInfoActive", "showEnvironmentContributions", "ScopedEnvironmentContributionInfo"]], ["vs/workbench/contrib/terminal/browser/terminal.contribution", ["miToggleIntegratedTerminal", "terminal", "terminal"]], ["vs/workbench/contrib/terminal/browser/terminalActions", ["showTerminalTabs", "workbench.action.terminal.newWorkspacePlaceholder", "terminalLaunchHelp", "workbench.action.terminal.runActiveFile.noFile", "noUnattachedTerminals", "sendSequence", "workbench.action.terminal.newWithCwd.cwd", "workbench.action.terminal.renameWithArg.name", "workbench.action.terminal.renameWithArg.noName", "workbench.action.terminal.join.insufficientTerminals", "workbench.action.terminal.join.onlySplits", "emptyTerminalNameInfo", "workbench.action.terminal.newWithProfile.profileName", "newWithProfile.location", "newWithProfile.location.view", "newWithProfile.location.editor", "workbench.action.terminal.newWorkspacePlaceholder", "workbench.action.terminal.overriddenCwdDescription", "workbench.action.terminal.newWorkspacePlaceholder", "workbench.action.terminal.rename.prompt", "workbench.action.terminal.newInActiveWorkspace", "workbench.action.terminal.createTerminalEditor", "workbench.action.terminal.createTerminalEditor", "workbench.action.terminal.createTerminalEditorSide", "workbench.action.terminal.focusPreviousPane", "workbench.action.terminal.focusNextPane", "workbench.action.terminal.resizePaneLeft", "workbench.action.terminal.resizePaneRight", "workbench.action.terminal.resizePaneUp", "workbench.action.terminal.resizePaneDown", "workbench.action.terminal.focus.tabsView", "workbench.action.terminal.focusNext", "workbench.action.terminal.focusPrevious", "workbench.action.terminal.runSelectedText", "workbench.action.terminal.runActiveFile", "workbench.action.terminal.scrollDown", "workbench.action.terminal.scrollDownPage", "workbench.action.terminal.scrollToBottom", "workbench.action.terminal.scrollUp", "workbench.action.terminal.scrollUpPage", "workbench.action.terminal.scrollToTop", "workbench.action.terminal.clearSelection", "workbench.action.terminal.detachSession", "workbench.action.terminal.attachToSession", "workbench.action.terminal.selectToPreviousCommand", "workbench.action.terminal.selectToNextCommand", "workbench.action.terminal.selectToPreviousLine", "workbench.action.terminal.selectToNextLine", "workbench.action.terminal.relaunch", "workbench.action.terminal.joinInstance", "workbench.action.terminal.join", "workbench.action.terminal.splitInActiveWorkspace", "workbench.action.terminal.selectAll", "workbench.action.terminal.new", "workbench.action.terminal.kill", "workbench.action.terminal.killAll", "workbench.action.terminal.killEditor", "workbench.action.terminal.clear", "workbench.action.terminal.selectDefaultShell", "workbench.action.terminal.openSettings", "workbench.action.terminal.setFixedDimensions", "workbench.action.terminal.switchTerminal", "workbench.action.terminal.newWithProfile"]], ["vs/workbench/contrib/terminal/browser/terminalEditorInput", ["confirmDirtyTerminal.message", "confirmDirtyTerminal.button", "confirmDirtyTerminals.detail", "confirmDirtyTerminal.detail"]], ["vs/workbench/contrib/terminal/browser/terminalIcons", ["terminalViewIcon", "renameTerminalIcon", "killTerminalIcon", "newTerminalIcon", "configureTerminalProfileIcon", "terminalDecorationMark", "terminalDecorationIncomplete", "terminalDecorationError", "terminalDecorationSuccess", "terminalCommandHistoryRemove", "terminalCommandHistoryOutput", "terminalCommandHistoryFuzzySearch", "terminalCommandHistoryOpenFile"]], ["vs/workbench/contrib/terminal/browser/terminalInstance", ["terminal.integrated.a11yPromptLabel", "terminal.integrated.useAccessibleBuffer", "terminal.integrated.useAccessibleBufferNoKb", "bellStatus", "keybindingHandling", "configureTerminalSettings", "disconnectStatus", "workspaceNotTrustedCreateTerminal", "workspaceNotTrustedCreateTerminalCwd", "launchFailed.exitCodeOnlyShellIntegration", "shellIntegration.learnMore", "shellIntegration.openSettings", "terminal.requestTrust", "terminalTextBoxAriaLabelNumberAndTitle", "terminalTextBoxAriaLabel", "terminalScreenReaderMode", "terminalHelpAriaLabel", "setTerminalDimensionsColumn", "setTerminalDimensionsRow", "terminalStaleTextBoxAriaLabel", "changeColor", "launchFailed.exitCodeAndCommandLine", "launchFailed.exitCodeOnly", "terminated.exitCodeAndCommandLine", "terminated.exitCodeOnly", "launchFailed.errorMessage"]], ["vs/workbench/contrib/terminal/browser/terminalMenus", ["miNewTerminal", "miSplitTerminal", "miRunActiveFile", "miRunSelectedText", "workbench.action.terminal.copySelection.short", "workbench.action.terminal.copySelectionAsHtml", "workbench.action.terminal.paste.short", "workbench.action.terminal.clear", "workbench.action.terminal.selectAll", "workbench.action.terminal.copySelection.short", "workbench.action.terminal.copySelectionAsHtml", "workbench.action.terminal.paste.short", "workbench.action.terminal.clear", "workbench.action.terminal.selectAll", "workbench.action.terminal.newWithProfile.short", "workbench.action.terminal.openSettings", "workbench.action.tasks.runTask", "workbench.action.tasks.configureTaskRunner", "workbench.action.terminal.clearLong", "workbench.action.terminal.runActiveFile", "workbench.action.terminal.runSelectedText", "workbench.action.terminal.renameInstance", "workbench.action.terminal.changeIcon", "workbench.action.terminal.changeColor", "workbench.action.terminal.joinInstance", "defaultTerminalProfile", "defaultTerminalProfile", "defaultTerminalProfile", "splitTerminal", "launchProfile", "workbench.action.terminal.selectDefaultProfile", "workbench.action.terminal.switchTerminal"]], ["vs/workbench/contrib/terminal/browser/terminalProcessManager", ["killportfailure", "ptyHostRelaunch"]], ["vs/workbench/contrib/terminal/browser/terminalProfileQuickpick", ["terminal.integrated.selectProfileToCreate", "terminal.integrated.chooseDefaultProfile", "enterTerminalProfileName", "terminalProfileAlreadyExists", "terminalProfiles", "ICreateContributedTerminalProfileOptions", "terminalProfiles.detected", "unsafePathWarning", "yes", "cancel", "createQuickLaunchProfile"]], ["vs/workbench/contrib/terminal/browser/terminalService", ["terminalService.terminalCloseConfirmationSingular", "terminalService.terminalCloseConfirmationPlural", "terminate", "localTerminalVirtualWorkspace", "localTerminalRemote"]], ["vs/workbench/contrib/terminal/browser/terminalTabbedView", ["moveTabsRight", "moveTabsLeft", "hideTabs"]], ["vs/workbench/contrib/terminal/browser/terminalTabsList", ["terminalInputAriaLabel", "terminal.tabs", "splitTerminalAriaLabel", "terminalAriaLabel", "label"]], ["vs/workbench/contrib/terminal/browser/terminalTooltip", ["hideDetails", "showDetails", "shellProcessTooltip.processId", "shellProcessTooltip.commandLine", "shellIntegration.rich", "shellIntegration.basic", "shellIntegration.injectionFailed", "shellIntegration.no", "shellIntegration", "shellIntegration"]], ["vs/workbench/contrib/terminal/browser/terminalView", ["terminal.useMonospace", "terminal.monospaceOnly", "terminals", "terminalConnectingLabel"]], ["vs/workbench/contrib/terminal/browser/xterm/decorationAddon", ["workbench.action.terminal.toggleVisibility", "terminal.rerunCommand", "rerun", "yes", "no", "terminal.copyCommand", "terminal.copyCommandAndOutput", "terminal.copyOutput", "terminal.copyOutputAsHtml", "workbench.action.terminal.runRecentCommand", "workbench.action.terminal.goToRecentDirectory", "terminal.learnShellIntegration", "toggleVisibility", "gutter", "overviewRuler"]], ["vs/workbench/contrib/terminal/browser/xterm/decorationStyles", ["terminalPromptContextMenu", "terminalPromptCommandFailed.duration", "terminalPromptCommandFailedWithExitCode.duration", "terminalPromptCommandSuccess.duration", "terminalPromptCommandFailed", "terminalPromptCommandFailedWithExitCode", "terminalPromptCommandSuccess"]], ["vs/workbench/contrib/terminal/browser/xterm/xtermTerminal", ["terminal.integrated.copySelection.noSelection"]], ["vs/workbench/contrib/terminal/common/terminal", ["vscode.extension.contributes.terminal", "vscode.extension.contributes.terminal.profiles", "vscode.extension.contributes.terminal.profiles.id", "vscode.extension.contributes.terminal.profiles.title", "vscode.extension.contributes.terminal.types.icon", "vscode.extension.contributes.terminal.types.icon.light", "vscode.extension.contributes.terminal.types.icon.dark"]], ["vs/workbench/contrib/terminal/common/terminalColorRegistry", ["terminal.background", "terminal.foreground", "terminalCursor.foreground", "terminalCursor.background", "terminal.selectionBackground", "terminal.inactiveSelectionBackground", "terminal.selectionForeground", "terminalCommandDecoration.defaultBackground", "terminalCommandDecoration.successBackground", "terminalCommandDecoration.errorBackground", "terminalOverviewRuler.cursorForeground", "terminal.border", "terminalOverviewRuler.border", "terminal.findMatchBackground", "terminal.findMatchHighlightBorder", "terminal.findMatchBorder", "terminal.findMatchHighlightBackground", "terminal.findMatchHighlightBorder", "terminalOverviewRuler.findMatchHighlightForeground", "terminal.dragAndDropBackground", "terminal.tab.activeBorder", "terminalInitialHintForeground", "terminal.ansiColor"]], ["vs/workbench/contrib/terminal/common/terminalConfiguration", ["cwd", "cwdFolder", "workspaceFolder", "workspaceFolderName", "local", "process", "progress", "separator", "sequence", "task", "shellType", "shellCommand", "shellPromptInput", "terminalTitle", "terminalDescription", "terminalIntegratedConfigurationTitle", "terminal.integrated.sendKeybindingsToShell", "terminal.integrated.tabs.defaultColor", "terminal.integrated.tabs.defaultIcon", "terminal.integrated.tabs.enabled", "terminal.integrated.tabs.enableAnimation", "terminal.integrated.tabs.hideCondition", "terminal.integrated.tabs.hideCondition.never", "terminal.integrated.tabs.hideCondition.singleTerminal", "terminal.integrated.tabs.hideCondition.singleGroup", "terminal.integrated.tabs.showActiveTerminal", "terminal.integrated.tabs.showActiveTerminal.always", "terminal.integrated.tabs.showActiveTerminal.singleTerminal", "terminal.integrated.tabs.showActiveTerminal.singleTerminalOrNarrow", "terminal.integrated.tabs.showActiveTerminal.never", "terminal.integrated.tabs.showActions", "terminal.integrated.tabs.showActions.always", "terminal.integrated.tabs.showActions.singleTerminal", "terminal.integrated.tabs.showActions.singleTerminalOrNarrow", "terminal.integrated.tabs.showActions.never", "terminal.integrated.tabs.location.left", "terminal.integrated.tabs.location.right", "terminal.integrated.tabs.location", "terminal.integrated.defaultLocation.editor", "terminal.integrated.defaultLocation.view", "terminal.integrated.defaultLocation", "terminal.integrated.tabs.focusMode.singleClick", "terminal.integrated.tabs.focusMode.doubleClick", "terminal.integrated.tabs.focusMode", "terminal.integrated.macOptionIsMeta", "terminal.integrated.macOptionClickForcesSelection", "terminal.integrated.altClickMovesCursor", "terminal.integrated.copyOnSelection", "terminal.integrated.enableMultiLinePasteWarning", "terminal.integrated.enableMultiLinePasteWarning.auto", "terminal.integrated.enableMultiLinePasteWarning.always", "terminal.integrated.enableMultiLinePasteWarning.never", "terminal.integrated.drawBoldTextInBrightColors", "terminal.integrated.fontFamily", "terminal.integrated.fontLigatures.enabled", "terminal.integrated.fontLigatures.featureSettings", "terminal.integrated.fontLigatures.fallbackLigatures", "terminal.integrated.fontSize", "terminal.integrated.letterSpacing", "terminal.integrated.lineHeight", "terminal.integrated.minimumContrastRatio", "terminal.integrated.tabStopWidth", "terminal.integrated.fastScrollSensitivity", "terminal.integrated.mouseWheelScrollSensitivity", "terminal.integrated.bellDuration", "terminal.integrated.fontWeightError", "terminal.integrated.fontWeight", "terminal.integrated.fontWeightError", "terminal.integrated.fontWeightBold", "terminal.integrated.cursorBlinking", "terminal.integrated.cursorStyle", "terminal.integrated.cursorStyleInactive", "terminal.integrated.cursorWidth", "terminal.integrated.scrollback", "terminal.integrated.detectLocale", "terminal.integrated.detectLocale.auto", "terminal.integrated.detectLocale.off", "terminal.integrated.detectLocale.on", "terminal.integrated.gpuAcceleration.auto", "terminal.integrated.gpuAcceleration.on", "terminal.integrated.gpuAcceleration.off", "terminal.integrated.gpuAcceleration", "terminal.integrated.tabs.separator", "terminal.integrated.rightClickBehavior.default", "terminal.integrated.rightClickBehavior.copyPaste", "terminal.integrated.rightClickBehavior.paste", "terminal.integrated.rightClickBehavior.selectWord", "terminal.integrated.rightClickBehavior.nothing", "terminal.integrated.rightClickBehavior", "terminal.integrated.middleClickBehavior.default", "terminal.integrated.middleClickBehavior.paste", "terminal.integrated.middleClickBehavior", "terminal.integrated.cwd", "terminal.integrated.confirmOnExit", "terminal.integrated.confirmOnExit.never", "terminal.integrated.confirmOnExit.always", "terminal.integrated.confirmOnExit.hasChildProcesses", "terminal.integrated.confirmOnKill", "terminal.integrated.confirmOnKill.never", "terminal.integrated.confirmOnKill.editor", "terminal.integrated.confirmOnKill.panel", "terminal.integrated.confirmOnKill.always", "terminal.integrated.enableBell", "terminal.integrated.enableVisualBell", "terminal.integrated.commandsToSkipShell", "openDefaultSettingsJson", "openDefaultSettingsJson.capitalized", "terminal.integrated.allowChords", "terminal.integrated.allowMnemonics", "terminal.integrated.env.osx", "terminal.integrated.env.linux", "terminal.integrated.env.windows", "terminal.integrated.environmentChangesIndicator", "terminal.integrated.environmentChangesIndicator.off", "terminal.integrated.environmentChangesIndicator.on", "terminal.integrated.environmentChangesIndicator.warnonly", "terminal.integrated.environmentChangesRelaunch", "terminal.integrated.showExitAlert", "terminal.integrated.windowsUseConptyDll", "terminal.integrated.splitCwd", "terminal.integrated.splitCwd.workspaceRoot", "terminal.integrated.splitCwd.initial", "terminal.integrated.splitCwd.inherited", "terminal.integrated.windowsEnableConpty", "terminal.integrated.wordSeparators", "terminal.integrated.enableFileLinks", "enableFileLinks.off", "enableFileLinks.on", "enableFileLinks.notRemote", "terminal.integrated.allowedLinkSchemes", "terminal.integrated.unicodeVersion.six", "terminal.integrated.unicodeVersion.eleven", "terminal.integrated.unicodeVersion", "terminal.integrated.enablePersistentSessions", "terminal.integrated.persistentSessionReviveProcess", "terminal.integrated.persistentSessionReviveProcess.onExit", "terminal.integrated.persistentSessionReviveProcess.onExitAndWindowClose", "terminal.integrated.persistentSessionReviveProcess.never", "terminal.integrated.hideOnStartup", "hideOnStartup.never", "hideOnStartup.whenEmpty", "hideOnStartup.always", "terminal.integrated.hideOnLastClosed", "terminal.integrated.customGlyphs", "terminal.integrated.rescaleOverlappingGlyphs", "terminal.integrated.shellIntegration.enabled", "terminal.integrated.shellIntegration.decorationsEnabled", "terminal.integrated.shellIntegration.decorationsEnabled.both", "terminal.integrated.shellIntegration.decorationsEnabled.gutter", "terminal.integrated.shellIntegration.decorationsEnabled.overviewRuler", "terminal.integrated.shellIntegration.decorationsEnabled.never", "terminal.integrated.shellIntegration.environmentReporting", "terminal.integrated.smoothScrolling", "terminal.integrated.ignoreBracketedPasteMode", "terminal.integrated.enableImages", "terminal.integrated.focusAfterRun", "terminal.integrated.focusAfterRun.terminal", "terminal.integrated.focusAfterRun.accessible-buffer", "terminal.integrated.focusAfterRun.none"]], ["vs/workbench/contrib/terminal/common/terminalContextKey", ["terminalFocusContextKey", "terminalFocusInAnyContextKey", "terminalEditorFocusContextKey", "terminalCountContextKey", "terminalTabsFocusContextKey", "terminalShellTypeContextKey", "terminalAltBufferActive", "terminalSuggestWidgetVisible", "terminalViewShowing", "terminalTextSelectedContextKey", "terminalTextSelectedInFocusedContextKey", "terminalProcessSupportedContextKey", "terminalTabsSingularSelectedContextKey", "isSplitTerminalContextKey", "inTerminalRunCommandPickerContextKey", "terminalShellIntegrationEnabled"]], ["vs/workbench/contrib/terminal/common/terminalStrings", ["terminal", "terminal.new", "doNotShowAgain", "currentSessionCategory", "previousSessionCategory", "task", "local", "killTerminal.short", "splitTerminal.short", "terminalCategory", "workbench.action.terminal.focus", "workbench.action.terminal.focusAndHideAccessibleBuffer", "killTerminal", "moveToEditor", "moveIntoNewWindow", "workbench.action.terminal.moveToTerminalPanel", "workbench.action.terminal.changeIcon", "workbench.action.terminal.changeColor", "splitTerminal", "unsplitTerminal", "workbench.action.terminal.rename", "workbench.action.terminal.sizeToContentWidthInstance", "workbench.action.terminal.focusHover", "workbench.action.terminal.sendSequence", "workbench.action.terminal.newWithCwd", "workbench.action.terminal.renameWithArg", "workbench.action.terminal.scrollToPreviousCommand", "workbench.action.terminal.scrollToNextCommand"]], ["vs/workbench/contrib/terminal/electron-sandbox/terminalRemote", ["workbench.action.terminal.newLocal"]], ["vs/workbench/contrib/terminalContrib/accessibility/browser/terminal.accessibility.contribution", ["workbench.action.terminal.focusAccessibleBuffer", "workbench.action.terminal.accessibleBufferGoToNextCommand", "workbench.action.terminal.accessibleBufferGoToPreviousCommand", "workbench.action.terminal.scrollToBottomAccessibleView", "workbench.action.terminal.scrollToTopAccessibleView"]], ["vs/workbench/contrib/terminalContrib/accessibility/browser/terminalAccessibilityHelp", ["focusAccessibleTerminalView", "preserveCursor", "openDetectedLink", "newWithProfile", "focusAfterRun", "focusViewOnExecution", "<PERSON><PERSON><PERSON><PERSON>", "suggestCommands", "suggestCommandsMore", "commandPromptMigration", "shellIntegration", "goToNextCommand", "goToPreviousCommand", "goToSymbol", "runRecentCommand", "goToRecentDirectory", "noShellIntegration"]], ["vs/workbench/contrib/terminalContrib/accessibility/common/terminalAccessibilityConfiguration", ["terminal.integrated.accessibleViewPreserveCursorPosition", "terminal.integrated.accessibleViewFocusOnCommandExecution"]], ["vs/workbench/contrib/terminalContrib/autoReplies/common/terminalAutoRepliesConfiguration", ["terminal.integrated.autoReplies", "terminal.integrated.autoReplies.reply"]], ["vs/workbench/contrib/terminalContrib/chat/browser/terminal.initialHint.contribution", ["emptyHintText", "hintTextDismiss", "inlineChatHint", "disableHint", "disableInitialHint", "disableInitialHint"]], ["vs/workbench/contrib/terminalContrib/chat/browser/terminalChat", ["chatFocusedContextKey", "chatVisibleContextKey", "chatRequestActiveContextKey", "chatInputHasTextContextKey", "chatResponseContainsCodeBlockContextKey", "chatResponseContainsMultipleCodeBlocksContextKey", "chatAgentRegisteredContextKey"]], ["vs/workbench/contrib/terminalContrib/chat/browser/terminalChatAccessibilityHelp", ["inlineChat.overview", "inlineChat.access", "inlineChat.input", "inlineChat.inputNoKb", "inlineChat.inspectResponseMessage", "inlineChat.inspectResponseNoKb", "inlineChat.focusResponse", "inlineChat.focusResponseNoKb", "inlineChat.focusInput", "inlineChat.focusInputNoKb", "inlineChat.runCommand", "inlineChat.runCommandNoKb", "inlineChat.insertCommand", "inlineChat.insertCommandNoKb", "inlineChat.toolbar", "chat.signals"]], ["vs/workbench/contrib/terminalContrib/chat/browser/terminalChatActions", ["startChat", "closeChat", "runCommand", "run", "runFirstCommand", "<PERSON><PERSON><PERSON><PERSON>", "insertCommand", "insert", "insertFirstCommand", "insertFirst", "chat.rerun.label", "viewInChat"]], ["vs/workbench/contrib/terminalContrib/chat/browser/terminalChatWidget", ["askAI"]], ["vs/workbench/contrib/terminalContrib/chat/common/terminalInitialHintConfiguration", ["terminal.integrated.initialHint"]], ["vs/workbench/contrib/terminalContrib/clipboard/browser/terminal.clipboard.contribution", ["workbench.action.terminal.copyLastCommand", "workbench.action.terminal.copyLastCommandOutput", "workbench.action.terminal.copyLastCommandAndOutput", "workbench.action.terminal.copySelection", "workbench.action.terminal.copyAndClearSelection", "workbench.action.terminal.copySelectionAsHtml", "workbench.action.terminal.paste", "workbench.action.terminal.pasteSelection"]], ["vs/workbench/contrib/terminalContrib/clipboard/browser/terminalClipboard", ["preview", "confirmMoveTrashMessageFilesAndDirectories", "multiLinePasteButton", "multiLinePasteButton.oneLine", "doNotAskAgain"]], ["vs/workbench/contrib/terminalContrib/commandGuide/browser/terminal.commandGuide.contribution", ["terminalCommandGuide.foreground"]], ["vs/workbench/contrib/terminalContrib/commandGuide/common/terminalCommandGuideConfiguration", ["showCommandGuide"]], ["vs/workbench/contrib/terminalContrib/developer/browser/terminal.developer.contribution", ["workbench.action.terminal.writeDataToTerminal.prompt", "workbench.action.terminal.recordSession.recording", "workbench.action.terminal.showTextureAtlas", "workbench.action.terminal.writeDataToTerminal", "workbench.action.terminal.recordSession", "workbench.action.terminal.restartPtyHost"]], ["vs/workbench/contrib/terminalContrib/environmentChanges/browser/terminal.environmentChanges.contribution", ["env<PERSON><PERSON><PERSON>", "extension", "ScopedEnvironmentContributionInfo", "workbench.action.terminal.showEnvironmentContributions"]], ["vs/workbench/contrib/terminalContrib/find/browser/terminal.find.contribution", ["workbench.action.terminal.focusFind", "workbench.action.terminal.hideFind", "workbench.action.terminal.toggleFindRegex", "workbench.action.terminal.toggleFindWholeWord", "workbench.action.terminal.toggleFindCaseSensitive", "workbench.action.terminal.findNext", "workbench.action.terminal.findPrevious", "workbench.action.terminal.searchWorkspace"]], ["vs/workbench/contrib/terminalContrib/history/browser/terminal.history.contribution", ["workbench.action.terminal.clearPreviousSessionHistory", "workbench.action.terminal.goToRecentDirectory", "goToRecentDirectory.metadata", "workbench.action.terminal.runRecentCommand"]], ["vs/workbench/contrib/terminalContrib/history/browser/terminalRunRecentQuickPick", ["removeCommand", "viewCommandOutput", "selectRecentCommandMac", "selectRecentCommand", "openShellHistoryFile", "shellFileHistoryCategory", "selectRecentDirectoryMac", "selectRecentDirectory"]], ["vs/workbench/contrib/terminalContrib/history/common/terminal.history", ["terminal.integrated.shellIntegration.history"]], ["vs/workbench/contrib/terminalContrib/links/browser/terminal.links.contribution", ["workbench.action.terminal.openDetectedLink", "workbench.action.terminal.openLastUrlLink", "workbench.action.terminal.openLastUrlLink.description", "workbench.action.terminal.openLastLocalFileLink"]], ["vs/workbench/contrib/terminalContrib/links/browser/terminalLinkDetectorAdapter", ["searchWorkspace", "openFile", "focusFolder", "openFolder", "followLink"]], ["vs/workbench/contrib/terminalContrib/links/browser/terminalLinkManager", ["scheme", "allow", "terminalLinkHandler.followLinkAlt.mac", "terminalLinkHandler.followLinkAlt", "terminalLinkHandler.followLinkCmd", "terminalLinkHandler.followLinkCtrl", "followLink", "followForwardedLink", "followLinkUrl"]], ["vs/workbench/contrib/terminalContrib/links/browser/terminalLinkQuickpick", ["terminal.integrated.urlLinks", "terminal.integrated.localFileLinks", "terminal.integrated.localFolderLinks", "terminal.integrated.searchLinks", "terminal.integrated.openDetectedLink", "terminal.integrated.urlLinks", "terminal.integrated.localFileLinks", "terminal.integrated.localFolderLinks", "terminal.integrated.searchLinks"]], ["vs/workbench/contrib/terminalContrib/quickAccess/browser/terminal.quickAccess.contribution", ["tasksQuickAccessPlaceholder", "tasksQuickAccessHelp", "quickAccessTerminal"]], ["vs/workbench/contrib/terminalContrib/quickAccess/browser/terminalQuickAccess", ["workbench.action.terminal.newplus", "workbench.action.terminal.newWithProfilePlus", "renameTerminal"]], ["vs/workbench/contrib/terminalContrib/quickFix/browser/quickFixAddon", ["quickFix.command", "quickFix.opener", "codeAction.widget.id.quickfix"]], ["vs/workbench/contrib/terminalContrib/quickFix/browser/terminal.quickFix.contribution", ["workbench.action.terminal.showQuickFixes"]], ["vs/workbench/contrib/terminalContrib/quickFix/browser/terminalQuickFixBuiltinActions", ["terminal.freePort", "terminal.createPR"]], ["vs/workbench/contrib/terminalContrib/quickFix/browser/terminalQuickFixService", ["vscode.extension.contributes.terminalQuickFixes", "vscode.extension.contributes.terminalQuickFixes.id", "vscode.extension.contributes.terminalQuickFixes.commandLineMatcher", "vscode.extension.contributes.terminalQuickFixes.outputMatcher", "vscode.extension.contributes.terminalQuickFixes.commandExitResult", "vscode.extension.contributes.terminalQuickFixes.kind"]], ["vs/workbench/contrib/terminalContrib/stickyScroll/browser/terminal.stickyScroll.contribution", ["stickyScroll", "miStickyScroll", "workbench.action.terminal.toggleStickyScroll"]], ["vs/workbench/contrib/terminalContrib/stickyScroll/browser/terminalStickyScrollColorRegistry", ["terminalStickyScroll.background", "terminalStickyScrollHover.background", "terminalStickyScroll.border"]], ["vs/workbench/contrib/terminalContrib/stickyScroll/browser/terminalStickyScrollOverlay", ["stickyScrollHoverTitle", "labelWithKeybinding", "labelWithKeybinding"]], ["vs/workbench/contrib/terminalContrib/stickyScroll/common/terminalStickyScrollConfiguration", ["stickyScroll.enabled", "stickyScroll.maxLineCount"]], ["vs/workbench/contrib/terminalContrib/suggest/browser/terminal.suggest.contribution", ["workbench.action.terminal.configureSuggestSettings", "workbench.action.terminal.requestCompletions", "workbench.action.terminal.resetSuggestWidgetSize", "workbench.action.terminal.selectPrevSuggestion", "workbench.action.terminal.selectPrevPageSuggestion", "workbench.action.terminal.selectNextSuggestion", "workbench.action.terminal.suggestToggleExplainMode", "workbench.action.terminal.suggestToggleDetailsFocus", "workbench.action.terminal.suggestToggleDetails", "workbench.action.terminal.selectNextPageSuggestion", "workbench.action.terminal.acceptSelectedSuggestion", "workbench.action.terminal.acceptSelectedSuggestionEnter", "workbench.action.terminal.hideSuggestWidget", "workbench.action.terminal.hideSuggestWidgetAndNavigateHistory"]], ["vs/workbench/contrib/terminalContrib/suggest/browser/terminalSuggestAddon", ["file", "folder", "method", "alias", "argument", "option", "optionValue", "flag", "inlineSuggestion", "inlineSuggestionAlwaysOnTop"]], ["vs/workbench/contrib/terminalContrib/suggest/browser/terminalSymbolIcons", ["terminalSymbolIcon.flagForeground", "terminalSymbolIcon.aliasForeground", "terminalSymbolIcon.enumMemberForeground", "terminalSymbolIcon.methodForeground", "terminalSymbolIcon.argumentForeground", "terminalSymbolIcon.optionForeground", "terminalSymbolIcon.inlineSuggestionForeground", "terminalSymbolIcon.fileForeground", "terminalSymbolIcon.folderForeground", "terminalSymbolFlagIcon", "terminalSymbolAliasIcon", "terminalSymbolOptionValue", "terminalSymbolMethodIcon", "terminalSymbolArgumentIcon", "terminalSymbolOptionIcon", "terminalSymbolInlineSuggestionIcon", "terminalSymbolFileIcon", "terminalSymbolFolderIcon"]], ["vs/workbench/contrib/terminalContrib/suggest/common/terminalSuggestConfiguration", ["suggest.enabled", "suggest.providers", "suggest.quickSuggestions", "suggest.quickSuggestions.commands", "suggest.quickSuggestions.arguments", "suggest.quickSuggestions.unknown", "suggest.suggestOnTriggerCharacters", "suggest.runOnEnter", "runOnEnter.ignore", "runOnEnter.never", "runOnEnter.exactMatch", "runOnEnter.exactMatchIgnoreExtension", "runOnEnter.always", "terminalWindowsExecutableSuggestionSetting", "suggest.showStatusBar", "suggest.cdPath", "suggest.cdPath.off", "suggest.cdPath.relative", "suggest.cdPath.absolute", "suggest.inlineSuggestion", "suggest.inlineSuggestion.off", "suggest.inlineSuggestion.alwaysOnTopExceptExactMatch", "suggest.inlineSuggestion.alwaysOnTop", "suggest.upArrowNavigatesHistory"]], ["vs/workbench/contrib/terminalContrib/typeAhead/common/terminalTypeAheadConfiguration", ["terminal.integrated.localEchoLatencyThreshold", "terminal.integrated.localEchoEnabled", "terminal.integrated.localEchoEnabled.on", "terminal.integrated.localEchoEnabled.off", "terminal.integrated.localEchoEnabled.auto", "terminal.integrated.localEchoExcludePrograms", "terminal.integrated.localEchoStyle"]], ["vs/workbench/contrib/terminalContrib/wslRecommendation/browser/terminal.wslRecommendation.contribution", ["useWslExtension.title", "install"]], ["vs/workbench/contrib/terminalContrib/zoom/browser/terminal.zoom.contribution", ["fontZoomIn", "fontZoomOut", "fontZoomReset"]], ["vs/workbench/contrib/terminalContrib/zoom/common/terminal.zoom", ["terminal.integrated.mouseWheelZoom.mac", "terminal.integrated.mouseWheelZoom"]], ["vs/workbench/contrib/testing/browser/codeCoverageDecorations", ["testing.toggleInlineCoverage", "coverage.branches", "coverage.branchNotCovered", "coverage.branchCoveredYes", "coverage.branchCovered", "coverage.declExecutedNo", "coverage.declExecutedCount", "coverage.declExecutedYes", "testing.hideInlineCoverage", "testing.showInlineCoverage", "testing.coverageForTestAvailable", "testing.rerun", "coverage.hideInline", "coverage.toggleInline", "testing.toggleToolbarTitle", "testing.toggleToolbarDesc", "testing.filterActionLabel"]], ["vs/workbench/contrib/testing/browser/codeCoverageDisplayUtils", ["testing.coverageForTest", "changePerTestFilter", "testing.percentCoverage", "testing.allTests", "testing.pickTest"]], ["vs/workbench/contrib/testing/browser/icons", ["testViewIcon", "testingResultsIcon", "testingRunIcon", "testingRerunIcon", "testingRunAllIcon", "testingDebugAllIcon", "testingDebugIcon", "testingCoverageIcon", "testingRunAllWithCoverageIcon", "testingCancelIcon", "filterIcon", "hiddenIcon", "testingShowAsList", "testingShowAsTree", "testingUpdateProfiles", "testingRefreshTests", "testingTurnContinuousRunOn", "testingTurnContinuousRunOff", "testingTurnContinuousRunIsOn", "testingCancelRefreshTests", "testingCoverage", "testingWasCovered", "testingMissingBranch", "testingErrorIcon", "testingFailedIcon", "testingPassedIcon", "testingQueuedIcon", "testingSkippedIcon", "testingUnsetIcon"]], ["vs/workbench/contrib/testing/browser/testCoverageBars", ["statementCoverage", "functionCoverage", "branchCoverage"]], ["vs/workbench/contrib/testing/browser/testCoverageView", ["functionsWithoutCoverage", "filteredToTest", "loadingCoverageDetails", "testCoverageItemLabel", "testCoverageTreeLabel", "testing.coverageSortByLocation", "testing.coverageSortByLocationDescription", "testing.coverageSortByCoverage", "testing.coverageSortByCoverageDescription", "testing.coverageSortByName", "testing.coverageSortByNameDescription", "testing.coverageSortPlaceholder", "testing.changeCoverageFilter", "testing.changeCoverageSort"]], ["vs/workbench/contrib/testing/browser/testExplorerActions", ["testing.toggleContinuousRunOff", "configureProfile", "testing.noProfiles", "testing.selectContinuousProfiles", "discoveringTests", "noTestProvider", "noDebugTestProvider", "noCoverageTestProvider", "noTestsAtCursor", "noTests", "noTestsInFile", "testing.noCoverage", "relatedTests", "noTestFound", "relatedCode", "noRelatedCode", "runSelectedTests", "debugSelectedTests", "coverageSelectedTests", "hideTest", "unhideTest", "unhideAllTests", "debug test", "run with cover test", "testing.runUsing", "run test", "testing.selectDefaultTestProfiles", "testing.toggleContinuousRunOn", "testing.startContinuousRunUsing", "testing.configureProfile", "testing.stopContinuous", "testing.startContinuous", "getSelectedProfiles", "getExplorerSelection", "runAllTests", "debugAllTests", "runAllWithCoverage", "testing.cancelRun", "testing.viewAsList", "testing.viewAsTree", "testing.sortByStatus", "testing.sortByLocation", "testing.sortByDuration", "testing.showMostRecentOutput", "testing.collapseAll", "testing.clearResults", "testing.editFocusedTest", "testing.runAtCursor", "testing.debugAtCursor", "testing.coverageAtCursor", "testing.runCurrentFile", "testing.debugCurrentFile", "testing.coverageCurrentFile", "testing.reRunFailTests", "testing.debugFailTests", "testing.reRunLastRun", "testing.debugLastRun", "testing.coverageLastRun", "testing.searchForTestExtension", "testing.openOutputPeek", "testing.toggleInlineTestOutput", "testing.refreshTests", "testing.cancelTestRefresh", "testing.clearCoverage", "testing.openCoverage", "testing.goToRelatedTest", "testing.peekToRelatedTest", "testing.goToRelatedCode", "testing.peekToRelatedCode"]], ["vs/workbench/contrib/testing/browser/testing.contribution", ["miViewTesting", "noTestProvidersRegistered", "searchForAdditionalTestExtensions", "test", "testResultsPanelName", "testResultsPanelName", "testExplorer", "testCoverage"]], ["vs/workbench/contrib/testing/browser/testingConfigurationUi", ["testConfigurationUi.pick", "updateTestConfiguration"]], ["vs/workbench/contrib/testing/browser/testingDecorations", ["peekTestOutout", "expected.title", "actual.title", "testing.gutterMsg.contextMenu", "testing.gutterMsg.debug", "testing.gutterMsg.coverage", "testing.gutterMsg.run", "run test", "debug test", "coverage test", "testing.runUsing", "peek failure", "reveal test", "run all test", "run all test with coverage", "debug all test", "testOverflowItems", "selectTestToRun"]], ["vs/workbench/contrib/testing/browser/testingExplorerFilter", ["testing.filters.showOnlyFailed", "testing.filters.showOnlyExecuted", "testing.filters.currentFile", "testing.filters.openedFiles", "testing.filters.showExcludedTests", "testing.filters.menu", "testExplorerFilterLabel", "testExplorerFilter", "testing.filters.fuzzyMatch", "testing.filters.showExcludedTests", "testing.filters.removeTestExclusions"]], ["vs/workbench/contrib/testing/browser/testingExplorerView", ["defaultTestProfile", "selectDefaultConfigs", "configureTestProfiles", "testingSelectConfig", "noResults", "testingContinuousBadge", "testingCountBadgePassed", "testingCountBadgeSkipped", "testingCountBadgeFailed", "testingNoTest", "testingFindExtension", "testing.treeElementLabelDuration", "testing.treeElementLabelOutdated", "testExplorer"]], ["vs/workbench/contrib/testing/browser/testingOutputPeek", ["testing.markdownPeekError", "testOutputTitle", "close", "testing.goToNextMessage", "testing.goToNextMessage.description", "testing.goToPreviousMessage", "testing.goToPreviousMessage.description", "testing.collapsePeekStack", "testing.openMessageInEditor", "testing.toggleTestingPeekHistory", "testing.toggleTestingPeekHistory.description"]], ["vs/workbench/contrib/testing/browser/testingProgressUiService", ["testProgress.runningInitial", "testProgress.running", "testProgressWithSkip.running", "testProgress.completed", "testProgressWithSkip.completed"]], ["vs/workbench/contrib/testing/browser/testingViewPaneContainer", ["testing"]], ["vs/workbench/contrib/testing/browser/testResultsView/testResultsOutput", ["testingOutputExpected", "testingOutputActual", "caseNoOutput", "runNoOutput", "runNoOutputForPast"]], ["vs/workbench/contrib/testing/browser/testResultsView/testResultsTree", ["openTestCoverage", "closeTestCoverage", "oneOlderResult", "nOlderResults", "messageMoreLinesN", "messageMoreLines1", "testingPeekLabel", "testing.showResultOutput", "testing.cancelRun", "testing.reRunLastRun", "testing.debugLastRun", "testing.showResultOutput", "testing.reRunTest", "testing.debugTest", "testing.goToTest", "testing.showResultOutput", "testing.revealInExplorer", "run test", "debug test", "testing.goToError"]], ["vs/workbench/contrib/testing/browser/testResultsView/testResultsViewContent", ["testing.callStack.run", "testing.callStack.debug", "testFollowup.more"]], ["vs/workbench/contrib/testing/browser/theme", ["testing.iconFailed", "testing.iconErrored", "testing.iconPassed", "testing.runAction", "testing.iconQueued", "testing.iconUnset", "testing.iconSkipped", "testing.peekBorder", "testing.messagePeekBorder", "testing.peekBorder", "testing.messagePeekHeaderBackground", "testing.coveredBackground", "testing.coveredBorder", "testing.coveredGutterBackground", "testing.uncoveredBranchBackground", "testing.uncoveredBackground", "testing.uncoveredBorder", "testing.uncoveredGutterBackground", "testing.coverCountBadgeBackground", "testing.coverCountBadgeForeground", "testing.message.error.badgeBackground", "testing.message.error.badgeBorder", "testing.message.error.badgeForeground", "testing.message.error.marginBackground", "testing.message.info.decorationForeground", "testing.message.info.marginBackground", "testing.iconErrored.retired", "testing.iconFailed.retired", "testing.iconPassed.retired", "testing.iconQueued.retired", "testing.iconUnset.retired", "testing.iconSkipped.retired"]], ["vs/workbench/contrib/testing/common/configuration", ["testConfigurationTitle", "testing.automaticallyOpenPeekView", "testing.automaticallyOpenPeekView.failureAnywhere", "testing.automaticallyOpenPeekView.failureInVisibleDocument", "testing.automaticallyOpenPeekView.never", "testing.showAllMessages", "testing.automaticallyOpenPeekViewDuringContinuousRun", "testing.countBadge", "testing.countBadge.failed", "testing.countBadge.off", "testing.countBadge.passed", "testing.countBadge.skipped", "testing.followRunningTest", "testing.defaultGutterClickAction", "testing.defaultGutterClickAction.run", "testing.defaultGutterClickAction.debug", "testing.defaultGutterClickAction.coverage", "testing.defaultGutterClickAction.contextMenu", "testing.gutterEnabled", "testing.saveBeforeTest", "testing.openTesting.neverOpen", "testing.openTesting.openOnTestStart", "testing.openTesting.openOnTestFailure", "testing.openTesting.openExplorerOnTestStart", "testing.openTesting", "testing.alwaysRevealTestOnStateChange", "testing.ShowCoverageInExplorer", "testing.displayedCoveragePercent", "testing.displayedCoveragePercent.totalCoverage", "testing.displayedCoveragePercent.statement", "testing.displayedCoveragePercent.minimum", "testing.coverageBarThresholds", "testing.coverageToolbarEnabled"]], ["vs/workbench/contrib/testing/common/constants", ["testState.errored", "testState.failed", "testState.passed", "testState.queued", "testState.running", "testState.skipped", "testState.unset", "testing.treeElementLabel", "testGroup.debug", "testGroup.run", "testGroup.coverage"]], ["vs/workbench/contrib/testing/common/testingContentProvider", ["runNoOutout"]], ["vs/workbench/contrib/testing/common/testingContextKeys", ["testing.canRefresh", "testing.isRefreshing", "testing.isContinuousModeOn", "testing.hasDebuggableTests", "testing.hasRunnableTests", "testing.hasCoverableTests", "testing.hasNonDefaultConfig", "testing.hasConfigurableConfig", "testing.supportsContinuousRun", "testing.isParentRunningContinuously", "testing.activeEditorHasTests", "testing.cursorInsideTestRange", "testing.isTestCoverageOpen", "testing.hasPerTestCoverage", "testing.isCoverageFilteredToTest", "testing.coverageToolbarEnabled", "testing.inlineCoverageEnabled", "testing.canGoToRelatedCode", "testing.canGoToRelatedTest", "testing.peekHasStack", "testing.peekItemType", "testing.controllerId", "testing.testId", "testing.testItemHasUri", "testing.testItemIsHidden", "testing.testMessage", "testing.testResultOutdated", "testing.testResultState", "testing.profile.context.group"]], ["vs/workbench/contrib/testing/common/testResult", ["runFinished", "testUnnamedTask"]], ["vs/workbench/contrib/testing/common/testServiceImpl", ["testTrust", "testError", "testTrust", "testError"]], ["vs/workbench/contrib/testing/common/testTypes", ["testing.runProfileBitset.run", "testing.runProfileBitset.debug", "testing.runProfileBitset.coverage"]], ["vs/workbench/contrib/themes/browser/themes.contribution", ["manageExtensionIcon", "themes.selectMarketplaceTheme", "search.error", "installExtension.confirm", "installExtension.button.ok", "installing extensions", "themes.selectTheme.darkScheme", "themes.selectTheme.lightScheme", "themes.selectTheme.darkHC", "themes.selectTheme.lightHC", "themes.selectTheme.default", "themes.configure.switchingEnabled", "themes.configure.switchingDisabled", "installColorThemes", "browseColorThemes", "themes.category.light", "themes.category.dark", "themes.category.hc", "installIconThemes", "themes.selectIconTheme", "fileIconThemeCategory", "noIconThemeLabel", "noIconThemeDesc", "installProductIconThemes", "browseProductIconThemes", "themes.selectProductIconTheme", "productIconThemeCategory", "defaultProductIconThemeLabel", "manage extension", "cannotToggle", "goToSetting", "themes", "miSelectTheme", "selectTheme.label", "themes.selectIconTheme.label", "themes.selectProductIconTheme.label", "selectTheme.label", "selectIconTheme.label", "selectProductIconTheme.label", "generateColorTheme.label", "toggleLightDarkThemes.label", "browseColorThemeInMarketPlace.label"]], ["vs/workbench/contrib/timeline/browser/timeline.contribution", ["timelineViewIcon", "timelineOpenIcon", "timelineConfigurationTitle", "timeline.pageSize", "timeline.pageOnScroll", "files.openTimeline", "timelineFilter", "filterTimeline"]], ["vs/workbench/contrib/timeline/browser/timelinePane", ["timeline.loadingMore", "timeline.loadMore", "timeline.editorCannotProvideTimeline", "timeline.noTimelineSourcesEnabled", "timeline.noLocalHistoryYet", "timeline.noTimelineInfoFromEnabledSources", "timeline.noTimelineInfo", "timeline.noSCM", "timeline.editorCannotProvideTimeline", "timeline.aria.item", "timeline", "timeline.loading", "timelineRefresh", "timelinePin", "timelineUnpin", "timeline", "refresh", "timeline", "timeline.toggleFollowActiveEditorCommand.follow", "timeline", "timeline.toggleFollowActiveEditorCommand.unfollow", "timeline"]], ["vs/workbench/contrib/typeHierarchy/browser/typeHierarchy.contribution", ["editorHasTypeHierarchyProvider", "typeHierarchyVisible", "typeHierarchyDirection", "no.item", "error", "close", "title", "title.supertypes", "title.subtypes", "title.refocusTypeHierarchy"]], ["vs/workbench/contrib/typeHierarchy/browser/typeHierarchyPeek", ["supertypes", "subtypes", "title.loading", "empt.supertypes", "empt.subtypes"]], ["vs/workbench/contrib/typeHierarchy/browser/typeHierarchyTree", ["tree.aria", "supertypes", "subtypes"]], ["vs/workbench/contrib/update/browser/releaseNotesEditor", ["releaseNotesInputName", "unassigned", "showOnUpdate"]], ["vs/workbench/contrib/update/browser/update.contribution", ["mshowReleaseNotes", "update.noReleaseNotesOnline", "mshowReleaseNotes", "releaseNotesFromFileNone", "pickUpdate", "updateButton", "showReleaseNotes", "showReleaseNotesCurrentFile", "developerCategory", "checkForUpdates", "downloadUpdate", "installUpdate", "restartToUpdate", "openDownloadPage", "applyUpdate"]], ["vs/workbench/contrib/update/browser/update", ["update.noReleaseNotesOnline", "read the release notes", "releaseNotes", "update service disabled", "learn more", "updateIsReady", "checkingForUpdates", "downloading", "updating", "update service", "noUpdatesAvailable", "thereIsUpdateAvailable", "download update", "later", "releaseNotes", "updateAvailable", "installUpdate", "later", "releaseNotes", "updateNow", "later", "releaseNotes", "updateAvailableAfterRestart", "checkForUpdates", "checkingForUpdates2", "download update_1", "DownloadingUpdate", "installUpdate...", "installingUpdate", "showUpdateReleaseNotes", "restartToUpdate", "switchToInsiders", "switchToStable", "relaunchMessage", "relaunchDetailInsiders", "relaunchDetailStable", "reload", "selectSyncService.message", "selectSyncService.detail", "use insiders", "use stable"]], ["vs/workbench/contrib/url/browser/trustedDomains", ["trustedDomain.trustDomain", "trustedDomain.trustAllPorts", "trustedDomain.trustSubDomain", "trustedDomain.trustAllDomains", "trustedDomain.manageTrustedDomains", "trustedDomain.manageTrustedDomain"]], ["vs/workbench/contrib/url/browser/trustedDomainsValidator", ["openExternalLinkAt", "open", "copy", "configureTrustedDomains"]], ["vs/workbench/contrib/url/browser/url.contribution", ["urlToOpen", "workbench.trustedDomains.promptInTrustedWorkspace", "openUrl"]], ["vs/workbench/contrib/userDataProfile/browser/userDataProfile", ["userdataprofilesEditor", "profiles", "New Profile Window", "new window with profile", "pick profile", "selectProfile", "miOpenProfiles", "current", "delete specific profile", "pick profile to delete", "change profile", "newWindowWithProfile", "openShort", "open profile", "open", "switchProfile", "manage profiles", "open profiles", "export profile", "export profile in share", "save profile as", "create profile", "delete profile"]], ["vs/workbench/contrib/userDataProfile/browser/userDataProfileActions", ["create temporary profile", "cleanup profile", "reset workspaces"]], ["vs/workbench/contrib/userDataProfile/browser/userDataProfilesEditor", ["editIcon", "removeIcon", "profilesSashBorder", "profiles", "from template", "importProfile", "newProfile", "newProfile", "new from template", "importProfile", "import from url", "import from file", "import profile quick pick title", "import profile placeholder", "import profile dialog", "activeProfile", "settings", "keybindings", "snippets", "tasks", "extensions", "name", "profileName", "profileName", "name required", "profileExists", "defaultProfileName", "profileName", "icon-label", "icon", "icon-description", "defaultProfileIcon", "changeIcon", "use for curren window", "enable for current window", "use for new windows", "enable for new windows", "create from", "copy from description", "copy profile from", "empty profile", "from templates", "from existing profiles", "contents", "options", "contents", "default profile contents description", "contents source description", "copy from default", "copy info", "default info", "none info", "folders_workspaces", "hostColumnLabel", "pathColumnLabel", "trustedFolderAriaLabel", "trustedFolderWithHostAriaLabel", "trustedFoldersAndWorkspaces", "addButton", "addButton", "addFolder", "addFolderTitle", "folders_workspaces_description", "no_folder_description", "default", "default description", "current description", "default", "default description", "none", "none description", "copy from default", "copy from profile description", "copy description", "change profile", "open", "deleteTrustedUri", "localAuthority", "userDataProfiles"]], ["vs/workbench/contrib/userDataProfile/browser/userDataProfilesEditorModel", ["name required", "profileExists", "invalid configurations", "open", "open", "applyToAllProfiles", "copy from", "untitled", "active", "copyFromProfile", "export", "delete", "open new window", "new profile exists", "discard", "cancel", "create", "import in desktop", "cancel", "preview", "export", "untitled", "replace", "create", "deleteProfile", "delete", "cancel"]], ["vs/workbench/contrib/userDataSync/browser/userDataSync.contribution", ["local too many requests - reload", "local too many requests - restart", "show sync logs", "reload", "restart", "operationId", "server too many requests", "settings sync", "show sync logs"]], ["vs/workbench/contrib/userDataSync/browser/userDataSync", ["syncing", "synced with time", "conflicts detected", "replace remote", "replace local", "show conflicts", "accept failed", "accept failed", "session expired", "turn on sync", "turned off", "turn on sync", "too large", "too many profiles", "error upgrade required", "operationId", "method not found", "operationId", "show sync logs", "report issue", "error reset required", "reset", "show synced data action", "service switched to insiders", "service switched to stable", "using separate service", "service changed and turned off", "turn on sync", "operationId", "open file", "errorInvalidConfiguration", "open file", "has conflicts", "turning on syncing", "sign in to sync", "no authentication providers", "too large while starting sync", "error upgrade required while starting sync", "operationId", "error reset required while starting sync", "reset", "show synced data action", "auth failed", "turn on failed with user data sync error", "turn on failed", "sign in and turn on", "configure and turn on sync detail", "configure sync title", "configure sync placeholder", "turn off sync confirmation", "turn off sync detail", "turn off", "turn off sync everywhere", "switchSyncService.title", "switchSyncService.description", "default", "insiders", "stable", "turning on sync", "cancel turning on sync", "sign in global", "sign in accounts", "sync is on", "turn off failed", "configure", "show sync log title", "show sync log toolrip", "complete merges title", "download sync activity complete", "workbench.actions.syncData.reset", "stop sync", "configure sync", "sync now", "sync settings", "show synced data", "global activity turn on sync", "resolveConflicts_global"]], ["vs/workbench/contrib/userDataSync/browser/userDataSyncConflictsView", ["explanation", "workbench.actions.sync.openConflicts", "workbench.actions.sync.acceptRemote", "workbench.actions.sync.acceptLocal", "remoteResourceName", "localResourceName", "Theirs", "Yours"]], ["vs/workbench/contrib/userDataSync/browser/userDataSyncViews", ["workbench.actions.sync.editMachineName", "workbench.actions.sync.turnOffSyncOnMachine", "workbench.actions.sync.loadActivity", "select sync activity file", "workbench.actions.sync.resolveResourceRef", "workbench.actions.sync.compareWithLocal", "remoteToLocalDiff", "leftResourceName", "rightResourceName", "workbench.actions.sync.replaceCurrent", "confirm replace", "reset", "sideBySideLabels", "current", "current", "no machines", "current", "not found", "turn off sync on multiple machines", "turn off sync on machine", "turn off", "placeholder", "not found", "valid message", "sync logs", "last sync states", "current", "conflicts", "synced machines", "remote sync activity title", "local sync activity title", "downloaded sync activity title", "troubleshoot"]], ["vs/workbench/contrib/userDataSync/electron-sandbox/userDataSync.contribution", ["no backups", "download sync activity complete", "open", "Open Backup folder"]], ["vs/workbench/contrib/webview/browser/webview.contribution", ["cut", "copy", "paste"]], ["vs/workbench/contrib/webview/browser/webviewElement", ["fatalErrorMessage"]], ["vs/workbench/contrib/webview/electron-sandbox/webviewCommands", ["openToolsDescription", "iframeWebviewAlert", "openToolsLabel"]], ["vs/workbench/contrib/webviewPanel/browser/webviewCommands", ["editor.action.webvieweditor.showFind", "editor.action.webvieweditor.hideFind", "editor.action.webvieweditor.findNext", "editor.action.webvieweditor.findPrevious", "refreshWebviewLabel"]], ["vs/workbench/contrib/webviewPanel/browser/webviewEditor", ["context.activeWebviewId"]], ["vs/workbench/contrib/webviewPanel/browser/webviewPanel.contribution", ["webview.editor.label"]], ["vs/workbench/contrib/welcomeGettingStarted/browser/gettingStarted.contribution", ["welcome", "welcome.markStepComplete", "welcome.markStepInomplete", "pickWalkthroughs", "workspacePlatform", "workbench.welcomePage.walkthroughs.openOnInstall", "workbench.startupEditor.none", "workbench.startupEditor.welcomePage", "workbench.startupEditor.readme", "workbench.startupEditor.newUntitledFile", "workbench.startupEditor.welcomePageInEmptyWorkbench", "workbench.startupEditor.terminal", "workbench.startupEditor", "deprecationMessage", "workbench.welcomePage.preferReducedMotion", "miWelcome", "minWelcomeDescription", "welcome", "welcome.goBack", "welcome.showAllWalkthroughs", "welcome.showNewWelcome"]], ["vs/workbench/contrib/welcomeGettingStarted/browser/gettingStarted", ["welcomeAriaLabel", "step<PERSON><PERSON>", "stepNotDone", "pickWalkthroughs", "videoAltText", "acessibleViewHint", "acessibleViewHintNoKbOpen", "goBack", "checkboxTitle", "welcomePage.showOnStartup", "gettingStarted.editingEvolved", "welcomePage.openFolderWithPath", "recent", "noRecents", "openFolder", "toStart", "show more recents", "showAll", "start", "new", "newItems", "close", "closeAriaLabel", "walkthroughs", "showAll", "gettingStarted.allStepsComplete", "gettingStarted.someStepsComplete", "gettingStarted.keyboardTip", "previousStep", "back", "nextStep", "next", "step<PERSON><PERSON>", "stepNotDone", "imageShowing", "videoShowing", "allDone", "nextOne", "privacy statement", "optOut", "footer", "welcome", "goBack"]], ["vs/workbench/contrib/welcomeGettingStarted/browser/gettingStartedAccessibleView", ["gettingStarted.step", "gettingStarted.title", "gettingStarted.description"]], ["vs/workbench/contrib/welcomeGettingStarted/browser/gettingStartedColors", ["welcomePage.background", "welcomePage.tileBackground", "welcomePage.tileHoverBackground", "welcomePage.tileBorder", "welcomePage.progress.background", "welcomePage.progress.foreground", "walkthrough.stepTitle.foreground"]], ["vs/workbench/contrib/welcomeGettingStarted/browser/gettingStartedExtensionPoint", ["title", "walkthroughs", "walkthroughs.id", "walkthroughs.title", "walkthroughs.icon", "walkthroughs.description", "walkthroughs.featuredFor", "walkthroughs.when", "walkthroughs.steps", "walkthroughs.steps.id", "walkthroughs.steps.title", "walkthroughs.steps.description.interpolated", "walkthroughs.steps.button.deprecated.interpolated", "walkthroughs.steps.media", "pathDeprecated", "walkthroughs.steps.media.image.path.string", "walkthroughs.steps.media.image.path.dark.string", "walkthroughs.steps.media.image.path.light.string", "walkthroughs.steps.media.image.path.hc.string", "walkthroughs.steps.media.image.path.hcLight.string", "walkthroughs.steps.media.altText", "walkthroughs.steps.media.image.path.svg", "walkthroughs.steps.media.altText", "pathDeprecated", "walkthroughs.steps.media.markdown.path", "walkthroughs.steps.completionEvents", "walkthroughs.steps.completionEvents.onCommand", "walkthroughs.steps.completionEvents.onLink", "walkthroughs.steps.completionEvents.onView", "walkthroughs.steps.completionEvents.onSettingChanged", "walkthroughs.steps.completionEvents.onContext", "walkthroughs.steps.completionEvents.extensionInstalled", "walkthroughs.steps.completionEvents.stepSelected", "walkthroughs.steps.doneOn", "walkthroughs.steps.doneOn.deprecation", "walkthroughs.steps.oneOn.command", "walkthroughs.steps.when"]], ["vs/workbench/contrib/welcomeGettingStarted/browser/gettingStartedIcons", ["gettingStarted<PERSON><PERSON><PERSON>ed", "gettingStartedChecked"]], ["vs/workbench/contrib/welcomeGettingStarted/browser/gettingStartedInput", ["walkthroughPageTitle", "getStarted"]], ["vs/workbench/contrib/welcomeGettingStarted/browser/gettingStartedService", ["builtin", "developer", "resetWelcomePageWalkthroughProgress", "resetGettingStartedProgressDescription"]], ["vs/workbench/contrib/welcomeGettingStarted/browser/startupPage", ["welcome.displayName", "startupPage.markdownPreviewError"]], ["vs/workbench/contrib/welcomeGettingStarted/common/gettingStartedContent", ["getting-started-setup-icon", "getting-started-beginner-icon", "gettingStarted.newFile.title", "gettingStarted.newFile.description", "gettingStarted.openMac.title", "gettingStarted.openMac.description", "gettingStarted.openFile.title", "gettingStarted.openFile.description", "gettingStarted.openFolder.title", "gettingStarted.openFolder.description", "gettingStarted.openFolder.title", "gettingStarted.openFolder.description", "gettingStarted.topLevelGitClone.title", "gettingStarted.topLevelGitClone.description", "gettingStarted.topLevelGitOpen.title", "gettingStarted.topLevelGitOpen.description", "gettingStarted.topLevelRemoteOpen.title", "gettingStarted.topLevelRemoteOpen.description", "gettingStarted.topLevelOpenTunnel.title", "gettingStarted.topLevelOpenTunnel.description", "gettingStarted.newWorkspaceChat.title", "gettingStarted.newWorkspaceChat.description", "gettingStarted.copilotSetup.title", "gettingStarted.copilotSetup.description", "setupCopilotButton.signIn", "setupCopilotButton.setup", "setupCopilotButton.chatWithCopilot", "gettingStarted.setup.title", "gettingStarted.setup.description", "gettingStarted.setup.walkthroughPageTitle", "gettingStarted.pickColor.title", "gettingStarted.pickColor.description.interpolated", "titleID", "gettingStarted.extensions.title", "gettingStarted.extensionsWeb.description.interpolated", "browsePopularWeb", "gettingStarted.findLanguageExts.title", "gettingStarted.findLanguageExts.description.interpolated", "browseLangExts", "gettingStarted.settings.title", "gettingStarted.settingsAndSync.description.interpolated", "tweakSettings", "gettingStarted.commandPalette.title", "gettingStarted.commandPalette.description.interpolated", "commandPalette", "gettingStarted.quickOpen.title", "gettingStarted.quickOpen.description.interpolated", "quickOpen", "gettingStarted.videoTutorial.title", "gettingStarted.videoTutorial.description.interpolated", "watch", "gettingStarted.setupWeb.title", "gettingStarted.setupWeb.description", "gettingStarted.setupWeb.walkthroughPageTitle", "gettingStarted.pickColor.title", "gettingStarted.pickColor.description.interpolated", "titleID", "gettingStarted.menuBar.title", "gettingStarted.menuBar.description.interpolated", "toggleMenuBar", "gettingStarted.extensions.title", "gettingStarted.extensionsWeb.description.interpolated", "browsePopularWeb", "gettingStarted.findLanguageExts.title", "gettingStarted.findLanguageExts.description.interpolated", "browseLangExts", "gettingStarted.settingsSync.title", "gettingStarted.settingsSync.description.interpolated", "enableSync", "gettingStarted.commandPalette.title", "gettingStarted.commandPalette.description.interpolated", "commandPalette", "gettingStarted.setup.OpenFolder.title", "gettingStarted.setup.OpenFolderWeb.description.interpolated", "openFolder", "openRepository", "gettingStarted.quickOpen.title", "gettingStarted.quickOpen.description.interpolated", "quickOpen", "gettingStarted.setupAccessibility.title", "gettingStarted.setupAccessibility.description", "gettingStarted.setupAccessibility.walkthroughPageTitle", "gettingStarted.accessibilityHelp.title", "gettingStarted.accessibilityHelp.description.interpolated", "openAccessibilityHelp", "gettingStarted.accessibleView.title", "gettingStarted.accessibleView.description.interpolated", "openAccessibleView", "gettingStarted.verbositySettings.title", "gettingStarted.verbositySettings.description.interpolated", "openVerbositySettings", "gettingStarted.commandPaletteAccessibility.title", "gettingStarted.commandPaletteAccessibility.description.interpolated", "commandPalette", "gettingStarted.keyboardShortcuts.title", "gettingStarted.keyboardShortcuts.description.interpolated", "keyboardShortcuts", "gettingStarted.accessibilitySignals.title", "gettingStarted.accessibilitySignals.description.interpolated", "listSignalSounds", "listSignalAnnouncements", "gettingStarted.hover.title", "gettingStarted.hover.description.interpolated", "showOrFocusHover", "gettingStarted.goToSymbol.title", "gettingStarted.goToSymbol.description.interpolated", "openGoToSymbol", "gettingStarted.codeFolding.title", "gettingStarted.codeFolding.description.interpolated", "toggleFold", "toggleFoldRecursively", "gettingStarted.intellisense.title", "gettingStarted.intellisense.description.interpolated", "triggerIntellisense", "triggerInlineSuggestion", "gettingStarted.accessibilitySettings.title", "gettingStarted.accessibilitySettings.description.interpolated", "openAccessibilitySettings", "gettingStarted.beginner.title", "gettingStarted.beginner.description", "gettingStarted.beginner.walkthroughPageTitle", "gettingStarted.extensions.title", "gettingStarted.extensions.description.interpolated", "browsePopular", "gettingStarted.terminal.title", "gettingStarted.terminal.description.interpolated", "showTerminal", "gettingStarted.debug.title", "gettingStarted.debug.description.interpolated", "runProject", "gettingStarted.scm.title", "gettingStarted.scmClone.description.interpolated", "cloneRepo", "gettingStarted.scm.title", "gettingStarted.scmSetup.description.interpolated", "initRepo", "gettingStarted.scm.title", "gettingStarted.scm.description.interpolated", "openSCM", "gettingStarted.installGit.title", "gettingStarted.installGit.description.interpolated", "installGit", "gettingStarted.tasks.title", "gettingStarted.tasks.description.interpolated", "runTasks", "gettingStarted.shortcuts.title", "gettingStarted.shortcuts.description.interpolated", "keyboardShortcuts", "gettingStarted.workspaceTrust.title", "gettingStarted.workspaceTrust.description.interpolated", "workspaceTrust", "enableTrust", "gettingStarted.notebook.title", "gettingStarted.notebook.walkthroughPageTitle", "gettingStarted.notebookProfile.title", "gettingStarted.notebookProfile.description", "gettingStarted.new.title", "gettingStarted.new.description", "gettingStarted.new.walkthroughPageTitle", "gettingStarted.agentMode.title", "gettingStarted.agentMode.description", "gettingStarted.nes.title", "gettingStarted.nes.description", "gettingStarted.customize.title", "gettingStarted.customize.description", "signUp", "newgettingStarted.commandPalette.title", "gettingStarted.commandPalette.description.interpolated", "commandPalette", "gettingStarted.pickColor.title", "gettingStarted.pickColor.description.interpolated", "titleID", "newgettingStarted.findLanguageExts.title", "newgettingStarted.findLanguageExts.description.interpolated", "browseLangExts", "newgettingStarted.settings.title", "newgettingStarted.settingsAndSync.description.interpolated", "tweakSettings"]], ["vs/workbench/contrib/welcomeGettingStarted/common/media/notebookProfile", ["default", "jup<PERSON><PERSON>", "colab"]], ["vs/workbench/contrib/welcomeGettingStarted/common/media/theme_picker_small", ["dark", "light", "HighContrast", "HighContrastLight"]], ["vs/workbench/contrib/welcomeGettingStarted/common/media/theme_picker", ["dark", "light", "HighContrast", "HighContrastLight", "seeM<PERSON>"]], ["vs/workbench/contrib/welcomeViews/common/newFile.contribution", ["Built-In", "newFileTitle", "newFilePlaceholder", "file", "notebook", "change keybinding", "miNewFileWithName", "miNewFile2", "Create", "welcome.new<PERSON>ile"]], ["vs/workbench/contrib/welcomeViews/common/viewsWelcomeContribution", ["ViewsWelcomeExtensionPoint.proposedAPI"]], ["vs/workbench/contrib/welcomeViews/common/viewsWelcomeExtensionPoint", ["contributes.viewsWelcome", "contributes.viewsWelcome.view", "contributes.viewsWelcome.view.view", "contributes.viewsWelcome.view.view", "contributes.viewsWelcome.view.contents", "contributes.viewsWelcome.view.when", "contributes.viewsWelcome.view.group", "contributes.viewsWelcome.view.enablement"]], ["vs/workbench/contrib/welcomeWalkthrough/browser/editor/editorWalkThrough", ["editorWalkThrough.title", "editor<PERSON><PERSON><PERSON><PERSON><PERSON>", "editorWalkThroughMetadata"]], ["vs/workbench/contrib/welcomeWalkthrough/browser/walkThrough.contribution", ["walkThrough.editor.label", "miPlayground"]], ["vs/workbench/contrib/welcomeWalkthrough/browser/walkThroughPart", ["walkThrough.unboundCommand", "walkThrough.gitNotFound"]], ["vs/workbench/contrib/welcomeWalkthrough/common/walkThroughUtils", ["walkThrough.embeddedEditorBackground"]], ["vs/workbench/contrib/workspace/browser/workspace.contribution", ["openLooseFileWorkspaceDetails", "openLooseFileWindowDetails", "openLooseFileLearnMore", "openLooseFileWorkspaceMesssage", "openLooseFileWindowMesssage", "open", "newWindow", "openLooseFileWorkspaceCheckbox", "workspaceTrust", "folderTrust", "immediateTrustRequestMessage", "grantWorkspaceTrustButton", "grantFolderTrustButton", "manageWorkspaceTrustButton", "cancelWorkspaceTrustButton", "immediateTrustRequestLearnMore", "addWorkspaceFolderMessage", "addWorkspaceFolderDetail", "no", "workspaceTrust", "folderTrust", "checkboxString", "trustOption", "trustFolderOptionDescription", "trustWorkspaceOptionDescription", "dontTrustOption", "dontTrustFolderOptionDescription", "dontTrustWorkspaceOptionDescription", "workspaceStartupTrustDetails", "folderStartupTrustDetails", "startupTrustRequestLearnMore", "restrictedModeBannerManage", "restrictedModeBannerLearnMore", "restrictedModeBannerAriaLabelWindow", "restrictedModeBannerAriaLabelFolder", "restrictedModeBannerAriaLabelWorkspace", "restrictedModeBannerMessageWindow", "restrictedModeBannerMessageFolder", "restrictedModeBannerMessageWorkspace", "status.ariaUntrustedWindow", "status.tooltipUntrustedWindow2", "status.ariaUntrustedFolder", "status.tooltipUntrustedFolder2", "status.ariaUntrustedWorkspace", "status.tooltipUntrustedWorkspace2", "status.WorkspaceTrust", "untrusted", "workspaceTrustEditor", "workspace.trust.description", "workspace.trust.startupPrompt.description", "workspace.trust.startupPrompt.always", "workspace.trust.startupPrompt.once", "workspace.trust.startupPrompt.never", "workspace.trust.banner.description", "workspace.trust.banner.always", "workspace.trust.banner.untilDismissed", "workspace.trust.banner.never", "workspace.trust.untrustedFiles.description", "workspace.trust.untrustedFiles.prompt", "workspace.trust.untrustedFiles.open", "workspace.trust.untrustedFiles.newWindow", "workspace.trust.emptyWindow.description", "workspacesCategory", "configureWorkspaceTrustSettings", "manageWorkspaceTrust"]], ["vs/workbench/contrib/workspace/browser/workspaceTrustEditor", ["shieldIcon", "checkListIcon", "xListIcon", "folderPickerIcon", "editIcon", "removeIcon", "hostColumnLabel", "pathColumnLabel", "trustedFolderAriaLabel", "trustedFolderWithHostAriaLabel", "trustedFoldersAndWorkspaces", "addButton", "addButton", "trustUri", "selectTrustedUri", "trustedFoldersDescription", "noTrustedFoldersDescriptions", "trustAll", "trustOrg", "invalidTrust", "trustUri", "selectTrustedUri", "editTrusted<PERSON>ri", "pickerTrusted<PERSON><PERSON>", "deleteTrustedUri", "localAuthority", "trustedUnsettableWindow", "trustedHeaderWindow", "trustedHeaderFolder", "trustedHeaderWorkspace", "untrustedHeader", "trustedWindow", "untrustedWorkspace", "trustedWindowSubtitle", "untrustedWindowSubtitle", "trustedFolder", "untrustedWorkspace", "trustedFolderSubtitle", "untrustedFolderSubtitle", "trustedWorkspace", "untrustedWorkspace", "trustedWorkspaceSubtitle", "untrustedWorkspaceSubtitle", "trustedDescription", "untrustedDescription", "workspaceTrustEditorHeaderActions", "root element label", "trustedFoldersAndWorkspaces", "trustedTasks", "trustedDebugging", "trustedExtensions", "trustedTasks", "trustedDebugging", "trustedSettings", "trustedExtensions", "untrustedTasks", "untrustedDebugging", "untrustedExtensions", "untrustedTasks", "untrustedDebugging", "untrustedSettings", "no untrustedSettings", "untrustedExtensions", "keyboardShortcut", "trustButton", "trustMessage", "trustParentButton", "dontTrustButton", "untrustedWorkspaceReason", "untrustedFolderReason", "trustedForcedReason"]], ["vs/workbench/contrib/workspace/common/workspace", ["workspaceTrustEnabledCtx", "workspaceTrustedCtx"]], ["vs/workbench/contrib/workspaces/browser/workspaces.contribution", ["foundWorkspace", "openWorkspace", "foundWorkspaces", "selectWorkspace", "selectToOpen", "alreadyOpen", "openWorkspace"]], ["vs/workbench/electron-sandbox/actions/developerActions", ["toggleDevTools", "configureRuntimeArguments", "reloadWindowWithExtensionsDisabled", "revealUserDataFolder", "showGPUInfo"]], ["vs/workbench/electron-sandbox/actions/installActions", ["successIn", "successFrom", "shellCommand", "install", "uninstall"]], ["vs/workbench/electron-sandbox/actions/windowActions", ["miCloseWindow", "miZoomIn", "miZoomOut", "miZoomReset", "close", "close", "windowGroup", "windowDirtyAriaLabel", "current", "current", "switchWindowPlaceHolder", "enableWindowAlwaysOnTop", "disableWindowAlwaysOnTop", "closeWindow", "zoomIn", "zoomOut", "zoomReset", "switchWindow", "quickSwitchWindow", "toggleWindowAlwaysOnTop"]], ["vs/workbench/electron-sandbox/desktop.contribution", ["miExit", "application.shellEnvironmentResolutionTimeout", "windowConfigurationTitle", "confirmSaveUntitledWorkspace", "window.openWithoutArgumentsInNewWindow.on", "window.openWithoutArgumentsInNewWindow.off", "openWithoutArgumentsInNewWindow", "window.reopenFolders.preserve", "window.reopenFolders.all", "window.reopenFolders.folders", "window.reopenFolders.one", "window.reopenFolders.none", "restoreWindows", "restoreFullscreen", "zoomLevel", "zoomPerWindow", "window.newWindowDimensions.default", "window.newWindowDimensions.inherit", "window.newWindowDimensions.offset", "window.newWindowDimensions.maximized", "window.newWindowDimensions.fullscreen", "newWindowDimensions", "closeWhenEmpty", "window.doubleClickIconToClose", "titleBarStyle", "controlsStyle", "window.customTitleBarVisibility.auto", "window.customTitleBarVisibility.windowed", "window.customTitleBarVisibility.never", "window.customTitleBarVisibility", "dialogStyle", "window.nativeTabs", "window.nativeFullScreen", "window.clickThroughInactive", "telemetryConfigurationTitle", "telemetry.enableCrashReporting", "enableCrashReporterDeprecated", "keyboardConfigurationTitle", "touchbar.enabled", "touchbar.ignored", "security.promptForLocalFileProtocolHandling", "security.promptForRemoteFileProtocolHandling", "argv.locale", "argv.disableLcdText", "argv.proxyBypassList", "argv.disableHardwareAcceleration", "argv.forceColorProfile", "argv.enableCrashReporter", "argv.crashReporterId", "argv.enebleProposedApi", "argv.logLevel", "argv.disableChromiumSandbox", "argv.useInMemorySecretStorage", "argv.force-renderer-accessibility", "argv.passwordStore", "newTab", "showPreviousTab", "showNextWindowTab", "moveWindowTabToNewWindow", "mergeAllWindowTabs", "toggleWindowTabsBar"]], ["vs/workbench/electron-sandbox/desktop.main", ["join.closeStorage"]], ["vs/workbench/electron-sandbox/parts/dialogs/dialogHandler", ["aboutDetail", "copy", "okButton"]], ["vs/workbench/electron-sandbox/window", ["sharedProcessCrash", "restart", "restart", "configure", "learnMore", "keychainWriteError", "troubleshooting", "runningTranslated", "downloadArmBuild", "showArgvParseWarning", "showArgvParseWarningAction", "proxyAuthRequired", "loginButton", "username", "password", "proxyDetail", "rememberCredentials", "shutdownErrorDetail", "willShutdownDetail", "shutdownErrorClose", "shutdownErrorQuit", "shutdownErrorReload", "shutdownErrorLoad", "shutdownTitleClose", "shutdownTitleQuit", "shutdownTitleReload", "shutdownTitleLoad", "shutdownForceClose", "shutdownForceQuit", "shutdownForceReload", "shutdownForceLoad", "runningAsRoot", "appRootWarning.banner", "resolveShellEnvironment", "learnMore", "zoomOut", "zoomIn", "zoomReset", "zoomResetLabel", "zoomSettings", "status.windowZoom", "zoomNumber"]], ["vs/workbench/services/accounts/common/defaultAccount", ["sign in"]], ["vs/workbench/services/actions/common/menusExtensionPoint", ["menus.commandPalette", "menus.touchBar", "menus.editorTitle", "menus.editorTitleRun", "menus.editorContext", "menus.editorContextCopyAs", "menus.editorContextShare", "menus.explorerContext", "menus.explorerContextShare", "menus.editorTabContext", "menus.editorTitleContextShare", "menus.debugCallstackContext", "menus.debugVariablesContext", "menus.debugToolBar", "menus.debugCreateConfiguation", "menus.notebookVariablesContext", "menus.home", "menus.opy", "menus.scmTitle", "menus.scmSourceControl", "menus.scmSourceControlTitle", "menus.resourceStateContext", "menus.resourceFolderContext", "menus.resourceGroupContext", "menus.changeTitle", "menus.input", "menus.scmHistoryTitle", "menus.historyItemContext", "menus.historyItemHover", "menus.historyItemRefContext", "menus.statusBarRemoteIndicator", "menus.terminalContext", "menus.terminalTabContext", "view.viewTitle", "view.containerTitle", "view.itemContext", "commentThread.editorActions", "commentThread.title", "commentThread.actions", "commentThread.actions", "commentThread.titleContext", "comment.title", "comment.actions", "comment.commentContext", "commentsView.threadActions", "notebook.toolbar", "notebook.kernelSource", "notebook.cell.title", "notebook.cell.execute", "interactive.toolbar", "interactive.cell.title", "issue.reporter", "testing.item.context", "testing.item.gutter.title", "testing.profiles.context.title", "testing.item.result.title", "testing.message.context.title", "testing.message.content.title", "menus.extensionContext", "view.timelineTitle", "view.timelineContext", "view.tunnelContext", "view.tunnelOriginInline", "view.tunnelPortInline", "file.newFile", "webview.context", "menus.share", "inlineCompletions.actions", "merge.toolbar", "editorLineNumberContext", "menus.mergeEditorResult", "menus.multiDiffEditorResource", "menus.diffEditorGutterToolBarMenus", "menus.diffEditorGutterToolBarMenus", "searchPanel.aiResultsCommands", "menus.chatModelPicker", "requirestring", "optstring", "optstring", "optstring", "requirestring", "optstring", "optstring", "requirearray", "require", "requirestring", "requirestring", "vscode.extension.contributes.menuItem.command", "vscode.extension.contributes.menuItem.alt", "vscode.extension.contributes.menuItem.when", "vscode.extension.contributes.menuItem.group", "vscode.extension.contributes.menuItem.submenu", "vscode.extension.contributes.menuItem.when", "vscode.extension.contributes.menuItem.group", "vscode.extension.contributes.submenu.id", "vscode.extension.contributes.submenu.label", "vscode.extension.contributes.submenu.icon", "vscode.extension.contributes.submenu.icon.light", "vscode.extension.contributes.submenu.icon.dark", "vscode.extension.contributes.menus", "proposed", "vscode.extension.contributes.submenus", "nonempty", "requirestring", "optstring", "opticon", "requireStringOrObject", "requirestring", "requirestrings", "vscode.extension.contributes.commandType.command", "vscode.extension.contributes.commandType.title", "vscode.extension.contributes.commandType.shortTitle", "vscode.extension.contributes.commandType.category", "vscode.extension.contributes.commandType.precondition", "vscode.extension.contributes.commandType.icon", "vscode.extension.contributes.commandType.icon.light", "vscode.extension.contributes.commandType.icon.dark", "vscode.extension.contributes.commands", "dup1", "dup0", "submenuId.invalid.id", "submenuId.duplicate.id", "submenuId.invalid.label", "proposedAPI.invalid", "missing.command", "missing.altCommand", "dupe.command", "unsupported.submenureference", "missing.submenu", "submenuItem.duplicate", "viewContainerTitle.when", "command name", "command title", "keyboard shortcuts", "menuContexts", "commands"]], ["vs/workbench/services/assignment/common/assignmentService", ["workbench.enableExperiments"]], ["vs/workbench/services/authentication/browser/authenticationExtensionsService", ["sign in", "confirmAuthenticationAccess", "allow", "deny", "useOtherAccount", "selectAccount", "getSessionPlateholder", "accessRequest", "signInRequest"]], ["vs/workbench/services/authentication/browser/authenticationService", ["authentication.id", "authentication.label", "authenticationExtensionPoint", "authentication.missingId", "authentication.missingLabel", "authentication.idConflict", "authentication.missingId", "authentication.missingLabel", "authentication.idConflict"]], ["vs/workbench/services/auxiliaryWindow/browser/auxiliaryWindowService", ["lifecycleVeto", "unableToOpenWindowError", "unableToOpenWindow", "unableToOpenWindowDetail", "retry"]], ["vs/workbench/services/auxiliaryWindow/electron-sandbox/auxiliaryWindowService", ["backupErrorDetails"]], ["vs/workbench/services/clipboard/browser/clipboardService", ["clipboardError", "retry", "learnMore"]], ["vs/workbench/services/configuration/browser/configurationService", ["configurationDefaults.description", "setting description"]], ["vs/workbench/services/configuration/common/configurationEditing", ["fsError", "openTasksConfiguration", "openLaunchConfiguration", "openMcpConfiguration", "open", "openTasksConfiguration", "openLaunchConfiguration", "saveAndRetry", "saveAndRetry", "open", "errorPolicyConfiguration", "errorUnknownKey", "errorInvalidWorkspaceConfigurationApplication", "errorInvalidWorkspaceConfigurationMachine", "errorInvalidFolderConfiguration", "errorInvalidUserTarget", "errorInvalidWorkspaceTarget", "errorInvalidFolderTarget", "errorInvalidResourceLanguageConfiguration", "errorNoWorkspaceOpened", "errorInvalidTaskConfiguration", "errorInvalidLaunchConfiguration", "errorInvalidMCPConfiguration", "errorInvalidConfiguration", "errorInvalidRemoteConfiguration", "errorInvalidConfigurationWorkspace", "errorInvalidConfigurationFolder", "errorTasksConfigurationFileDirty", "errorLaunchConfigurationFileDirty", "errorMCPConfigurationFileDirty", "errorConfigurationFileDirty", "errorRemoteConfigurationFileDirty", "errorConfigurationFileDirtyWorkspace", "errorConfigurationFileDirtyFolder", "errorTasksConfigurationFileModifiedSince", "errorLaunchConfigurationFileModifiedSince", "errorMCPConfigurationFileModifiedSince", "errorConfigurationFileModifiedSince", "errorRemoteConfigurationFileModifiedSince", "errorConfigurationFileModifiedSinceWorkspace", "errorConfigurationFileModifiedSinceFolder", "errorUnknown", "userTarget", "remoteUserTarget", "workspaceTarget", "folderTarget"]], ["vs/workbench/services/configuration/common/jsonEditingService", ["errorInvalidFile"]], ["vs/workbench/services/configurationResolver/browser/baseConfigurationResolverService", ["commandVariable.noStringType", "inputVariable.noInputSection", "inputVariable.missingAttribute", "inputVariable.defaultInputValue", "inputVariable.command.noStringType", "inputVariable.unknownType", "inputVariable.undefinedVariable"]], ["vs/workbench/services/configurationResolver/common/configurationResolverSchema", ["JsonSchema.input.id", "JsonSchema.input.type", "JsonSchema.input.description", "JsonSchema.input.default", "JsonSchema.inputs", "JsonSchema.input.type.promptString", "JsonSchema.input.password", "JsonSchema.input.type.pickString", "JsonSchema.input.options", "JsonSchema.input.pickString.optionLabel", "JsonSchema.input.pickString.optionValue", "JsonSchema.input.type.command", "JsonSchema.input.command.command", "JsonSchema.input.command.args", "JsonSchema.input.command.args", "JsonSchema.input.command.args"]], ["vs/workbench/services/configurationResolver/common/configurationResolverUtils", ["deprecatedVariables"]], ["vs/workbench/services/configurationResolver/common/variableResolver", ["canNotResolveFile", "canNotResolveFolderForFile", "canNotFindFolder", "canNotResolveWorkspaceFolderMultiRoot", "canNotResolveWorkspaceFolder", "missingEnvVarName", "configNotFound", "configNoString", "missingConfigName", "extensionNotInstalled", "missingExtensionName", "canNotResolveUserHome", "canNotResolveLineNumber", "canNotResolveColumnNumber", "canNotResolveSelectedText", "noValueForCommand"]], ["vs/workbench/services/decorations/browser/decorationsService", ["bubbleTitle"]], ["vs/workbench/services/dialogs/browser/abstractFileDialogService", ["saveChangesDetail", "saveChangesMessage", "saveChangesMessages", "saveAll", "save", "dontSave", "openFileOrFolder.title", "openFile.title", "openFolder.title", "openWorkspace.title", "filterName.workspace", "saveFileAs.title", "saveAsTitle", "allFiles", "noExt"]], ["vs/workbench/services/dialogs/browser/fileDialogService", ["pickFolderAndOpen", "pickFolderAndOpen", "pickWorkspaceAndOpen", "openRemote", "learnMore", "openFiles", "unsupportedBrowserMessage", "unsupportedBrowserDetail"]], ["vs/workbench/services/dialogs/browser/simpleFileDialog", ["openLocalFile", "saveLocalFile", "openLocalFolder", "openLocalFileFolder", "remoteFileDialog.notConnectedToRemote", "remoteFileDialog.local", "remoteFileDialog.hideDotFiles", "remoteFileDialog.showDotFiles", "remoteFileDialog.badPath", "remoteFileDialog.cancel", "remoteFileDialog.invalidPath", "remoteFileDialog.validateFolder", "remoteFileDialog.validateExisting", "remoteFileDialog.validateBadFilename", "remoteFileDialog.validateCreateDirectory", "remoteFileDialog.validateNonexistentDir", "remoteFileDialog.validateReadonlyFolder", "remoteFileDialog.validateNonexistentDir", "remoteFileDialog.windowsDriveLetter", "remoteFileDialog.validateFileOnly", "remoteFileDialog.validateFolderOnly"]], ["vs/workbench/services/editor/browser/editorResolverService", ["editorResolver.conflictingDefaults", "editorResolver.configureDefault", "editorResolver.keepDefault", "promptOpenWith.currentlyActive", "promptOpenWith.currentDefault", "promptOpenWith.currentDefaultAndActive", "promptOpenWith.configureDefault", "promptOpenWith.updateDefaultPlaceHolder", "promptOpenWith.placeHolder"]], ["vs/workbench/services/editor/common/editorResolverService", ["editor.editor<PERSON><PERSON><PERSON><PERSON>"]], ["vs/workbench/services/extensionManagement/browser/extensionBisect", ["I cannot reproduce", "This is Bad", "bisect.singular", "bisect.plural", "msg.start", "detail.start", "msg2", "done.msg", "done.detail2", "done.msg", "report", "continue", "done.detail", "done.disbale", "msg.next", "bisect", "next.good", "next.bad", "next.stop", "next.cancel", "title.start", "title.isBad", "title.stop"]], ["vs/workbench/services/extensionManagement/browser/extensionEnablementService", ["extensionsDisabled", "Reload", "cannot disable language pack extension", "cannot disable auth extension", "cannot change enablement environment", "cannot change disablement environment", "cannot change enablement malicious", "cannot change enablement virtual workspace", "cannot change enablement extension kind", "cannot change disallowed extension enablement", "cannot change invalid extension enablement", "cannot change enablement dependency", "noWorkspace", "cannot disable auth extension in workspace"]], ["vs/workbench/services/extensionManagement/browser/webExtensionsScannerService", ["not a web extension", "openInstalledWebExtensionsResource"]], ["vs/workbench/services/extensionManagement/common/extensionFeaturesManagemetService", ["accessExtensionFeature", "accessExtensionFeatureMessage", "allow", "disallow"]], ["vs/workbench/services/extensionManagement/common/extensionManagementServerService", ["remote", "browser"]], ["vs/workbench/services/extensionManagement/common/extensionManagementService", ["singleDependentError", "twoDependentsError", "multipleDependentsError", "manifest is not found", "cannot be installed", "cannot be installed", "Manifest is not found", "Manifest is not found", "Manifest is not found", "Manifest is not found", "cannot be installed in server", "cannot be installed", "install extension", "install extensions", "install single extension", "install multiple extensions", "install", "install and do no sync", "Manifest is not found", "trust publishers and install", "trust and install", "learnMore", "checkTrustedPublisherTitle", "checkTwoTrustedPublishersTitle", "checkAllTrustedPublishersTitle", "extension published by message", "singleUntrustedPublisher", "message3", "firstTimeInstallingMessage", "message1", "multiInstallMessage", "verifiedPublisherWithName", "unverifiedPublisherWithName", "unverifiedPublishers", "allUnverifed", "message4", "message2", "extensionInstallWorkspaceTrustButton", "extensionInstallWorkspaceTrustContinueButton", "extensionInstallWorkspaceTrustManageButton", "extensionInstallWorkspaceTrustMessage", "VS Code for Web", "limited support", "install anyways", "showExtensions", "non web extensions detail", "non web extensions", "main.notFound"]], ["vs/workbench/services/extensionManagement/common/extensionsIcons", ["verifiedPublisher", "extensionIconVerifiedForeground"]], ["vs/workbench/services/extensionManagement/electron-sandbox/extensionGalleryManifestService", ["extensionGalleryManifestService.accountChange", "restart"]], ["vs/workbench/services/extensionManagement/electron-sandbox/extensionManagementServerService", ["local", "remote"]], ["vs/workbench/services/extensionManagement/electron-sandbox/********************************", ["incompatibleAPI", "notFoundReleaseExtension", "notFoundCompatibleDependency"]], ["vs/workbench/services/extensionRecommendations/common/workspaceExtensionsConfig", ["select for remove", "select for add", "select for remove", "select for add", "workspace folder", "workspace"]], ["vs/workbench/services/extensions/browser/extensionUrlHandler", ["confirmUrl", "rememberConfirmUrl", "open", "installDetail", "openUri", "reloadAndHandle", "reloadAndOpen", "no", "manage", "extensions"]], ["vs/workbench/services/extensions/common/abstractExtensionService", ["disconnectRemote", "stopExtensionHosts", "looping", "looping", "extensionTestError", "extensionStopVetoError", "extensionStopVetoMessage", "proceedAnyways", "extensionService.autoRestart", "extensionService.crash", "restart", "activation"]], ["vs/workbench/services/extensions/common/extensionHostManager", ["measureExtHostLatency"]], ["vs/workbench/services/extensions/common/extensionsProposedApi", ["enabledProposedAPIs"]], ["vs/workbench/services/extensions/common/extensionsRegistry", ["ui", "workspace", "vscode.extension.engines", "vscode.extension.engines.vscode", "vscode.extension.publisher", "vscode.extension.displayName", "vscode.extension.categories", "vscode.extension.category.languages.deprecated", "vscode.extension.galleryBanner", "vscode.extension.galleryBanner.color", "vscode.extension.galleryBanner.theme", "vscode.extension.contributes", "vscode.extension.preview", "vscode.extension.enableProposedApi.deprecated", "vscode.extension.enabledApiProposals", "vscode.extension.api", "vscode.extension.api.none", "vscode.extension.activationEvents", "vscode.extension.activationEvents.onWebviewPanel", "vscode.extension.activationEvents.onLanguage", "vscode.extension.activationEvents.onCommand", "vscode.extension.activationEvents.onDebug", "vscode.extension.activationEvents.onDebugInitialConfigurations", "vscode.extension.activationEvents.onDebugDynamicConfigurations", "vscode.extension.activationEvents.onDebugResolve", "vscode.extension.activationEvents.onDebugAdapterProtocolTracker", "vscode.extension.activationEvents.workspaceContains", "vscode.extension.activationEvents.onStartupFinished", "vscode.extension.activationEvents.onTaskType", "vscode.extension.activationEvents.onFileSystem", "vscode.extension.activationEvents.onEditSession", "vscode.extension.activationEvents.onSearch", "vscode.extension.activationEvents.onView", "vscode.extension.activationEvents.onUri", "vscode.extension.activationEvents.onOpenExternalUri", "vscode.extension.activationEvents.onCustomEditor", "vscode.extension.activationEvents.onNotebook", "vscode.extension.activationEvents.onAuthenticationRequest", "vscode.extension.activationEvents.onRenderer", "vscode.extension.activationEvents.onTerminalProfile", "vscode.extension.activationEvents.onTerminalQuickFixRequest", "vscode.extension.activationEvents.onWalkthrough", "vscode.extension.activationEvents.onIssueReporterOpened", "vscode.extension.activationEvents.onChatParticipant", "vscode.extension.activationEvents.onLanguageModelTool", "vscode.extension.activationEvents.onTerminalCompletionsRequested", "vscode.extension.activationEvents.onMcpCollection", "vscode.extension.activationEvents.star", "vscode.extension.badges", "vscode.extension.badges.url", "vscode.extension.badges.href", "vscode.extension.badges.description", "vscode.extension.markdown", "vscode.extension.qna", "vscode.extension.extensionDependencies", "vscode.extension.contributes.extensionPack", "extensionKind", "extensionKind.ui", "extensionKind.workspace", "extensionKind.ui-workspace", "extensionKind.workspace-ui", "extensionKind.empty", "vscode.extension.capabilities", "vscode.extension.capabilities.virtualWorkspaces", "vscode.extension.capabilities.virtualWorkspaces.supported", "vscode.extension.capabilities.virtualWorkspaces.supported.limited", "vscode.extension.capabilities.virtualWorkspaces.supported.true", "vscode.extension.capabilities.virtualWorkspaces.supported.false", "vscode.extension.capabilities.virtualWorkspaces.description", "vscode.extension.capabilities.untrustedWorkspaces", "vscode.extension.capabilities.untrustedWorkspaces.supported", "vscode.extension.capabilities.untrustedWorkspaces.supported.limited", "vscode.extension.capabilities.untrustedWorkspaces.supported.true", "vscode.extension.capabilities.untrustedWorkspaces.supported.false", "vscode.extension.capabilities.untrustedWorkspaces.restrictedConfigurations", "vscode.extension.capabilities.untrustedWorkspaces.description", "vscode.extension.contributes.sponsor", "vscode.extension.contributes.sponsor.url", "vscode.extension.scripts.prepublish", "vscode.extension.scripts.uninstall", "vscode.extension.icon", "vscode.extension.l10n", "vscode.extension.pricing", "product.extensionEnabledApiProposals"]], ["vs/workbench/services/extensions/common/extensionsUtil", ["overwritingExtension", "overwritingExtension", "overwritingWithWorkspaceExtension", "extensionUnderDevelopment"]], ["vs/workbench/services/extensions/electron-sandbox/cachedExtensionScanner", ["extensionCache.invalid", "reloadWindow"]], ["vs/workbench/services/extensions/electron-sandbox/localProcessExtensionHost", ["extensionHost.startupFailDebug", "extensionHost.startupFail", "reloadWindow", "join.extensionDevelopment"]], ["vs/workbench/services/extensions/electron-sandbox/nativeExtensionService", ["extensionService.versionMismatchCrash", "relaunch", "extensionService.autoRestart", "startBisect", "devTools", "restart", "learnMore", "extensionService.crash", "getEnvironmentFailure", "enableResolver", "enable", "installResolver", "install", "resolverExtensionNotFound", "restartExtensionHost.reason", "restartExtensionHost"]], ["vs/workbench/services/files/electron-sandbox/diskFileSystemProvider", ["fileWatcher"]], ["vs/workbench/services/files/electron-sandbox/elevatedFileService", ["fileNotTrustedMessageWindows", "fileNotTrustedMessagePosix", "fileNotTrusted"]], ["vs/workbench/services/filesConfiguration/common/filesConfigurationService", ["providerReadonly", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "configured<PERSON><PERSON><PERSON><PERSON>", "fileLocked", "fileReadonly"]], ["vs/workbench/services/history/browser/historyService", ["canNavigateBack", "canNavigateForward", "canNavigateBackInNavigationLocations", "canNavigateForwardInNavigationLocations", "canNavigateToLastNavigationLocation", "canNavigateBackInEditLocations", "canNavigateForwardInEditLocations", "canNavigateToLastEditLocation", "canReopenClosedEditor"]], ["vs/workbench/services/host/browser/browserHostService", ["unableToOpenExternal", "open"]], ["vs/workbench/services/integrity/electron-sandbox/integrityService", ["integrity.prompt", "integrity.moreInformation", "integrity.dontShowAgain"]], ["vs/workbench/services/keybinding/browser/keybindingService", ["nonempty", "requirestring", "optstring", "optstring", "optstring", "optstring", "optstring", "vscode.extension.contributes.keybindings.command", "vscode.extension.contributes.keybindings.args", "vscode.extension.contributes.keybindings.key", "vscode.extension.contributes.keybindings.mac", "vscode.extension.contributes.keybindings.linux", "vscode.extension.contributes.keybindings.win", "vscode.extension.contributes.keybindings.when", "vscode.extension.contributes.keybindings", "invalid.keybindings", "unboundCommands", "keybindings.json.title", "keybindings.json.command", "keybindings.json.removalCommand", "keybindings.json.key", "keybindings.commandsIsArray", "keybindings.json.when", "keybindings.json.args"]], ["vs/workbench/services/keybinding/browser/keyboardLayoutService", ["keyboardConfigurationTitle", "keyboard.layout.config"]], ["vs/workbench/services/keybinding/common/keybindingEditing", ["errorKeybindingsFileDirty", "parseErrors", "errorInvalidConfiguration", "emptyKeybindingsHeader"]], ["vs/workbench/services/label/common/labelService", ["vscode.extension.contributes.resourceLabelFormatters", "vscode.extension.contributes.resourceLabelFormatters.scheme", "vscode.extension.contributes.resourceLabelFormatters.authority", "vscode.extension.contributes.resourceLabelFormatters.formatting", "vscode.extension.contributes.resourceLabelFormatters.label", "vscode.extension.contributes.resourceLabelFormatters.separator", "vscode.extension.contributes.resourceLabelFormatters.stripPathStartingSeparator", "vscode.extension.contributes.resourceLabelFormatters.tildify", "vscode.extension.contributes.resourceLabelFormatters.formatting.workspaceSuffix", "untitledWorkspace", "temporaryWorkspace", "workspaceNameVerbose", "workspaceName"]], ["vs/workbench/services/language/common/languageService", ["vscode.extension.contributes.languages", "vscode.extension.contributes.languages.id", "vscode.extension.contributes.languages.aliases", "vscode.extension.contributes.languages.extensions", "vscode.extension.contributes.languages.filenames", "vscode.extension.contributes.languages.filenamePatterns", "vscode.extension.contributes.languages.mimetypes", "vscode.extension.contributes.languages.firstLine", "vscode.extension.contributes.languages.configuration", "vscode.extension.contributes.languages.icon", "vscode.extension.contributes.languages.icon.light", "vscode.extension.contributes.languages.icon.dark", "language id", "language name", "file extensions", "grammar", "snippets", "languages", "invalid", "invalid.empty", "require.id", "opt.extensions", "opt.filenames", "opt.firstLine", "opt.configuration", "opt.aliases", "opt.mimetypes", "opt.icon"]], ["vs/workbench/services/lifecycle/browser/lifecycleService", ["lifecycleVeto"]], ["vs/workbench/services/localization/browser/localeService", ["relaunchDisplayLanguageMessage", "relaunchDisplayLanguageDetail", "reload", "clearDisplayLanguageMessage", "clearDisplayLanguageDetail", "reload"]], ["vs/workbench/services/localization/electron-sandbox/localeService", ["argvInvalid", "openArgv", "installing", "restartDisplayLanguageMessage1", "restartDisplayLanguageDetail1", "restart"]], ["vs/workbench/services/log/common/logConstants", ["window"]], ["vs/workbench/services/notification/common/notificationService", ["neverShowAgain", "neverShowAgain"]], ["vs/workbench/services/preferences/browser/keybindingsEditorInput", ["keybindingsEditorLabelIcon", "keybindingsInputName"]], ["vs/workbench/services/preferences/browser/keybindingsEditorModel", ["default", "extension", "user", "cat.title", "cat.title", "option", "meta"]], ["vs/workbench/services/preferences/browser/preferencesService", ["openFolderFirst", "emptyKeybindingsHeader", "defaultKeybindings", "defaultKeybindings", "fail.createSettings"]], ["vs/workbench/services/preferences/common/preferencesEditorInput", ["settingsEditorLabelIcon", "settingsEditor2InputName"]], ["vs/workbench/services/preferences/common/preferencesModels", ["commonlyUsed", "defaultKeybindingsHeader"]], ["vs/workbench/services/preferences/common/preferencesValidation", ["validations.booleanIncorrectType", "validations.expectedNumeric", "validations.stringIncorrectEnumOptions", "validations.stringIncorrectType", "invalidTypeError", "regexParsingError", "validations.maxLength", "validations.minLength", "validations.regex", "validations.colorFormat", "validations.uriEmpty", "validations.uriMissing", "validations.uriSchemeMissing", "validations.invalidStringEnumValue", "validations.exclusiveMax", "validations.exclusiveMin", "validations.max", "validations.min", "validations.multipleOf", "validations.expectedInteger", "validations.arrayIncorrectType", "validations.stringArrayUniqueItems", "validations.stringArrayMinItem", "validations.stringArrayMaxItem", "validations.stringArrayIncorrectType", "validations.stringArrayItemPattern", "validations.stringArrayItemEnum", "validations.objectIncorrectType", "validations.objectPattern"]], ["vs/workbench/services/progress/browser/progressService", ["progress.text2", "progress.title3", "progress.title2", "progress.title2", "status.progress", "cancel", "cancel", "dismiss"]], ["vs/workbench/services/remote/browser/remoteAgentService", ["connectionError", "connectionErrorDetail", "reload"]], ["vs/workbench/services/remote/common/remoteExplorerService", ["getStartedWalkthrough.id", "RemoteHelpInformationExtPoint", "RemoteHelpInformationExtPoint.getStarted", "RemoteHelpInformationExtPoint.documentation", "RemoteHelpInformationExtPoint.feedback", "RemoteHelpInformationExtPoint.feedback.deprecated", "RemoteHelpInformationExtPoint.reportIssue", "RemoteHelpInformationExtPoint.issues"]], ["vs/workbench/services/remote/common/tunnelModel", ["tunnel.forwardedPortsViewEnabled", "tunnel.forwardedPortsViewEnabled", "tunnel.source.user", "tunnel.source.auto", "remote.localPortMismatch.single", "tunnel.staticallyForwarded"]], ["vs/workbench/services/remote/electron-sandbox/remoteAgentService", ["devTools", "directUrl", "connectionError"]], ["vs/workbench/services/request/browser/requestService", ["network"]], ["vs/workbench/services/request/electron-sandbox/requestService", ["network"]], ["vs/workbench/services/search/browser/searchService", ["errorSearchText", "errorSearchFile"]], ["vs/workbench/services/search/common/queryBuilder", ["search.noWorkspaceWithName"]], ["vs/workbench/services/secrets/electron-sandbox/secretStorageService", ["troubleshootingButton", "encryptionNotAvailableJustTroubleshootingGuide", "usePlainTextExtraSentence", "usePlainText", "isGnome", "isKwallet"]], ["vs/workbench/services/suggest/browser/simpleSuggestWidget", ["simpleSuggestWidgetHasFocusedSuggestion", "simpleSuggestWidgetHasNavigated", "suggestWidget.loading", "suggestWidget.noSuggestions", "suggest", "label.full", "label.detail", "label.desc", "label", "ariaCurrenttSuggestionReadDetails"]], ["vs/workbench/services/suggest/browser/simpleSuggestWidgetDetails", ["details.close", "loading"]], ["vs/workbench/services/textfile/browser/textFileService", ["textFileCreate.source", "textFileOverwrite.source", "textFileModelDecorations", "readonlyAndDeleted", "readonly", "deleted", "fileBinaryError", "confirmOverwrite", "overwriteIrreversible", "replaceButtonLabel", "confirmMakeWriteable", "confirmMakeWriteableDetail", "makeWriteableButtonLabel"]], ["vs/workbench/services/textfile/common/textFileEditorModel", ["textFileCreate.source", "saveParticipants", "saveTextFile"]], ["vs/workbench/services/textfile/common/textFileEditorModelManager", ["genericSaveError"]], ["vs/workbench/services/textfile/common/textFileSaveParticipant", ["saveParticipants1", "skip"]], ["vs/workbench/services/textfile/electron-sandbox/nativeTextFileService", ["join.textFiles"]], ["vs/workbench/services/textMate/browser/textMateTokenizationFeatureImpl", ["alreadyDebugging", "stop", "progress1", "progress2", "invalid.language", "invalid.scopeName", "invalid.path.0", "invalid.injectTo", "invalid.embeddedLanguages", "invalid.tokenTypes", "invalid.path.1"]], ["vs/workbench/services/textMate/common/TMGrammars", ["vscode.extension.contributes.grammars", "vscode.extension.contributes.grammars.language", "vscode.extension.contributes.grammars.scopeName", "vscode.extension.contributes.grammars.path", "vscode.extension.contributes.grammars.embeddedLanguages", "vscode.extension.contributes.grammars.tokenTypes", "vscode.extension.contributes.grammars.injectTo", "vscode.extension.contributes.grammars.balancedBracketScopes", "vscode.extension.contributes.grammars.unbalancedBracketScopes"]], ["vs/workbench/services/themes/browser/fileIconThemeData", ["error.cannotparseicontheme", "error.invalidformat"]], ["vs/workbench/services/themes/browser/productIconThemeData", ["error.parseicondefs", "defaultTheme", "error.cannotparseicontheme", "error.invalidformat", "error.missingProperties", "error.fontWeight", "error.fontStyle", "error.fontSrc", "error.noFontSrc", "error.fontId", "error.icon.font", "error.icon.fontCharacter"]], ["vs/workbench/services/themes/browser/workbenchThemeService", ["error.cannotloadtheme"]], ["vs/workbench/services/themes/common/colorExtensionPoint", ["contributes.color", "contributes.color.id", "contributes.color.id.format", "contributes.color.description", "contributes.defaults.light", "contributes.defaults.dark", "contributes.defaults.highContrast", "contributes.defaults.highContrastLight", "invalid.colorConfiguration", "invalid.default.colorType", "invalid.id", "invalid.id.format", "invalid.description", "invalid.defaults", "invalid.defaults.highContrast", "invalid.defaults.highContrastLight", "id", "description", "defaultDark", "defaultLight", "defaultHC", "colors"]], ["vs/workbench/services/themes/common/colorThemeData", ["error.<PERSON><PERSON><PERSON><PERSON><PERSON>", "error.invalidformat", "error.invalidformat.colors", "error.invalidformat.tokenColors", "error.invalidformat.semanticTokenColors", "error.plist.invalidformat", "error.cannotparse", "error.cannotload"]], ["vs/workbench/services/themes/common/colorThemeSchema", ["schema.token.settings", "schema.token.foreground", "schema.token.background.warning", "schema.token.fontStyle", "schema.fontStyle.error", "schema.token.fontStyle.none", "schema.properties.name", "schema.properties.scope", "schema.workbenchColors", "schema.tokenColors.path", "schema.colors", "schema.supportsSemanticHighlighting", "schema.semanticTokenColors"]], ["vs/workbench/services/themes/common/fileIconThemeSchema", ["schema.folderExpanded", "schema.folder", "schema.file", "schema.rootFolder", "schema.rootFolderExpanded", "schema.rootFolderNames", "schema.folderName", "schema.rootFolderNamesExpanded", "schema.rootFolderNameExpanded", "schema.folderNames", "schema.folderName", "schema.folderNamesExpanded", "schema.folderNameExpanded", "schema.fileExtensions", "schema.fileExtension", "schema.fileNames", "schema.fileName", "schema.languageIds", "schema.languageId", "schema.fonts", "schema.id", "schema.src", "schema.font-path", "schema.font-format", "schema.font-weight", "schema.font-style", "schema.font-size", "schema.iconDefinitions", "schema.iconDefinition", "schema.iconPath", "schema.fontCharacter", "schema.fontColor", "schema.fontSize", "schema.fontId", "schema.light", "schema.highContrast", "schema.hidesExplorerArrows", "schema.showLanguageModeIcons"]], ["vs/workbench/services/themes/common/iconExtensionPoint", ["contributes.icons", "contributes.icon.id", "contributes.icon.id.format", "contributes.icon.description", "contributes.icon.default.fontPath", "contributes.icon.default.fontCharacter", "contributes.icon.default", "invalid.icons.configuration", "invalid.icons.id.format", "invalid.icons.description", "invalid.icons.default.fontPath.extension", "invalid.icons.default.fontPath.path", "invalid.icons.default"]], ["vs/workbench/services/themes/common/productIconThemeSchema", ["schema.id", "schema.src", "schema.font-path", "schema.font-format", "schema.font-weight", "schema.font-style", "schema.iconDefinitions"]], ["vs/workbench/services/themes/common/themeConfiguration", ["colorTheme", "colorThemeError", "preferredDarkColorTheme", "colorThemeError", "preferredLightColorTheme", "colorThemeError", "preferredHCDarkColorTheme", "colorThemeError", "preferredHCLightColorTheme", "colorThemeError", "detectColorScheme", "workbenchColors", "iconTheme", "noIconThemeLabel", "noIconThemeDesc", "iconThemeError", "productIconTheme", "defaultProductIconThemeLabel", "defaultProductIconThemeDesc", "productIconThemeError", "autoDetectHighContrast", "editorColors.comments", "editorColors.strings", "editorColors.keywords", "editorColors.numbers", "editorColors.types", "editorColors.functions", "editorColors.variables", "editorColors.textMateRules", "editorColors.semanticHighlighting", "editorColors.semanticHighlighting.deprecationMessage", "editorColors.semanticHighlighting.deprecationMessageMarkdown", "editorColors", "editorColors.semanticHighlighting.enabled", "editorColors.semanticHighlighting.rules", "semanticTokenColors"]], ["vs/workbench/services/themes/common/themeExtensionPoints", ["vscode.extension.contributes.themes", "vscode.extension.contributes.themes.id", "vscode.extension.contributes.themes.label", "vscode.extension.contributes.themes.uiTheme", "vscode.extension.contributes.themes.path", "vscode.extension.contributes.iconThemes", "vscode.extension.contributes.iconThemes.id", "vscode.extension.contributes.iconThemes.label", "vscode.extension.contributes.iconThemes.path", "vscode.extension.contributes.productIconThemes", "vscode.extension.contributes.productIconThemes.id", "vscode.extension.contributes.productIconThemes.label", "vscode.extension.contributes.productIconThemes.path", "color themes", "file icon themes", "product icon themes", "themes", "re<PERSON><PERSON><PERSON>", "reqpath", "reqid", "invalid.path.1"]], ["vs/workbench/services/themes/common/tokenClassificationExtensionPoint", ["contributes.semanticTokenTypes", "contributes.semanticTokenTypes.id", "contributes.semanticTokenTypes.id.format", "contributes.semanticTokenTypes.superType", "contributes.semanticTokenTypes.superType.format", "contributes.color.description", "contributes.semanticTokenModifiers", "contributes.semanticTokenModifiers.id", "contributes.semanticTokenModifiers.id.format", "contributes.semanticTokenModifiers.description", "contributes.semanticTokenScopes", "contributes.semanticTokenScopes.languages", "contributes.semanticTokenScopes.scopes", "invalid.id", "invalid.id.format", "invalid.superType.format", "invalid.description", "invalid.semanticTokenTypeConfiguration", "invalid.semanticTokenModifierConfiguration", "invalid.semanticTokenScopes.configuration", "invalid.semanticTokenScopes.language", "invalid.semanticTokenScopes.scopes", "invalid.semanticTokenScopes.scopes.value", "invalid.semanticTokenScopes.scopes.selector"]], ["vs/workbench/services/themes/electron-sandbox/themes.contribution", ["window.systemColorTheme.default", "window.systemColorTheme.auto", "window.systemColorTheme.light", "window.systemColorTheme.dark", "window.systemColorTheme"]], ["vs/workbench/services/userDataProfile/browser/extensionsResource", ["installingExtension", "extensions", "all profiles and disabled", "exclude", "exclude"]], ["vs/workbench/services/userDataProfile/browser/globalStateResource", ["globalState"]], ["vs/workbench/services/userDataProfile/browser/keybindingsResource", ["keybindings"]], ["vs/workbench/services/userDataProfile/browser/settingsResource", ["settings"]], ["vs/workbench/services/userDataProfile/browser/snippetsResource", ["snippets", "exclude"]], ["vs/workbench/services/userDataProfile/browser/tasksResource", ["tasks"]], ["vs/workbench/services/userDataProfile/browser/userDataProfileImportExportService", ["create from profile", "installing extensions", "create from profile", "creating settings", "create keybindings", "create tasks", "create snippets", "applying global state", "installing extensions", "troubleshoot issue", "troubleshoot profile progress", "progress extensions", "switching profile", "profiles.exporting", "export success", "copy", "open", "open in", "close", "invalid profile content", "invalid profile content", "progress settings", "progress keybindings", "progress tasks", "progress snippets", "progress global state", "progress extensions", "select profile content handler", "profile already exists", "overwrite", "local", "file", "export profile dialog", "select profile", "select", "select", "from default", "export profile name", "export profile title", "profile name required"]], ["vs/workbench/services/userDataProfile/browser/userDataProfileManagement", ["reload message when removed", "reload message when switched", "reload message when updated", "cannotRenameDefaultProfile", "cannotDeleteDefaultProfile", "switch profile", "reload message", "reload button"]], ["vs/workbench/services/userDataProfile/common/userDataProfile", ["defaultProfileIcon", "profile", "profiles"]], ["vs/workbench/services/userDataProfile/common/userDataProfileIcons", ["settingsViewBarIcon"]], ["vs/workbench/services/userDataSync/browser/userDataSyncWorkbenchService", ["no authentication providers", "no account", "sync in progress", "settings sync", "yes", "no", "sync turned on", "turning on", "resolving conflicts", "syncing...", "and", "conflicts detected", "resolve", "show conflicts", "replace local single", "replace local", "replace remote single", "replace remote", "reset", "reset title", "resetButton", "download sync activity dialog title", "download sync activity dialog open label", "no authentication providers during signin", "choose account placeholder", "signed in", "last used", "others", "sign in using account"]], ["vs/workbench/services/userDataSync/common/userDataSync", ["settings", "keybindings", "snippets", "prompts", "tasks", "extensions", "ui state label", "profiles", "workspace state label", "syncViewIcon", "sync category", "download sync activity title"]], ["vs/workbench/services/views/browser/viewDescriptorService", ["user", "<PERSON><PERSON><PERSON><PERSON>", "toggleVisibilityDescription", "hideViewDescription", "resetViewLocation"]], ["vs/workbench/services/views/browser/viewsService", ["editor", "show view", "toggle view", "show view", "toggle view", "open view", "preserveFocus", "focus view", "resetViewLocation"]], ["vs/workbench/services/views/test/browser/viewContainerModel.test", ["test", "test", "Test View 1", "test", "Test View 1", "test", "Test View 1", "Test View 2", "test", "Test View 1", "Test View 2", "test", "Test View 1", "Test View 2", "Test View 3", "test", "Test View 1", "Test View 2", "Test View 3", "test", "Test View 1", "test", "Test View 1", "test", "Test View 1", "Test View 2", "Test View 3", "test", "Test View 1", "test", "Test View 1", "test", "Test View 1", "test", "Test View 1", "test", "Test View 5", "Test View 2", "Test View 4", "Test View 3", "Test View 1", "test", "Test View 1", "test", "Test View 1", "test", "Test View 1", "test", "Test View 1", "Test View 2", "Test View 3", "test", "Test View 1", "Test View 2", "Test View 3", "test", "Test View 1", "Test View 2", "Test View 3", "Test View 4", "test", "Test View 1"]], ["vs/workbench/services/views/test/browser/viewDescriptorService.test", ["test", "test", "Test View 1", "Test View 2", "Test View 3", "Test View 1", "Test View 2", "Test View 3", "Test View 1", "Test View 2", "Test View 3", "Test View 1", "Test View 2", "Test View 3", "Test View 1", "Test View 2", "Test View 3", "test", "Test View 1", "Test View 2", "Test View 3", "Test View 4", "test", "Test View 1", "Test View 2", "Test View 3", "Test View 4", "Test View 1", "Test View 2", "Test View 3", "Test View 1", "test", "Test View 1", "Test View 2", "Test View 3", "Test View 4", "test", "Test View 1", "test", "Test View 1", "Test View 2", "test", "Test View 1", "Test View 2"]], ["vs/workbench/services/workingCopy/common/fileWorkingCopyManager", ["fileWorkingCopyCreate.source", "fileWorkingCopyReplace.source", "fileWorkingCopyDecorations", "readonlyAndDeleted", "readonly", "deleted", "confirmOverwrite", "overwriteIrreversible", "replaceButtonLabel", "confirmMakeWriteable", "confirmMakeWriteableDetail", "makeWriteableButtonLabel"]], ["vs/workbench/services/workingCopy/common/storedFileWorkingCopy", ["saveParticipants", "saveTextFile", "staleSaveError", "overwrite", "revert", "overwriteElevated", "overwriteElevatedSudo", "saveElevated", "saveElevatedSudo", "overwrite", "retry", "saveAs", "revert", "readonlySaveErrorAdmin", "readonlySaveError<PERSON>udo", "readonlySaveError", "permissionDeniedSaveError", "permissionDeniedSaveErrorSudo", "genericSaveError"]], ["vs/workbench/services/workingCopy/common/storedFileWorkingCopyManager", ["join.fileWorkingCopyManager"]], ["vs/workbench/services/workingCopy/common/storedFileWorkingCopySaveParticipant", ["saveParticipants1", "skip"]], ["vs/workbench/services/workingCopy/common/workingCopyHistoryService", ["default.source", "moved.source", "renamed.source", "join.workingCopyHistory"]], ["vs/workbench/services/workingCopy/common/workingCopyHistoryTracker", ["undoRedo.source"]], ["vs/workbench/services/workingCopy/electron-sandbox/workingCopyBackupService", ["join.workingCopyBackups"]], ["vs/workbench/services/workingCopy/electron-sandbox/workingCopyBackupTracker", ["backupTrackerBackupFailed", "backupTrackerConfirmFailed", "backupErrorDetails", "ok", "shutdownForceClose", "shutdownForceQuit", "shutdownForceReload", "backupBeforeShutdownMessage", "backupBeforeShutdownDetail", "saveBeforeShutdown", "revertBeforeShutdown", "discardBackupsBeforeShutdown"]], ["vs/workbench/services/workspaces/browser/abstractWorkspaceEditingService", ["save", "saveWorkspace", "errorInvalidTaskConfiguration", "openWorkspaceConfigurationFile"]], ["vs/workbench/services/workspaces/browser/workspaceTrustEditorInput", ["workspaceTrustEditorLabelIcon", "workspaceTrustEditorInputName"]], ["vs/workbench/services/workspaces/electron-sandbox/workspaceEditingService", ["saveWorkspaceMessage", "saveWorkspaceDetail", "save", "doNotSave", "doNotAskAgain", "workspaceOpenedMessage", "workspaceOpenedDetail", "restartExtensionHost.reason"]]]