# Terminal Manager - 演示命令

## 🎯 快速测试命令

以下是一些可以用来测试新功能的示例命令：

### Windows 系统命令

#### 1. 查看当前目录
```
命令: dir
描述: 显示当前目录的文件和文件夹
预期输出: 目录列表
```

#### 2. 查看系统信息
```
命令: systeminfo | findstr "OS Name"
描述: 显示操作系统名称
预期输出: 操作系统信息
```

#### 3. 查看网络配置
```
命令: ipconfig
描述: 显示网络配置信息
预期输出: IP地址等网络信息
```

#### 4. 创建测试文件
```
命令: echo "Hello from Terminal Manager" > test.txt
描述: 创建一个包含文本的测试文件
预期输出: 文件创建确认
```

#### 5. 查看文件内容
```
命令: type test.txt
描述: 显示刚创建的测试文件内容
预期输出: "Hello from Terminal Manager"
```

### 跨平台命令

#### 1. Node.js 版本
```
命令: node --version
描述: 显示Node.js版本
预期输出: v18.x.x 或类似版本号
```

#### 2. npm 版本
```
命令: npm --version
描述: 显示npm版本
预期输出: 版本号
```

#### 3. 查看环境变量
```
命令: echo %PATH% (Windows) 或 echo $PATH (Linux/Mac)
描述: 显示系统PATH环境变量
预期输出: 路径列表
```

### 开发相关命令

#### 1. TypeScript 编译
```
命令: npx tsc --version
描述: 显示TypeScript编译器版本
预期输出: Version x.x.x
```

#### 2. 查看package.json
```
命令: type package.json (Windows) 或 cat package.json (Linux/Mac)
描述: 显示项目配置文件
预期输出: JSON配置内容
```

#### 3. 运行npm脚本
```
命令: npm run compile
描述: 编译TypeScript代码
预期输出: 编译过程和结果
```

## 🧪 测试流程

### 基础功能测试

1. **启动扩展**
   - 按 `F5` 启动开发窗口
   - 确认扩展激活成功

2. **执行简单命令**
   - `Ctrl+Shift+P` → `Terminal Manager: Execute Command`
   - 输入：`echo "Test"`
   - 工作目录：使用默认值
   - 观察：实时输出和最终结果

3. **执行复杂命令**
   - 选择一个会产生多行输出的命令（如 `dir` 或 `npm list`）
   - 观察：进度显示和完整输出

4. **测试错误处理**
   - 输入一个不存在的命令（如 `nonexistentcommand`）
   - 观察：错误处理和输出显示

### 输出功能测试

1. **控制台输出验证**
   - 打开开发者工具 (`F12`)
   - 执行命令后查看控制台中的 `[Terminal Output]` 消息

2. **日志输出验证**
   - 查看 "Terminal Manager" 输出频道
   - 确认命令执行日志记录

3. **文档输出验证**
   - 执行命令后查看新打开的输出文档
   - 测试"复制到剪贴板"功能
   - 测试"保存为文件"功能

### 高级功能测试

1. **长时间运行命令**
   - Windows: `ping google.com -t`
   - Linux/Mac: `ping google.com`
   - 测试取消功能

2. **多行输出命令**
   - `npm list` 或 `dir /s`
   - 验证完整输出捕获

3. **交互式命令**
   - 注意：某些交互式命令可能需要特殊处理

## 🎉 成功指标

### 功能正常的标志

✅ **命令执行**
- 命令能够正常执行
- 终端窗口显示执行过程
- 没有错误提示

✅ **输出捕获**
- 控制台显示 `[Terminal Output]` 消息
- 日志频道记录输出信息
- 输出文档正确显示内容

✅ **用户体验**
- 进度指示正常工作
- 完成后显示成功消息
- 输出文档格式正确

✅ **错误处理**
- 无效命令显示错误信息
- 错误输出也能正确捕获
- 用户能够理解错误原因

## 🚀 推荐测试顺序

1. **基础测试**：`echo "Hello"`
2. **目录操作**：`dir` 或 `ls`
3. **版本查询**：`node --version`
4. **文件操作**：创建和读取文件
5. **错误测试**：执行不存在的命令
6. **长输出测试**：`npm list` 或类似命令

通过这些测试，您可以全面验证Terminal Manager的新功能是否正常工作！
