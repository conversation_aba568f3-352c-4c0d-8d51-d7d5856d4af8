2025-05-31 18:27:43.821 [info] Extension host with pid 84564 started
2025-05-31 18:27:43.887 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-05-31 18:27:44.080 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-05-31 18:27:44.155 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-05-31 18:27:44.234 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-05-31 18:27:44.415 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.openRepositoryInParentFolders', provide the URI of a resource or 'null' for any resource.
2025-05-31 18:27:44.415 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.showProgress', provide the URI of a resource or 'null' for any resource.
2025-05-31 18:27:44.446 [info] Eager extensions activated
2025-05-31 18:27:44.577 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-05-31 18:27:44.581 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-05-31 18:27:44.599 [info] ExtensionService#_doActivateExtension undefined_publisher.terminal-manager, startup: false, activationEvent: 'onStartupFinished'
2025-05-31 18:27:44.613 [error] Activating extension undefined_publisher.terminal-manager failed due to an error:
2025-05-31 18:27:44.613 [error] Error: Cannot find module '@utils/path'
Require stack:
- c:\Users\<USER>\Desktop\code\terminal-manager\out\terminal\TerminalManager.js
- c:\Users\<USER>\Desktop\code\terminal-manager\out\extension.js
- c:\Users\<USER>\Desktop\code\terminal-manager\.vscode-test\vscode-win32-x64-archive-1.100.2\resources\app\out\vs\workbench\api\node\extensionHostProcess.js
	at Module._resolveFilename (node:internal/modules/cjs/loader:1219:15)
	at n._resolveFilename (node:electron/js2c/utility_init:2:16068)
	at t._resolveFilename (file:///c:/Users/<USER>/Desktop/code/terminal-manager/.vscode-test/vscode-win32-x64-archive-1.100.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:180:22910)
	at Module._load (node:internal/modules/cjs/loader:1050:27)
	at c._load (node:electron/js2c/node_init:2:16955)
	at e._load (file:///c:/Users/<USER>/Desktop/code/terminal-manager/.vscode-test/vscode-win32-x64-archive-1.100.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:225:1509)
	at t._load (file:///c:/Users/<USER>/Desktop/code/terminal-manager/.vscode-test/vscode-win32-x64-archive-1.100.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:180:22628)
	at r._load (file:///c:/Users/<USER>/Desktop/code/terminal-manager/.vscode-test/vscode-win32-x64-archive-1.100.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:172:22062)
	at Module.require (node:internal/modules/cjs/loader:1305:19)
	at require (node:internal/modules/helpers:182:18)
	at Object.<anonymous> (c:\Users\<USER>\Desktop\code\terminal-manager\out\terminal\TerminalManager.js:32:16)
	at Module._compile (node:internal/modules/cjs/loader:1544:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1629:10)
	at Module.load (node:internal/modules/cjs/loader:1282:32)
	at Module._load (node:internal/modules/cjs/loader:1103:12)
	at c._load (node:electron/js2c/node_init:2:16955)
	at e._load (file:///c:/Users/<USER>/Desktop/code/terminal-manager/.vscode-test/vscode-win32-x64-archive-1.100.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:225:1509)
	at t._load (file:///c:/Users/<USER>/Desktop/code/terminal-manager/.vscode-test/vscode-win32-x64-archive-1.100.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:180:22628)
	at r._load (file:///c:/Users/<USER>/Desktop/code/terminal-manager/.vscode-test/vscode-win32-x64-archive-1.100.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:172:22062)
	at Module.require (node:internal/modules/cjs/loader:1305:19)
	at require (node:internal/modules/helpers:182:18)
	at Object.<anonymous> (c:\Users\<USER>\Desktop\code\terminal-manager\out\extension.js:29:27)
	at Module._compile (node:internal/modules/cjs/loader:1544:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1629:10)
	at Module.load (node:internal/modules/cjs/loader:1282:32)
	at Module._load (node:internal/modules/cjs/loader:1103:12)
	at c._load (node:electron/js2c/node_init:2:16955)
	at e._load (file:///c:/Users/<USER>/Desktop/code/terminal-manager/.vscode-test/vscode-win32-x64-archive-1.100.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:225:1509)
	at t._load (file:///c:/Users/<USER>/Desktop/code/terminal-manager/.vscode-test/vscode-win32-x64-archive-1.100.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:180:22628)
	at r._load (file:///c:/Users/<USER>/Desktop/code/terminal-manager/.vscode-test/vscode-win32-x64-archive-1.100.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:172:22062)
	at Module.require (node:internal/modules/cjs/loader:1305:19)
	at require (node:internal/modules/helpers:182:18)
	at PJ.Cb (file:///c:/Users/<USER>/Desktop/code/terminal-manager/.vscode-test/vscode-win32-x64-archive-1.100.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:211:1253)
2025-05-31 18:27:44.644 [info] Extension host terminating: renderer closed the MessagePort
2025-05-31 18:27:44.664 [info] Extension host with pid 84564 exiting with code 0
