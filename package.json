{"name": "terminal-manager", "displayName": "Terminal Manager", "description": "A VSCode extension for advanced terminal management and command execution", "version": "0.1.0", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "terminalManager.executeCommand", "title": "Execute Command", "category": "Terminal Manager"}], "configuration": {"title": "Terminal Manager", "properties": {"terminalManager.autoCompile": {"type": "boolean", "default": true, "description": "Enable automatic compilation on file changes"}, "terminalManager.autoReload": {"type": "boolean", "default": true, "description": "Enable automatic extension reload on source file changes"}, "terminalManager.shellIntegrationTimeout": {"type": "number", "default": 4000, "description": "Timeout for shell integration in milliseconds"}, "terminalManager.maxTerminals": {"type": "number", "default": 10, "description": "Maximum number of terminals to keep active"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "lint:fix": "eslint src --ext ts --fix", "test": "node ./out/test/runTest.js", "package": "vsce package", "dev": "node scripts/dev.js", "clean": "<PERSON><PERSON><PERSON> out", "rebuild": "npm run clean && npm run compile"}, "devDependencies": {"@types/glob": "^8.1.0", "@types/mocha": "^10.0.1", "@types/node": "16.x", "@types/should": "^13.0.0", "@types/sinon": "^10.0.13", "@types/vscode": "^1.74.0", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "@vscode/test-electron": "^2.2.0", "eslint": "^8.28.0", "mocha": "^10.1.0", "rimraf": "^6.0.1", "should": "^13.2.3", "sinon": "^15.0.0", "typescript": "^4.9.4"}, "dependencies": {"chokidar": "^3.5.3", "p-wait-for": "^5.0.0"}}