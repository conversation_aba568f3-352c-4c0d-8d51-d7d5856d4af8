#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Terminal Manager development environment...\n');

// Start TypeScript compiler in watch mode
console.log('📦 Starting TypeScript compiler in watch mode...');
const tscWatch = spawn('npm', ['run', 'watch'], {
    cwd: path.resolve(__dirname, '..'),
    stdio: 'inherit',
    shell: true
});

tscWatch.on('error', (error) => {
    console.error('❌ TypeScript compiler error:', error);
});

tscWatch.on('close', (code) => {
    console.log(`📦 TypeScript compiler exited with code ${code}`);
});

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n🛑 Stopping development environment...');
    tscWatch.kill('SIGINT');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Stopping development environment...');
    tscWatch.kill('SIGTERM');
    process.exit(0);
});

console.log('✅ Development environment started!');
console.log('📝 TypeScript files will be compiled automatically on changes');
console.log('🔧 Press F5 in VSCode to launch the extension');
console.log('🛑 Press Ctrl+C to stop\n');
