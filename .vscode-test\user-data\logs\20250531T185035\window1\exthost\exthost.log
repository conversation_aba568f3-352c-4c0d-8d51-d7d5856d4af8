2025-05-31 18:50:36.752 [info] Extension host with pid 88136 started
2025-05-31 18:50:36.774 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-05-31 18:50:36.811 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-05-31 18:50:36.816 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-05-31 18:50:36.855 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-05-31 18:50:37.009 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.openRepositoryInParentFolders', provide the URI of a resource or 'null' for any resource.
2025-05-31 18:50:37.009 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.showProgress', provide the URI of a resource or 'null' for any resource.
2025-05-31 18:50:37.033 [info] Eager extensions activated
2025-05-31 18:50:37.119 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-05-31 18:50:37.124 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-05-31 18:50:37.142 [info] ExtensionService#_doActivateExtension undefined_publisher.terminal-manager, startup: false, activationEvent: 'onStartupFinished'
2025-05-31 18:50:37.189 [info] Extension host terminating: renderer closed the MessagePort
2025-05-31 18:50:37.200 [info] Extension host with pid 88136 exiting with code 0
