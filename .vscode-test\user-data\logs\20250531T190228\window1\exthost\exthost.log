2025-05-31 19:02:30.622 [info] Extension host with pid 45808 started
2025-05-31 19:02:30.680 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-05-31 19:02:30.773 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-05-31 19:02:30.781 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-05-31 19:02:30.837 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-05-31 19:02:30.974 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.openRepositoryInParentFolders', provide the URI of a resource or 'null' for any resource.
2025-05-31 19:02:30.974 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.showProgress', provide the URI of a resource or 'null' for any resource.
2025-05-31 19:02:30.988 [info] Eager extensions activated
2025-05-31 19:02:31.075 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-05-31 19:02:31.078 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-05-31 19:02:31.097 [info] ExtensionService#_doActivateExtension undefined_publisher.terminal-manager, startup: false, activationEvent: 'onStartupFinished'
2025-05-31 19:02:31.182 [info] Extension host terminating: renderer closed the MessagePort
2025-05-31 19:02:31.199 [info] Extension host with pid 45808 exiting with code 0
