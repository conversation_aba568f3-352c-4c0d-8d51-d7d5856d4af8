/*!--------------------------------------------------------
 * Copyright (C) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/var Qe=function(t,e){return Qe=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,i){s.__proto__=i}||function(s,i){for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(s[r]=i[r])},Qe(t,e)};export function __extends(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");Qe(t,e);function s(){this.constructor=t}t.prototype=e===null?Object.create(e):(s.prototype=e.prototype,new s)}export var __assign=function(){return __assign=Object.assign||function(e){for(var s,i=1,r=arguments.length;i<r;i++){s=arguments[i];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},__assign.apply(this,arguments)};export function __rest(t,e){var s={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(s[i]=t[i]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,i=Object.getOwnPropertySymbols(t);r<i.length;r++)e.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(t,i[r])&&(s[i[r]]=t[i[r]]);return s}export function __decorate(t,e,s,i){var r=arguments.length,n=r<3?e:i===null?i=Object.getOwnPropertyDescriptor(e,s):i,o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")n=Reflect.decorate(t,e,s,i);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(n=(r<3?o(n):r>3?o(e,s,n):o(e,s))||n);return r>3&&n&&Object.defineProperty(e,s,n),n}export function __param(t,e){return function(s,i){e(s,i,t)}}export function __esDecorate(t,e,s,i,r,n){function o(O){if(O!==void 0&&typeof O!="function")throw new TypeError("Function expected");return O}for(var a=i.kind,h=a==="getter"?"get":a==="setter"?"set":"value",c=!e&&t?i.static?t:t.prototype:null,f=e||(c?Object.getOwnPropertyDescriptor(c,i.name):{}),l,u=!1,d=s.length-1;d>=0;d--){var v={};for(var y in i)v[y]=y==="access"?{}:i[y];for(var y in i.access)v.access[y]=i.access[y];v.addInitializer=function(O){if(u)throw new TypeError("Cannot add initializers after decoration has completed");n.push(o(O||null))};var E=(0,s[d])(a==="accessor"?{get:f.get,set:f.set}:f[h],v);if(a==="accessor"){if(E===void 0)continue;if(E===null||typeof E!="object")throw new TypeError("Object expected");(l=o(E.get))&&(f.get=l),(l=o(E.set))&&(f.set=l),(l=o(E.init))&&r.unshift(l)}else(l=o(E))&&(a==="field"?r.unshift(l):f[h]=l)}c&&Object.defineProperty(c,i.name,f),u=!0}export function __runInitializers(t,e,s){for(var i=arguments.length>2,r=0;r<e.length;r++)s=i?e[r].call(t,s):e[r].call(t);return i?s:void 0}export function __propKey(t){return typeof t=="symbol"?t:"".concat(t)}export function __setFunctionName(t,e,s){return typeof e=="symbol"&&(e=e.description?"[".concat(e.description,"]"):""),Object.defineProperty(t,"name",{configurable:!0,value:s?"".concat(s," ",e):e})}export function __metadata(t,e){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(t,e)}export function __awaiter(t,e,s,i){function r(n){return n instanceof s?n:new s(function(o){o(n)})}return new(s||(s=Promise))(function(n,o){function a(f){try{c(i.next(f))}catch(l){o(l)}}function h(f){try{c(i.throw(f))}catch(l){o(l)}}function c(f){f.done?n(f.value):r(f.value).then(a,h)}c((i=i.apply(t,e||[])).next())})}export function __generator(t,e){var s={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},i,r,n,o;return o={next:a(0),throw:a(1),return:a(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function a(c){return function(f){return h([c,f])}}function h(c){if(i)throw new TypeError("Generator is already executing.");for(;o&&(o=0,c[0]&&(s=0)),s;)try{if(i=1,r&&(n=c[0]&2?r.return:c[0]?r.throw||((n=r.return)&&n.call(r),0):r.next)&&!(n=n.call(r,c[1])).done)return n;switch(r=0,n&&(c=[c[0]&2,n.value]),c[0]){case 0:case 1:n=c;break;case 4:return s.label++,{value:c[1],done:!1};case 5:s.label++,r=c[1],c=[0];continue;case 7:c=s.ops.pop(),s.trys.pop();continue;default:if(n=s.trys,!(n=n.length>0&&n[n.length-1])&&(c[0]===6||c[0]===2)){s=0;continue}if(c[0]===3&&(!n||c[1]>n[0]&&c[1]<n[3])){s.label=c[1];break}if(c[0]===6&&s.label<n[1]){s.label=n[1],n=c;break}if(n&&s.label<n[2]){s.label=n[2],s.ops.push(c);break}n[2]&&s.ops.pop(),s.trys.pop();continue}c=e.call(t,s)}catch(f){c=[6,f],r=0}finally{i=n=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}export var __createBinding=Object.create?function(t,e,s,i){i===void 0&&(i=s);var r=Object.getOwnPropertyDescriptor(e,s);(!r||("get"in r?!e.__esModule:r.writable||r.configurable))&&(r={enumerable:!0,get:function(){return e[s]}}),Object.defineProperty(t,i,r)}:function(t,e,s,i){i===void 0&&(i=s),t[i]=e[s]};export function __exportStar(t,e){for(var s in t)s!=="default"&&!Object.prototype.hasOwnProperty.call(e,s)&&__createBinding(e,t,s)}export function __values(t){var e=typeof Symbol=="function"&&Symbol.iterator,s=e&&t[e],i=0;if(s)return s.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&i>=t.length&&(t=void 0),{value:t&&t[i++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}export function __read(t,e){var s=typeof Symbol=="function"&&t[Symbol.iterator];if(!s)return t;var i=s.call(t),r,n=[],o;try{for(;(e===void 0||e-- >0)&&!(r=i.next()).done;)n.push(r.value)}catch(a){o={error:a}}finally{try{r&&!r.done&&(s=i.return)&&s.call(i)}finally{if(o)throw o.error}}return n}export function __spread(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(__read(arguments[e]));return t}export function __spreadArrays(){for(var t=0,e=0,s=arguments.length;e<s;e++)t+=arguments[e].length;for(var i=Array(t),r=0,e=0;e<s;e++)for(var n=arguments[e],o=0,a=n.length;o<a;o++,r++)i[r]=n[o];return i}export function __spreadArray(t,e,s){if(s||arguments.length===2)for(var i=0,r=e.length,n;i<r;i++)(n||!(i in e))&&(n||(n=Array.prototype.slice.call(e,0,i)),n[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))}export function __await(t){return this instanceof __await?(this.v=t,this):new __await(t)}export function __asyncGenerator(t,e,s){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var i=s.apply(t,e||[]),r,n=[];return r={},a("next"),a("throw"),a("return",o),r[Symbol.asyncIterator]=function(){return this},r;function o(d){return function(v){return Promise.resolve(v).then(d,l)}}function a(d,v){i[d]&&(r[d]=function(y){return new Promise(function(E,O){n.push([d,y,E,O])>1||h(d,y)})},v&&(r[d]=v(r[d])))}function h(d,v){try{c(i[d](v))}catch(y){u(n[0][3],y)}}function c(d){d.value instanceof __await?Promise.resolve(d.value.v).then(f,l):u(n[0][2],d)}function f(d){h("next",d)}function l(d){h("throw",d)}function u(d,v){d(v),n.shift(),n.length&&h(n[0][0],n[0][1])}}export function __asyncDelegator(t){var e,s;return e={},i("next"),i("throw",function(r){throw r}),i("return"),e[Symbol.iterator]=function(){return this},e;function i(r,n){e[r]=t[r]?function(o){return(s=!s)?{value:__await(t[r](o)),done:!1}:n?n(o):o}:n}}export function __asyncValues(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=t[Symbol.asyncIterator],s;return e?e.call(t):(t=typeof __values=="function"?__values(t):t[Symbol.iterator](),s={},i("next"),i("throw"),i("return"),s[Symbol.asyncIterator]=function(){return this},s);function i(n){s[n]=t[n]&&function(o){return new Promise(function(a,h){o=t[n](o),r(a,h,o.done,o.value)})}}function r(n,o,a,h){Promise.resolve(h).then(function(c){n({value:c,done:a})},o)}}export function __makeTemplateObject(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t}var Qi=Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e};export function __importStar(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var s in t)s!=="default"&&Object.prototype.hasOwnProperty.call(t,s)&&__createBinding(e,t,s);return Qi(e,t),e}export function __importDefault(t){return t&&t.__esModule?t:{default:t}}export function __classPrivateFieldGet(t,e,s,i){if(s==="a"&&!i)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?t!==e||!i:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return s==="m"?i:s==="a"?i.call(t):i?i.value:e.get(t)}export function __classPrivateFieldSet(t,e,s,i,r){if(i==="m")throw new TypeError("Private method is not writable");if(i==="a"&&!r)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?t!==e||!r:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return i==="a"?r.call(t,s):r?r.value=s:e.set(t,s),s}export function __classPrivateFieldIn(t,e){if(e===null||typeof e!="object"&&typeof e!="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof t=="function"?e===t:t.has(e)}export function __addDisposableResource(t,e,s){if(e!=null){if(typeof e!="object"&&typeof e!="function")throw new TypeError("Object expected.");var i,r;if(s){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=e[Symbol.asyncDispose]}if(i===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=e[Symbol.dispose],s&&(r=i)}if(typeof i!="function")throw new TypeError("Object not disposable.");r&&(i=function(){try{r.call(this)}catch(n){return Promise.reject(n)}}),t.stack.push({value:e,dispose:i,async:s})}else s&&t.stack.push({async:!0});return e}var Ki=typeof SuppressedError=="function"?SuppressedError:function(t,e,s){var i=new Error(s);return i.name="SuppressedError",i.error=t,i.suppressed=e,i};export function __disposeResources(t){function e(i){t.error=t.hasError?new Ki(i,t.error,"An error was suppressed during disposal."):i,t.hasError=!0}function s(){for(;t.stack.length;){var i=t.stack.pop();try{var r=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(r).then(s,function(n){return e(n),s()})}catch(n){e(n)}}if(t.hasError)throw t.error}return s()}export default{__extends,__assign,__rest,__decorate,__param,__metadata,__awaiter,__generator,__createBinding,__exportStar,__values,__read,__spread,__spreadArrays,__spreadArray,__await,__asyncGenerator,__asyncDelegator,__asyncValues,__makeTemplateObject,__importStar,__importDefault,__classPrivateFieldGet,__classPrivateFieldSet,__classPrivateFieldIn,__addDisposableResource,__disposeResources};function Gi(t,e,s=0,i=t.length){let r=s,n=i;for(;r<n;){const o=Math.floor((r+n)/2);e(t[o])?r=o+1:n=o}return r-1}var $2=class Ri{static{this.assertInvariants=!1}constructor(e){this.e=e,this.c=0}findLastMonotonous(e){if(Ri.assertInvariants){if(this.d){for(const i of this.e)if(this.d(i)&&!e(i))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.")}this.d=e}const s=Gi(this.e,e,this.c);return this.c=s+1,s===-1?void 0:this.e[s]}},Ji=class{constructor(){this.b=[],this.a=function(t){setTimeout(()=>{throw t.stack?Vt.isErrorNoTelemetry(t)?new Vt(t.message+`

`+t.stack):new Error(t.message+`

`+t.stack):t},0)}}addListener(t){return this.b.push(t),()=>{this.d(t)}}c(t){this.b.forEach(e=>{e(t)})}d(t){this.b.splice(this.b.indexOf(t),1)}setUnexpectedErrorHandler(t){this.a=t}getUnexpectedErrorHandler(){return this.a}onUnexpectedError(t){this.a(t),this.c(t)}onUnexpectedExternalError(t){this.a(t)}},Yi=new Ji;function Ht(t){Zi(t)||Yi.onUnexpectedError(t)}var Ke="Canceled";function Zi(t){return t instanceof lt?!0:t instanceof Error&&t.name===Ke&&t.message===Ke}var lt=class extends Error{constructor(){super(Ke),this.name=this.message}},Vt=class _1 extends Error{constructor(e){super(e),this.name="CodeExpectedError"}static fromError(e){if(e instanceof _1)return e;const s=new _1;return s.message=e.message,s.stack=e.stack,s}static isErrorNoTelemetry(e){return e.name==="CodeExpectedError"}},Xi=class Ii extends Error{constructor(e){super(e||"An unexpected bug occurred."),Object.setPrototypeOf(this,Ii.prototype)}};function tr(t,e,s=(i,r)=>i===r){if(t===e)return!0;if(!t||!e||t.length!==e.length)return!1;for(let i=0,r=t.length;i<r;i++)if(!s(t[i],e[i]))return!1;return!0}function er(t){return t.filter(e=>!!e)}function N1(t,e){let s;if(typeof e=="number"){let i=e;s=()=>{const r=Math.sin(i++)*179426549;return r-Math.floor(r)}}else s=Math.random;for(let i=t.length-1;i>0;i-=1){const r=Math.floor(s()*(i+1)),n=t[i];t[i]=t[r],t[r]=n}}function sr(t){return t[Math.floor(Math.random()*t.length)]}var Ge;(function(t){function e(n){return n<0}t.isLessThan=e;function s(n){return n<=0}t.isLessThanOrEqual=s;function i(n){return n>0}t.isGreaterThan=i;function r(n){return n===0}t.isNeitherLessOrGreaterThan=r,t.greaterThan=1,t.lessThan=-1,t.neitherLessOrGreaterThan=0})(Ge||(Ge={}));function ir(t,e){return(s,i)=>e(t(s),t(i))}var rr=(t,e)=>t-e,E2=class We{static{this.empty=new We(e=>{})}constructor(e){this.iterate=e}forEach(e){this.iterate(s=>(e(s),!0))}toArray(){const e=[];return this.iterate(s=>(e.push(s),!0)),e}filter(e){return new We(s=>this.iterate(i=>e(i)?s(i):!0))}map(e){return new We(s=>this.iterate(i=>s(e(i))))}some(e){let s=!1;return this.iterate(i=>(s=e(i),!s)),s}findFirst(e){let s;return this.iterate(i=>e(i)?(s=i,!1):!0),s}findLast(e){let s;return this.iterate(i=>(e(i)&&(s=i),!0)),s}findLastMaxBy(e){let s,i=!0;return this.iterate(r=>((i||Ge.isGreaterThan(e(r,s)))&&(i=!1,s=r),!0)),s}},j1;function nr(t,e){const s=Object.create(null);for(const i of t){const r=e(i);let n=s[r];n||(n=s[r]=[]),n.push(i)}return s}var A2=class{static{j1=Symbol.toStringTag}constructor(t,e){this.b=e,this.a=new Map,this[j1]="SetWithKey";for(const s of t)this.add(s)}get size(){return this.a.size}add(t){const e=this.b(t);return this.a.set(e,t),this}delete(t){return this.a.delete(this.b(t))}has(t){return this.a.has(this.b(t))}*entries(){for(const t of this.a.values())yield[t,t]}keys(){return this.values()}*values(){for(const t of this.a.values())yield t}clear(){this.a.clear()}forEach(t,e){this.a.forEach(s=>t.call(e,s,s,this))}[Symbol.iterator](){return this.values()}},M1,T1,U1,or=class{constructor(t,e){this.uri=t,this.value=e}};function ar(t){return Array.isArray(t)}var F1=class ne{static{this.c=e=>e.toString()}constructor(e,s){if(this[M1]="ResourceMap",e instanceof ne)this.d=new Map(e.d),this.e=s??ne.c;else if(ar(e)){this.d=new Map,this.e=s??ne.c;for(const[i,r]of e)this.set(i,r)}else this.d=new Map,this.e=e??ne.c}set(e,s){return this.d.set(this.e(e),new or(e,s)),this}get(e){return this.d.get(this.e(e))?.value}has(e){return this.d.has(this.e(e))}get size(){return this.d.size}clear(){this.d.clear()}delete(e){return this.d.delete(this.e(e))}forEach(e,s){typeof s<"u"&&(e=e.bind(s));for(const[i,r]of this.d)e(r.value,r.uri,this)}*values(){for(const e of this.d.values())yield e.value}*keys(){for(const e of this.d.values())yield e.uri}*entries(){for(const e of this.d.values())yield[e.uri,e.value]}*[(M1=Symbol.toStringTag,Symbol.iterator)](){for(const[,e]of this.d)yield[e.uri,e.value]}},C2=class{constructor(t,e){this[T1]="ResourceSet",!t||typeof t=="function"?this.c=new F1(t):(this.c=new F1(e),t.forEach(this.add,this))}get size(){return this.c.size}add(t){return this.c.set(t,t),this}clear(){this.c.clear()}delete(t){return this.c.delete(t)}forEach(t,e){this.c.forEach((s,i)=>t.call(e,i,i,this))}has(t){return this.c.has(t)}entries(){return this.c.entries()}keys(){return this.c.keys()}values(){return this.c.keys()}[(T1=Symbol.toStringTag,Symbol.iterator)](){return this.keys()}},W1;(function(t){t[t.None=0]="None",t[t.AsOld=1]="AsOld",t[t.AsNew=2]="AsNew"})(W1||(W1={}));var cr=class{constructor(){this[U1]="LinkedMap",this.c=new Map,this.d=void 0,this.e=void 0,this.f=0,this.g=0}clear(){this.c.clear(),this.d=void 0,this.e=void 0,this.f=0,this.g++}isEmpty(){return!this.d&&!this.e}get size(){return this.f}get first(){return this.d?.value}get last(){return this.e?.value}has(t){return this.c.has(t)}get(t,e=0){const s=this.c.get(t);if(s)return e!==0&&this.n(s,e),s.value}set(t,e,s=0){let i=this.c.get(t);if(i)i.value=e,s!==0&&this.n(i,s);else{switch(i={key:t,value:e,next:void 0,previous:void 0},s){case 0:this.l(i);break;case 1:this.k(i);break;case 2:this.l(i);break;default:this.l(i);break}this.c.set(t,i),this.f++}return this}delete(t){return!!this.remove(t)}remove(t){const e=this.c.get(t);if(e)return this.c.delete(t),this.m(e),this.f--,e.value}shift(){if(!this.d&&!this.e)return;if(!this.d||!this.e)throw new Error("Invalid list");const t=this.d;return this.c.delete(t.key),this.m(t),this.f--,t.value}forEach(t,e){const s=this.g;let i=this.d;for(;i;){if(e?t.bind(e)(i.value,i.key,this):t(i.value,i.key,this),this.g!==s)throw new Error("LinkedMap got modified during iteration.");i=i.next}}keys(){const t=this,e=this.g;let s=this.d;const i={[Symbol.iterator](){return i},next(){if(t.g!==e)throw new Error("LinkedMap got modified during iteration.");if(s){const r={value:s.key,done:!1};return s=s.next,r}else return{value:void 0,done:!0}}};return i}values(){const t=this,e=this.g;let s=this.d;const i={[Symbol.iterator](){return i},next(){if(t.g!==e)throw new Error("LinkedMap got modified during iteration.");if(s){const r={value:s.value,done:!1};return s=s.next,r}else return{value:void 0,done:!0}}};return i}entries(){const t=this,e=this.g;let s=this.d;const i={[Symbol.iterator](){return i},next(){if(t.g!==e)throw new Error("LinkedMap got modified during iteration.");if(s){const r={value:[s.key,s.value],done:!1};return s=s.next,r}else return{value:void 0,done:!0}}};return i}[(U1=Symbol.toStringTag,Symbol.iterator)](){return this.entries()}h(t){if(t>=this.size)return;if(t===0){this.clear();return}let e=this.d,s=this.size;for(;e&&s>t;)this.c.delete(e.key),e=e.next,s--;this.d=e,this.f=s,e&&(e.previous=void 0),this.g++}j(t){if(t>=this.size)return;if(t===0){this.clear();return}let e=this.e,s=this.size;for(;e&&s>t;)this.c.delete(e.key),e=e.previous,s--;this.e=e,this.f=s,e&&(e.next=void 0),this.g++}k(t){if(!this.d&&!this.e)this.e=t;else if(this.d)t.next=this.d,this.d.previous=t;else throw new Error("Invalid list");this.d=t,this.g++}l(t){if(!this.d&&!this.e)this.d=t;else if(this.e)t.previous=this.e,this.e.next=t;else throw new Error("Invalid list");this.e=t,this.g++}m(t){if(t===this.d&&t===this.e)this.d=void 0,this.e=void 0;else if(t===this.d){if(!t.next)throw new Error("Invalid list");t.next.previous=void 0,this.d=t.next}else if(t===this.e){if(!t.previous)throw new Error("Invalid list");t.previous.next=void 0,this.e=t.previous}else{const e=t.next,s=t.previous;if(!e||!s)throw new Error("Invalid list");e.previous=s,s.next=e}t.next=void 0,t.previous=void 0,this.g++}n(t,e){if(!this.d||!this.e)throw new Error("Invalid list");if(!(e!==1&&e!==2)){if(e===1){if(t===this.d)return;const s=t.next,i=t.previous;t===this.e?(i.next=void 0,this.e=i):(s.previous=i,i.next=s),t.previous=void 0,t.next=this.d,this.d.previous=t,this.d=t,this.g++}else if(e===2){if(t===this.e)return;const s=t.next,i=t.previous;t===this.d?(s.previous=void 0,this.d=s):(s.previous=i,i.next=s),t.next=void 0,t.previous=this.e,this.e.next=t,this.e=t,this.g++}}}toJSON(){const t=[];return this.forEach((e,s)=>{t.push([s,e])}),t}fromJSON(t){this.clear();for(const[e,s]of t)this.set(e,s)}},hr=class extends cr{constructor(t,e=1){super(),this.o=t,this.p=Math.min(Math.max(0,e),1)}get limit(){return this.o}set limit(t){this.o=t,this.q()}get ratio(){return this.p}set ratio(t){this.p=Math.min(Math.max(0,t),1),this.q()}get(t,e=2){return super.get(t,e)}peek(t){return super.get(t,0)}set(t,e){return super.set(t,e,2),this}q(){this.size>this.o&&this.r(Math.round(this.o*this.p))}},Je=class extends hr{constructor(t,e=1){super(t,e)}r(t){this.h(t)}set(t,e){return super.set(t,e),this.q(),this}},lr=class{constructor(){this.c=new Map}add(t,e){let s=this.c.get(t);s||(s=new Set,this.c.set(t,s)),s.add(e)}delete(t,e){const s=this.c.get(t);s&&(s.delete(e),s.size===0&&this.c.delete(t))}forEach(t,e){const s=this.c.get(t);s&&s.forEach(e)}get(t){const e=this.c.get(t);return e||new Set}};function Ye(t,e){const s=this;let i=!1,r;return function(){if(i)return r;if(i=!0,e)try{r=t.apply(s,arguments)}finally{e()}else r=t.apply(s,arguments);return r}}function B1(t,e="unexpected state"){if(!t)throw typeof e=="string"?new Xi(`Assertion Failed: ${e}`):e}function fr(t){return typeof t=="string"}function ur(t){return typeof t=="number"&&!isNaN(t)}function dr(t){return!!t&&typeof t[Symbol.iterator]=="function"}function gr(t){return typeof t>"u"}function pr(t){return gr(t)||t===null}function wr(t,e){if(!t)throw new Error(e?`Unexpected type, expected '${e}'`:"Unexpected type")}function q1(t){return typeof t=="function"}var Ze;(function(t){function e(p){return p&&typeof p=="object"&&typeof p[Symbol.iterator]=="function"}t.is=e;const s=Object.freeze([]);function i(){return s}t.empty=i;function*r(p){yield p}t.single=r;function n(p){return e(p)?p:r(p)}t.wrap=n;function o(p){return p||s}t.from=o;function*a(p){for(let x=p.length-1;x>=0;x--)yield p[x]}t.reverse=a;function h(p){return!p||p[Symbol.iterator]().next().done===!0}t.isEmpty=h;function c(p){return p[Symbol.iterator]().next().value}t.first=c;function f(p,x){let k=0;for(const V of p)if(x(V,k++))return!0;return!1}t.some=f;function l(p,x){for(const k of p)if(x(k))return k}t.find=l;function*u(p,x){for(const k of p)x(k)&&(yield k)}t.filter=u;function*d(p,x){let k=0;for(const V of p)yield x(V,k++)}t.map=d;function*v(p,x){let k=0;for(const V of p)yield*x(V,k++)}t.flatMap=v;function*y(...p){for(const x of p)dr(x)?yield*x:yield x}t.concat=y;function E(p,x,k){let V=k;for(const kt of p)V=x(V,kt);return V}t.reduce=E;function O(p){let x=0;for(const k of p)x++;return x}t.length=O;function*et(p,x,k=p.length){for(x<-p.length&&(x=0),x<0&&(x+=p.length),k<0?k+=p.length:k>p.length&&(k=p.length);x<k;x++)yield p[x]}t.slice=et;function j(p,x=Number.POSITIVE_INFINITY){const k=[];if(x===0)return[k,p];const V=p[Symbol.iterator]();for(let kt=0;kt<x;kt++){const le=V.next();if(le.done)return[k,t.empty()];k.push(le.value)}return[k,{[Symbol.iterator](){return V}}]}t.consume=j;async function Pt(p){const x=[];for await(const k of p)x.push(k);return Promise.resolve(x)}t.asyncToArray=Pt})(Ze||(Ze={}));var vr=!1,Rt=null,x2=class Oi{constructor(){this.b=new Map}static{this.a=0}c(e){let s=this.b.get(e);return s||(s={parent:null,source:null,isSingleton:!1,value:e,idx:Oi.a++},this.b.set(e,s)),s}trackDisposable(e){const s=this.c(e);s.source||(s.source=new Error().stack)}setParent(e,s){const i=this.c(e);i.parent=s}markAsDisposed(e){this.b.delete(e)}markAsSingleton(e){this.c(e).isSingleton=!0}f(e,s){const i=s.get(e);if(i)return i;const r=e.parent?this.f(this.c(e.parent),s):e;return s.set(e,r),r}getTrackedDisposables(){const e=new Map;return[...this.b.entries()].filter(([,i])=>i.source!==null&&!this.f(i,e).isSingleton).flatMap(([i])=>i)}computeLeakingDisposables(e=10,s){let i;if(s)i=s;else{const h=new Map,c=[...this.b.values()].filter(l=>l.source!==null&&!this.f(l,h).isSingleton);if(c.length===0)return;const f=new Set(c.map(l=>l.value));if(i=c.filter(l=>!(l.parent&&f.has(l.parent))),i.length===0)throw new Error("There are cyclic diposable chains!")}if(!i)return;function r(h){function c(l,u){for(;l.length>0&&u.some(d=>typeof d=="string"?d===l[0]:l[0].match(d));)l.shift()}const f=h.source.split(`
`).map(l=>l.trim().replace("at ","")).filter(l=>l!=="");return c(f,["Error",/^trackDisposable \(.*\)$/,/^DisposableTracker.trackDisposable \(.*\)$/]),f.reverse()}const n=new lr;for(const h of i){const c=r(h);for(let f=0;f<=c.length;f++)n.add(c.slice(0,f).join(`
`),h)}i.sort(ir(h=>h.idx,rr));let o="",a=0;for(const h of i.slice(0,e)){a++;const c=r(h),f=[];for(let l=0;l<c.length;l++){let u=c[l];u=`(shared with ${n.get(c.slice(0,l+1).join(`
`)).size}/${i.length} leaks) at ${u}`;const v=n.get(c.slice(0,l).join(`
`)),y=nr([...v].map(E=>r(E)[l]),E=>E);delete y[c[l]];for(const[E,O]of Object.entries(y))f.unshift(`    - stacktraces of ${O.length} other leaks continue with ${E}`);f.unshift(u)}o+=`


==================== Leaking disposable ${a}/${i.length}: ${h.value.constructor.name} ====================
${f.join(`
`)}
============================================================

`}return i.length>e&&(o+=`


... and ${i.length-e} more leaking disposables

`),{leaks:i,details:o}}};function mr(t){Rt=t}if(vr){const t="__is_disposable_tracked__";mr(new class{trackDisposable(e){const s=new Error("Potentially leaked disposable").stack;setTimeout(()=>{e[t]||console.log(s)},3e3)}setParent(e,s){if(e&&e!==X.None)try{e[t]=!0}catch{}}markAsDisposed(e){if(e&&e!==X.None)try{e[t]=!0}catch{}}markAsSingleton(e){}})}function Qt(t){return Rt?.trackDisposable(t),t}function Kt(t){Rt?.markAsDisposed(t)}function Gt(t,e){Rt?.setParent(t,e)}function br(t,e){if(Rt)for(const s of t)Rt.setParent(s,e)}function yr(t){return typeof t=="object"&&t!==null&&typeof t.dispose=="function"&&t.dispose.length===0}function Jt(t){if(Ze.is(t)){const e=[];for(const s of t)if(s)try{s.dispose()}catch(i){e.push(i)}if(e.length===1)throw e[0];if(e.length>1)throw new AggregateError(e,"Encountered errors while disposing of store");return Array.isArray(t)?[]:t}else if(t)return t.dispose(),t}function $r(...t){const e=G(()=>Jt(t));return br(t,e),e}function G(t){const e=Qt({dispose:Ye(()=>{Kt(e),t()})});return e}var st=class Ni{static{this.DISABLE_DISPOSED_WARNING=!1}constructor(){this.f=new Set,this.g=!1,Qt(this)}dispose(){this.g||(Kt(this),this.g=!0,this.clear())}get isDisposed(){return this.g}clear(){if(this.f.size!==0)try{Jt(this.f)}finally{this.f.clear()}}add(e){if(!e)return e;if(e===this)throw new Error("Cannot register a disposable on itself!");return Gt(e,this),this.g?Ni.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this.f.add(e),e}delete(e){if(e){if(e===this)throw new Error("Cannot dispose a disposable on itself!");this.f.delete(e),e.dispose()}}deleteAndLeak(e){e&&this.f.has(e)&&(this.f.delete(e),Gt(e,null))}},X=class{static{this.None=Object.freeze({dispose(){}})}constructor(){this.q=new st,Qt(this),Gt(this.q,this)}dispose(){Kt(this),this.q.dispose()}B(t){if(t===this)throw new Error("Cannot register a disposable on itself!");return this.q.add(t)}},Xe=class{constructor(){this.b=!1,Qt(this)}get value(){return this.b?void 0:this.a}set value(t){this.b||t===this.a||(this.a?.dispose(),t&&Gt(t,this),this.a=t)}clear(){this.value=void 0}dispose(){this.b=!0,Kt(this),this.a?.dispose(),this.a=void 0}clearAndLeak(){const t=this.a;return this.a=void 0,t&&Gt(t,null),t}},z1=class{constructor(){this.a=new Map,this.b=!1,Qt(this)}dispose(){Kt(this),this.b=!0,this.clearAndDisposeAll()}clearAndDisposeAll(){if(this.a.size)try{Jt(this.a.values())}finally{this.a.clear()}}has(t){return this.a.has(t)}get size(){return this.a.size}get(t){return this.a.get(t)}set(t,e,s=!1){this.b&&console.warn(new Error("Trying to add a disposable to a DisposableMap that has already been disposed of. The added object will be leaked!").stack),s||this.a.get(t)?.dispose(),this.a.set(t,e)}deleteAndDispose(t){this.a.get(t)?.dispose(),this.a.delete(t)}deleteAndLeak(t){const e=this.a.get(t);return this.a.delete(t),e}keys(){return this.a.keys()}values(){return this.a.values()}[Symbol.iterator](){return this.a[Symbol.iterator]()}},L2=class Be{static{this.Undefined=new Be(void 0)}constructor(e){this.element=e,this.next=Be.Undefined,this.prev=Be.Undefined}},Er=globalThis.performance.now.bind(globalThis.performance),Ar=class ji{static create(e){return new ji(e)}constructor(e){this.c=e===!1?Date.now:Er,this.a=this.c(),this.b=-1}stop(){this.b=this.c()}reset(){this.a=this.c(),this.b=-1}elapsed(){return this.b!==-1?this.b-this.a:this.c()-this.a}},H1=!1,Cr=!1,R;(function(t){t.None=()=>X.None;function e(b){if(Cr){const{onDidAddListener:g}=b,m=t1.create();let w=0;b.onDidAddListener=()=>{++w===2&&(console.warn("snapshotted emitter LIKELY used public and SHOULD HAVE BEEN created with DisposableStore. snapshotted here"),m.print()),g?.()}}}function s(b,g){return d(b,()=>{},0,void 0,!0,void 0,g)}t.defer=s;function i(b){return(g,m=null,w)=>{let $=!1,C;return C=b(D=>{if(!$)return C?C.dispose():$=!0,g.call(m,D)},null,w),$&&C.dispose(),C}}t.once=i;function r(b,g){return t.once(t.filter(b,g))}t.onceIf=r;function n(b,g,m){return l((w,$=null,C)=>b(D=>w.call($,g(D)),null,C),m)}t.map=n;function o(b,g,m){return l((w,$=null,C)=>b(D=>{g(D),w.call($,D)},null,C),m)}t.forEach=o;function a(b,g,m){return l((w,$=null,C)=>b(D=>g(D)&&w.call($,D),null,C),m)}t.filter=a;function h(b){return b}t.signal=h;function c(...b){return(g,m=null,w)=>{const $=$r(...b.map(C=>C(D=>g.call(m,D))));return u($,w)}}t.any=c;function f(b,g,m,w){let $=m;return n(b,C=>($=g($,C),$),w)}t.reduce=f;function l(b,g){let m;const w={onWillAddFirstListener(){m=b($.fire,$)},onDidRemoveLastListener(){m?.dispose()}};g||e(w);const $=new I(w);return g?.add($),$.event}function u(b,g){return g instanceof Array?g.push(b):g&&g.add(b),b}function d(b,g,m=100,w=!1,$=!1,C,D){let T,B,_t,fe=0,zt;const O1={leakWarningThreshold:C,onWillAddFirstListener(){T=b(Hi=>{fe++,B=g(B,Hi),w&&!_t&&(ue.fire(B),B=void 0),zt=()=>{const Vi=B;B=void 0,_t=void 0,(!w||fe>1)&&ue.fire(Vi),fe=0},typeof m=="number"?(clearTimeout(_t),_t=setTimeout(zt,m)):_t===void 0&&(_t=0,queueMicrotask(zt))})},onWillRemoveListener(){$&&fe>0&&zt?.()},onDidRemoveLastListener(){zt=void 0,T.dispose()}};D||e(O1);const ue=new I(O1);return D?.add(ue),ue.event}t.debounce=d;function v(b,g=0,m){return t.debounce(b,(w,$)=>w?(w.push($),w):[$],g,void 0,!0,void 0,m)}t.accumulate=v;function y(b,g=(w,$)=>w===$,m){let w=!0,$;return a(b,C=>{const D=w||!g(C,$);return w=!1,$=C,D},m)}t.latch=y;function E(b,g,m){return[t.filter(b,g,m),t.filter(b,w=>!g(w),m)]}t.split=E;function O(b,g=!1,m=[],w){let $=m.slice(),C=b(B=>{$?$.push(B):T.fire(B)});w&&w.add(C);const D=()=>{$?.forEach(B=>T.fire(B)),$=null},T=new I({onWillAddFirstListener(){C||(C=b(B=>T.fire(B)),w&&w.add(C))},onDidAddFirstListener(){$&&(g?setTimeout(D):D())},onDidRemoveLastListener(){C&&C.dispose(),C=null}});return w&&w.add(T),T.event}t.buffer=O;function et(b,g){return(w,$,C)=>{const D=g(new Pt);return b(function(T){const B=D.evaluate(T);B!==j&&w.call($,B)},void 0,C)}}t.chain=et;const j=Symbol("HaltChainable");class Pt{constructor(){this.f=[]}map(g){return this.f.push(g),this}forEach(g){return this.f.push(m=>(g(m),m)),this}filter(g){return this.f.push(m=>g(m)?m:j),this}reduce(g,m){let w=m;return this.f.push($=>(w=g(w,$),w)),this}latch(g=(m,w)=>m===w){let m=!0,w;return this.f.push($=>{const C=m||!g($,w);return m=!1,w=$,C?$:j}),this}evaluate(g){for(const m of this.f)if(g=m(g),g===j)break;return g}}function p(b,g,m=w=>w){const w=(...T)=>D.fire(m(...T)),$=()=>b.on(g,w),C=()=>b.removeListener(g,w),D=new I({onWillAddFirstListener:$,onDidRemoveLastListener:C});return D.event}t.fromNodeEventEmitter=p;function x(b,g,m=w=>w){const w=(...T)=>D.fire(m(...T)),$=()=>b.addEventListener(g,w),C=()=>b.removeEventListener(g,w),D=new I({onWillAddFirstListener:$,onDidRemoveLastListener:C});return D.event}t.fromDOMEventEmitter=x;function k(b,g){return new Promise(m=>i(b)(m,null,g))}t.toPromise=k;function V(b){const g=new I;return b.then(m=>{g.fire(m)},()=>{g.fire(void 0)}).finally(()=>{g.dispose()}),g.event}t.fromPromise=V;function kt(b,g){return b(m=>g.fire(m))}t.forward=kt;function le(b,g,m){return g(m),b(w=>g(w))}t.runAndSubscribe=le;class Bi{constructor(g,m){this._observable=g,this.f=0,this.g=!1;const w={onWillAddFirstListener:()=>{g.addObserver(this),this._observable.reportChanges()},onDidRemoveLastListener:()=>{g.removeObserver(this)}};m||e(w),this.emitter=new I(w),m&&m.add(this.emitter)}beginUpdate(g){this.f++}handlePossibleChange(g){}handleChange(g,m){this.g=!0}endUpdate(g){this.f--,this.f===0&&(this._observable.reportChanges(),this.g&&(this.g=!1,this.emitter.fire(this._observable.get())))}}function qi(b,g){return new Bi(b,g).emitter.event}t.fromObservable=qi;function zi(b){return(g,m,w)=>{let $=0,C=!1;const D={beginUpdate(){$++},endUpdate(){$--,$===0&&(b.reportChanges(),C&&(C=!1,g.call(m)))},handlePossibleChange(){},handleChange(){C=!0}};b.addObserver(D),b.reportChanges();const T={dispose(){b.removeObserver(D)}};return w instanceof st?w.add(T):Array.isArray(w)&&w.push(T),T}}t.fromObservableLight=zi})(R||(R={}));var xr=class R1{static{this.all=new Set}static{this.f=0}constructor(e){this.listenerCount=0,this.invocationCount=0,this.elapsedOverall=0,this.durations=[],this.name=`${e}_${R1.f++}`,R1.all.add(this)}start(e){this.g=new Ar,this.listenerCount=e}stop(){if(this.g){const e=this.g.elapsed();this.durations.push(e),this.elapsedOverall+=e,this.invocationCount+=1,this.g=void 0}}},V1=-1,Lr=class Mi{static{this.f=1}constructor(e,s,i=(Mi.f++).toString(16).padStart(3,"0")){this.j=e,this.threshold=s,this.name=i,this.h=0}dispose(){this.g?.clear()}check(e,s){const i=this.threshold;if(i<=0||s<i)return;this.g||(this.g=new Map);const r=this.g.get(e.value)||0;if(this.g.set(e.value,r+1),this.h-=1,this.h<=0){this.h=i*.5;const[n,o]=this.getMostFrequentStack(),a=`[${this.name}] potential listener LEAK detected, having ${s} listeners already. MOST frequent listener (${o}):`;console.warn(a),console.warn(n);const h=new Sr(a,n);this.j(h)}return()=>{const n=this.g.get(e.value)||0;this.g.set(e.value,n-1)}}getMostFrequentStack(){if(!this.g)return;let e,s=0;for(const[i,r]of this.g)(!e||s<r)&&(e=[i,r],s=r);return e}},t1=class Ti{static create(){const e=new Error;return new Ti(e.stack??"")}constructor(e){this.value=e}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}},Sr=class extends Error{constructor(t,e){super(t),this.name="ListenerLeakError",this.stack=e}},Dr=class extends Error{constructor(t,e){super(t),this.name="ListenerRefusalError",this.stack=e}},Pr=0,de=class{constructor(t){this.value=t,this.id=Pr++}},kr=2,_r=(t,e)=>{if(t instanceof de)e(t);else for(let s=0;s<t.length;s++){const i=t[s];i&&e(i)}},I=class{constructor(t){this.z=0,this.f=t,this.g=V1>0||this.f?.leakWarningThreshold?new Lr(t?.onListenerError??Ht,this.f?.leakWarningThreshold??V1):void 0,this.j=this.f?._profName?new xr(this.f._profName):void 0,this.w=this.f?.deliveryQueue}dispose(){if(!this.m){if(this.m=!0,this.w?.current===this&&this.w.reset(),this.u){if(H1){const t=this.u;queueMicrotask(()=>{_r(t,e=>e.stack?.print())})}this.u=void 0,this.z=0}this.f?.onDidRemoveLastListener?.(),this.g?.dispose()}}get event(){return this.q??=(t,e,s)=>{if(this.g&&this.z>this.g.threshold**2){const a=`[${this.g.name}] REFUSES to accept new listeners because it exceeded its threshold by far (${this.z} vs ${this.g.threshold})`;console.warn(a);const h=this.g.getMostFrequentStack()??["UNKNOWN stack",-1],c=new Dr(`${a}. HINT: Stack shows most frequent listener (${h[1]}-times)`,h[0]);return(this.f?.onListenerError||Ht)(c),X.None}if(this.m)return X.None;e&&(t=t.bind(e));const i=new de(t);let r,n;this.g&&this.z>=Math.ceil(this.g.threshold*.2)&&(i.stack=t1.create(),r=this.g.check(i.stack,this.z+1)),H1&&(i.stack=n??t1.create()),this.u?this.u instanceof de?(this.w??=new Rr,this.u=[this.u,i]):this.u.push(i):(this.f?.onWillAddFirstListener?.(this),this.u=i,this.f?.onDidAddFirstListener?.(this)),this.f?.onDidAddListener?.(this),this.z++;const o=G(()=>{r?.(),this.A(i)});return s instanceof st?s.add(o):Array.isArray(s)&&s.push(o),o},this.q}A(t){if(this.f?.onWillRemoveListener?.(this),!this.u)return;if(this.z===1){this.u=void 0,this.f?.onDidRemoveLastListener?.(this),this.z=0;return}const e=this.u,s=e.indexOf(t);if(s===-1)throw console.log("disposed?",this.m),console.log("size?",this.z),console.log("arr?",JSON.stringify(this.u)),new Error("Attempted to dispose unknown listener");this.z--,e[s]=void 0;const i=this.w.current===this;if(this.z*kr<=e.length){let r=0;for(let n=0;n<e.length;n++)e[n]?e[r++]=e[n]:i&&r<this.w.end&&(this.w.end--,r<this.w.i&&this.w.i--);e.length=r}}B(t,e){if(!t)return;const s=this.f?.onListenerError||Ht;if(!s){t.value(e);return}try{t.value(e)}catch(i){s(i)}}C(t){const e=t.current.u;for(;t.i<t.end;)this.B(e[t.i++],t.value);t.reset()}fire(t){if(this.w?.current&&(this.C(this.w),this.j?.stop()),this.j?.start(this.z),this.u)if(this.u instanceof de)this.B(this.u,t);else{const e=this.w;e.enqueue(this,t,this.u.length),this.C(e)}this.j?.stop()}hasListeners(){return this.z>0}},Rr=class{constructor(){this.i=-1,this.end=0}enqueue(t,e,s){this.i=0,this.end=s,this.current=t,this.value=e}reset(){this.i=this.end,this.current=void 0,this.value=void 0}},Ir=class{constructor(){this.g=!1,this.h=[],this.f=new I({onWillAddFirstListener:()=>this.j(),onDidRemoveLastListener:()=>this.k()})}get event(){return this.f.event}add(t){const e={event:t,listener:null};return this.h.push(e),this.g&&this.m(e),G(Ye(()=>{this.g&&this.o(e);const i=this.h.indexOf(e);this.h.splice(i,1)}))}j(){this.g=!0,this.h.forEach(t=>this.m(t))}k(){this.g=!1,this.h.forEach(t=>this.o(t))}m(t){t.listener=t.event(e=>this.f.fire(e))}o(t){t.listener?.dispose(),t.listener=null}dispose(){this.f.dispose();for(const t of this.h)t.listener?.dispose();this.h=[]}},Or=class{constructor(){this.f=!1,this.g=R.None,this.h=X.None,this.j=new I({onDidAddFirstListener:()=>{this.f=!0,this.h=this.g(this.j.fire,this.j)},onDidRemoveLastListener:()=>{this.f=!1,this.h.dispose()}}),this.event=this.j.event}set input(t){this.g=t,this.f&&(this.h.dispose(),this.h=t(this.j.fire,this.j))}dispose(){this.h.dispose(),this.j.dispose()}},Q1=Object.freeze(function(t,e){const s=setTimeout(t.bind(e),0);return{dispose(){clearTimeout(s)}}}),Yt;(function(t){function e(s){return s===t.None||s===t.Cancelled||s instanceof ge?!0:!s||typeof s!="object"?!1:typeof s.isCancellationRequested=="boolean"&&typeof s.onCancellationRequested=="function"}t.isCancellationToken=e,t.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:R.None}),t.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:Q1})})(Yt||(Yt={}));var ge=class{constructor(){this.a=!1,this.b=null}cancel(){this.a||(this.a=!0,this.b&&(this.b.fire(void 0),this.dispose()))}get isCancellationRequested(){return this.a}get onCancellationRequested(){return this.a?Q1:(this.b||(this.b=new I),this.b.event)}dispose(){this.b&&(this.b.dispose(),this.b=null)}},It=class{constructor(t){this.f=void 0,this.g=void 0,this.g=t&&t.onCancellationRequested(this.cancel,this)}get token(){return this.f||(this.f=new ge),this.f}cancel(){this.f?this.f instanceof ge&&this.f.cancel():this.f=Yt.Cancelled}dispose(t=!1){t&&this.cancel(),this.g?.dispose(),this.f?this.f instanceof ge&&this.f.dispose():this.f=Yt.None}};function Nr(){return globalThis._VSCODE_NLS_MESSAGES}function K1(){return globalThis._VSCODE_NLS_LANGUAGE}var jr=K1()==="pseudo"||typeof document<"u"&&document.location&&typeof document.location.hash=="string"&&document.location.hash.indexOf("pseudo=true")>=0;function G1(t,e){let s;return e.length===0?s=t:s=t.replace(/\{(\d+)\}/g,(i,r)=>{const n=r[0],o=e[n];let a=i;return typeof o=="string"?a=o:(typeof o=="number"||typeof o=="boolean"||o===void 0||o===null)&&(a=String(o)),a}),jr&&(s="\uFF3B"+s.replace(/[aouei]/g,"$&$&")+"\uFF3D"),s}function it(t,e,...s){return G1(typeof t=="number"?Mr(t,e):e,s)}function Mr(t,e){const s=Nr()?.[t];if(typeof s!="string"){if(typeof e=="string")return e;throw new Error(`!!! NLS MISSING: ${t} !!!`)}return s}var Ot="en",pe=!1,we=!1,Zt=!1,Tr=!1,J1=!1,e1=!1,Ur=!1,Fr=!1,Wr=!1,Br=!1,ve=void 0,me=Ot,Y1=Ot,qr=void 0,ft=void 0,ut=globalThis,J=void 0;typeof ut.vscode<"u"&&typeof ut.vscode.process<"u"?J=ut.vscode.process:typeof process<"u"&&typeof process?.versions?.node=="string"&&(J=process);var Z1=typeof J?.versions?.electron=="string",zr=Z1&&J?.type==="renderer";if(typeof J=="object"){pe=J.platform==="win32",we=J.platform==="darwin",Zt=J.platform==="linux",Tr=Zt&&!!J.env.SNAP&&!!J.env.SNAP_REVISION,Ur=Z1,Wr=!!J.env.CI||!!J.env.BUILD_ARTIFACTSTAGINGDIRECTORY,ve=Ot,me=Ot;const t=J.env.VSCODE_NLS_CONFIG;if(t)try{const e=JSON.parse(t);ve=e.userLocale,Y1=e.osLocale,me=e.resolvedLanguage||Ot,qr=e.languagePack?.translationsConfigFile}catch{}J1=!0}else typeof navigator=="object"&&!zr?(ft=navigator.userAgent,pe=ft.indexOf("Windows")>=0,we=ft.indexOf("Macintosh")>=0,Fr=(ft.indexOf("Macintosh")>=0||ft.indexOf("iPad")>=0||ft.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,Zt=ft.indexOf("Linux")>=0,Br=ft?.indexOf("Mobi")>=0,e1=!0,me=K1()||Ot,ve=navigator.language.toLowerCase(),Y1=ve):console.error("Unable to resolve platform.");var X1;(function(t){t[t.Web=0]="Web",t[t.Mac=1]="Mac",t[t.Linux=2]="Linux",t[t.Windows=3]="Windows"})(X1||(X1={}));var s1=0;we?s1=1:pe?s1=3:Zt&&(s1=2);var Y=pe,Nt=we,z=Zt,Hr=J1,Vr=e1,Qr=e1&&typeof ut.importScripts=="function",Kr=Qr?ut.origin:void 0,ct=ft,bt=me,ts;(function(t){function e(){return bt}t.value=e;function s(){return bt.length===2?bt==="en":bt.length>=3?bt[0]==="e"&&bt[1]==="n"&&bt[2]==="-":!1}t.isDefaultVariant=s;function i(){return bt==="en"}t.isDefault=i})(ts||(ts={}));var Gr=typeof ut.postMessage=="function"&&!ut.importScripts,Jr=(()=>{if(Gr){const t=[];ut.addEventListener("message",s=>{if(s.data&&s.data.vscodeScheduleAsyncWork)for(let i=0,r=t.length;i<r;i++){const n=t[i];if(n.id===s.data.vscodeScheduleAsyncWork){t.splice(i,1),n.callback();return}}});let e=0;return s=>{const i=++e;t.push({id:i,callback:s}),ut.postMessage({vscodeScheduleAsyncWork:i},"*")}}return t=>setTimeout(t)})(),es;(function(t){t[t.Windows=1]="Windows",t[t.Macintosh=2]="Macintosh",t[t.Linux=3]="Linux"})(es||(es={}));var Yr=!!(ct&&ct.indexOf("Chrome")>=0),S2=!!(ct&&ct.indexOf("Firefox")>=0),D2=!!(!Yr&&ct&&ct.indexOf("Safari")>=0),P2=!!(ct&&ct.indexOf("Edg/")>=0),k2=!!(ct&&ct.indexOf("Android")>=0),Et,i1=globalThis.vscode;if(typeof i1<"u"&&typeof i1.process<"u"){const t=i1.process;Et={get platform(){return t.platform},get arch(){return t.arch},get env(){return t.env},cwd(){return t.cwd()}}}else typeof process<"u"&&typeof process?.versions?.node=="string"?Et={get platform(){return process.platform},get arch(){return process.arch},get env(){return process.env},cwd(){return process.env.VSCODE_CWD||process.cwd()}}:Et={get platform(){return Y?"win32":Nt?"darwin":"linux"},get arch(){},get env(){return{}},cwd(){return"/"}};var be=Et.cwd,Zr=Et.env,Xr=Et.platform,_2=Et.arch,tn=65,en=97,sn=90,rn=122,At=46,U=47,Q=92,dt=58,nn=63,ss=class extends Error{constructor(t,e,s){let i;typeof e=="string"&&e.indexOf("not ")===0?(i="must not be",e=e.replace(/^not /,"")):i="must be";const r=t.indexOf(".")!==-1?"property":"argument";let n=`The "${t}" ${r} ${i} of type ${e}`;n+=`. Received type ${typeof s}`,super(n),this.code="ERR_INVALID_ARG_TYPE"}};function on(t,e){if(t===null||typeof t!="object")throw new ss(e,"Object",t)}function N(t,e){if(typeof t!="string")throw new ss(e,"string",t)}var K=Xr==="win32";function A(t){return t===U||t===Q}function r1(t){return t===U}function gt(t){return t>=tn&&t<=sn||t>=en&&t<=rn}function ye(t,e,s,i){let r="",n=0,o=-1,a=0,h=0;for(let c=0;c<=t.length;++c){if(c<t.length)h=t.charCodeAt(c);else{if(i(h))break;h=U}if(i(h)){if(!(o===c-1||a===1))if(a===2){if(r.length<2||n!==2||r.charCodeAt(r.length-1)!==At||r.charCodeAt(r.length-2)!==At){if(r.length>2){const f=r.lastIndexOf(s);f===-1?(r="",n=0):(r=r.slice(0,f),n=r.length-1-r.lastIndexOf(s)),o=c,a=0;continue}else if(r.length!==0){r="",n=0,o=c,a=0;continue}}e&&(r+=r.length>0?`${s}..`:"..",n=2)}else r.length>0?r+=`${s}${t.slice(o+1,c)}`:r=t.slice(o+1,c),n=c-o-1;o=c,a=0}else h===At&&a!==-1?++a:a=-1}return r}function an(t){return t?`${t[0]==="."?"":"."}${t}`:""}function is(t,e){on(e,"pathObject");const s=e.dir||e.root,i=e.base||`${e.name||""}${an(e.ext)}`;return s?s===e.root?`${s}${i}`:`${s}${t}${i}`:i}var M={resolve(...t){let e="",s="",i=!1;for(let r=t.length-1;r>=-1;r--){let n;if(r>=0){if(n=t[r],N(n,`paths[${r}]`),n.length===0)continue}else e.length===0?n=be():(n=Zr[`=${e}`]||be(),(n===void 0||n.slice(0,2).toLowerCase()!==e.toLowerCase()&&n.charCodeAt(2)===Q)&&(n=`${e}\\`));const o=n.length;let a=0,h="",c=!1;const f=n.charCodeAt(0);if(o===1)A(f)&&(a=1,c=!0);else if(A(f))if(c=!0,A(n.charCodeAt(1))){let l=2,u=l;for(;l<o&&!A(n.charCodeAt(l));)l++;if(l<o&&l!==u){const d=n.slice(u,l);for(u=l;l<o&&A(n.charCodeAt(l));)l++;if(l<o&&l!==u){for(u=l;l<o&&!A(n.charCodeAt(l));)l++;(l===o||l!==u)&&(h=`\\\\${d}\\${n.slice(u,l)}`,a=l)}}}else a=1;else gt(f)&&n.charCodeAt(1)===dt&&(h=n.slice(0,2),a=2,o>2&&A(n.charCodeAt(2))&&(c=!0,a=3));if(h.length>0)if(e.length>0){if(h.toLowerCase()!==e.toLowerCase())continue}else e=h;if(i){if(e.length>0)break}else if(s=`${n.slice(a)}\\${s}`,i=c,c&&e.length>0)break}return s=ye(s,!i,"\\",A),i?`${e}\\${s}`:`${e}${s}`||"."},normalize(t){N(t,"path");const e=t.length;if(e===0)return".";let s=0,i,r=!1;const n=t.charCodeAt(0);if(e===1)return r1(n)?"\\":t;if(A(n))if(r=!0,A(t.charCodeAt(1))){let a=2,h=a;for(;a<e&&!A(t.charCodeAt(a));)a++;if(a<e&&a!==h){const c=t.slice(h,a);for(h=a;a<e&&A(t.charCodeAt(a));)a++;if(a<e&&a!==h){for(h=a;a<e&&!A(t.charCodeAt(a));)a++;if(a===e)return`\\\\${c}\\${t.slice(h)}\\`;a!==h&&(i=`\\\\${c}\\${t.slice(h,a)}`,s=a)}}}else s=1;else gt(n)&&t.charCodeAt(1)===dt&&(i=t.slice(0,2),s=2,e>2&&A(t.charCodeAt(2))&&(r=!0,s=3));let o=s<e?ye(t.slice(s),!r,"\\",A):"";if(o.length===0&&!r&&(o="."),o.length>0&&A(t.charCodeAt(e-1))&&(o+="\\"),!r&&i===void 0&&t.includes(":")){if(o.length>=2&&gt(o.charCodeAt(0))&&o.charCodeAt(1)===dt)return`.\\${o}`;let a=t.indexOf(":");do if(a===e-1||A(t.charCodeAt(a+1)))return`.\\${o}`;while((a=t.indexOf(":",a+1))!==-1)}return i===void 0?r?`\\${o}`:o:r?`${i}\\${o}`:`${i}${o}`},isAbsolute(t){N(t,"path");const e=t.length;if(e===0)return!1;const s=t.charCodeAt(0);return A(s)||e>2&&gt(s)&&t.charCodeAt(1)===dt&&A(t.charCodeAt(2))},join(...t){if(t.length===0)return".";let e,s;for(let n=0;n<t.length;++n){const o=t[n];N(o,"path"),o.length>0&&(e===void 0?e=s=o:e+=`\\${o}`)}if(e===void 0)return".";let i=!0,r=0;if(typeof s=="string"&&A(s.charCodeAt(0))){++r;const n=s.length;n>1&&A(s.charCodeAt(1))&&(++r,n>2&&(A(s.charCodeAt(2))?++r:i=!1))}if(i){for(;r<e.length&&A(e.charCodeAt(r));)r++;r>=2&&(e=`\\${e.slice(r)}`)}return M.normalize(e)},relative(t,e){if(N(t,"from"),N(e,"to"),t===e)return"";const s=M.resolve(t),i=M.resolve(e);if(s===i||(t=s.toLowerCase(),e=i.toLowerCase(),t===e))return"";if(s.length!==t.length||i.length!==e.length){const v=s.split("\\"),y=i.split("\\");v[v.length-1]===""&&v.pop(),y[y.length-1]===""&&y.pop();const E=v.length,O=y.length,et=E<O?E:O;let j;for(j=0;j<et&&v[j].toLowerCase()===y[j].toLowerCase();j++);return j===0?i:j===et?O>et?y.slice(j).join("\\"):E>et?"..\\".repeat(E-1-j)+"..":"":"..\\".repeat(E-j)+y.slice(j).join("\\")}let r=0;for(;r<t.length&&t.charCodeAt(r)===Q;)r++;let n=t.length;for(;n-1>r&&t.charCodeAt(n-1)===Q;)n--;const o=n-r;let a=0;for(;a<e.length&&e.charCodeAt(a)===Q;)a++;let h=e.length;for(;h-1>a&&e.charCodeAt(h-1)===Q;)h--;const c=h-a,f=o<c?o:c;let l=-1,u=0;for(;u<f;u++){const v=t.charCodeAt(r+u);if(v!==e.charCodeAt(a+u))break;v===Q&&(l=u)}if(u!==f){if(l===-1)return i}else{if(c>f){if(e.charCodeAt(a+u)===Q)return i.slice(a+u+1);if(u===2)return i.slice(a+u)}o>f&&(t.charCodeAt(r+u)===Q?l=u:u===2&&(l=3)),l===-1&&(l=0)}let d="";for(u=r+l+1;u<=n;++u)(u===n||t.charCodeAt(u)===Q)&&(d+=d.length===0?"..":"\\..");return a+=l,d.length>0?`${d}${i.slice(a,h)}`:(i.charCodeAt(a)===Q&&++a,i.slice(a,h))},toNamespacedPath(t){if(typeof t!="string"||t.length===0)return t;const e=M.resolve(t);if(e.length<=2)return t;if(e.charCodeAt(0)===Q){if(e.charCodeAt(1)===Q){const s=e.charCodeAt(2);if(s!==nn&&s!==At)return`\\\\?\\UNC\\${e.slice(2)}`}}else if(gt(e.charCodeAt(0))&&e.charCodeAt(1)===dt&&e.charCodeAt(2)===Q)return`\\\\?\\${e}`;return e},dirname(t){N(t,"path");const e=t.length;if(e===0)return".";let s=-1,i=0;const r=t.charCodeAt(0);if(e===1)return A(r)?t:".";if(A(r)){if(s=i=1,A(t.charCodeAt(1))){let a=2,h=a;for(;a<e&&!A(t.charCodeAt(a));)a++;if(a<e&&a!==h){for(h=a;a<e&&A(t.charCodeAt(a));)a++;if(a<e&&a!==h){for(h=a;a<e&&!A(t.charCodeAt(a));)a++;if(a===e)return t;a!==h&&(s=i=a+1)}}}}else gt(r)&&t.charCodeAt(1)===dt&&(s=e>2&&A(t.charCodeAt(2))?3:2,i=s);let n=-1,o=!0;for(let a=e-1;a>=i;--a)if(A(t.charCodeAt(a))){if(!o){n=a;break}}else o=!1;if(n===-1){if(s===-1)return".";n=s}return t.slice(0,n)},basename(t,e){e!==void 0&&N(e,"suffix"),N(t,"path");let s=0,i=-1,r=!0,n;if(t.length>=2&&gt(t.charCodeAt(0))&&t.charCodeAt(1)===dt&&(s=2),e!==void 0&&e.length>0&&e.length<=t.length){if(e===t)return"";let o=e.length-1,a=-1;for(n=t.length-1;n>=s;--n){const h=t.charCodeAt(n);if(A(h)){if(!r){s=n+1;break}}else a===-1&&(r=!1,a=n+1),o>=0&&(h===e.charCodeAt(o)?--o===-1&&(i=n):(o=-1,i=a))}return s===i?i=a:i===-1&&(i=t.length),t.slice(s,i)}for(n=t.length-1;n>=s;--n)if(A(t.charCodeAt(n))){if(!r){s=n+1;break}}else i===-1&&(r=!1,i=n+1);return i===-1?"":t.slice(s,i)},extname(t){N(t,"path");let e=0,s=-1,i=0,r=-1,n=!0,o=0;t.length>=2&&t.charCodeAt(1)===dt&&gt(t.charCodeAt(0))&&(e=i=2);for(let a=t.length-1;a>=e;--a){const h=t.charCodeAt(a);if(A(h)){if(!n){i=a+1;break}continue}r===-1&&(n=!1,r=a+1),h===At?s===-1?s=a:o!==1&&(o=1):s!==-1&&(o=-1)}return s===-1||r===-1||o===0||o===1&&s===r-1&&s===i+1?"":t.slice(s,r)},format:is.bind(null,"\\"),parse(t){N(t,"path");const e={root:"",dir:"",base:"",ext:"",name:""};if(t.length===0)return e;const s=t.length;let i=0,r=t.charCodeAt(0);if(s===1)return A(r)?(e.root=e.dir=t,e):(e.base=e.name=t,e);if(A(r)){if(i=1,A(t.charCodeAt(1))){let l=2,u=l;for(;l<s&&!A(t.charCodeAt(l));)l++;if(l<s&&l!==u){for(u=l;l<s&&A(t.charCodeAt(l));)l++;if(l<s&&l!==u){for(u=l;l<s&&!A(t.charCodeAt(l));)l++;l===s?i=l:l!==u&&(i=l+1)}}}}else if(gt(r)&&t.charCodeAt(1)===dt){if(s<=2)return e.root=e.dir=t,e;if(i=2,A(t.charCodeAt(2))){if(s===3)return e.root=e.dir=t,e;i=3}}i>0&&(e.root=t.slice(0,i));let n=-1,o=i,a=-1,h=!0,c=t.length-1,f=0;for(;c>=i;--c){if(r=t.charCodeAt(c),A(r)){if(!h){o=c+1;break}continue}a===-1&&(h=!1,a=c+1),r===At?n===-1?n=c:f!==1&&(f=1):n!==-1&&(f=-1)}return a!==-1&&(n===-1||f===0||f===1&&n===a-1&&n===o+1?e.base=e.name=t.slice(o,a):(e.name=t.slice(o,n),e.base=t.slice(o,a),e.ext=t.slice(n,a))),o>0&&o!==i?e.dir=t.slice(0,o-1):e.dir=e.root,e},sep:"\\",delimiter:";",win32:null,posix:null},cn=(()=>{if(K){const t=/\\/g;return()=>{const e=be().replace(t,"/");return e.slice(e.indexOf("/"))}}return()=>be()})(),S={resolve(...t){let e="",s=!1;for(let i=t.length-1;i>=0&&!s;i--){const r=t[i];N(r,`paths[${i}]`),r.length!==0&&(e=`${r}/${e}`,s=r.charCodeAt(0)===U)}if(!s){const i=cn();e=`${i}/${e}`,s=i.charCodeAt(0)===U}return e=ye(e,!s,"/",r1),s?`/${e}`:e.length>0?e:"."},normalize(t){if(N(t,"path"),t.length===0)return".";const e=t.charCodeAt(0)===U,s=t.charCodeAt(t.length-1)===U;return t=ye(t,!e,"/",r1),t.length===0?e?"/":s?"./":".":(s&&(t+="/"),e?`/${t}`:t)},isAbsolute(t){return N(t,"path"),t.length>0&&t.charCodeAt(0)===U},join(...t){if(t.length===0)return".";const e=[];for(let s=0;s<t.length;++s){const i=t[s];N(i,"path"),i.length>0&&e.push(i)}return e.length===0?".":S.normalize(e.join("/"))},relative(t,e){if(N(t,"from"),N(e,"to"),t===e||(t=S.resolve(t),e=S.resolve(e),t===e))return"";const s=1,i=t.length,r=i-s,n=1,o=e.length-n,a=r<o?r:o;let h=-1,c=0;for(;c<a;c++){const l=t.charCodeAt(s+c);if(l!==e.charCodeAt(n+c))break;l===U&&(h=c)}if(c===a)if(o>a){if(e.charCodeAt(n+c)===U)return e.slice(n+c+1);if(c===0)return e.slice(n+c)}else r>a&&(t.charCodeAt(s+c)===U?h=c:c===0&&(h=0));let f="";for(c=s+h+1;c<=i;++c)(c===i||t.charCodeAt(c)===U)&&(f+=f.length===0?"..":"/..");return`${f}${e.slice(n+h)}`},toNamespacedPath(t){return t},dirname(t){if(N(t,"path"),t.length===0)return".";const e=t.charCodeAt(0)===U;let s=-1,i=!0;for(let r=t.length-1;r>=1;--r)if(t.charCodeAt(r)===U){if(!i){s=r;break}}else i=!1;return s===-1?e?"/":".":e&&s===1?"//":t.slice(0,s)},basename(t,e){e!==void 0&&N(e,"suffix"),N(t,"path");let s=0,i=-1,r=!0,n;if(e!==void 0&&e.length>0&&e.length<=t.length){if(e===t)return"";let o=e.length-1,a=-1;for(n=t.length-1;n>=0;--n){const h=t.charCodeAt(n);if(h===U){if(!r){s=n+1;break}}else a===-1&&(r=!1,a=n+1),o>=0&&(h===e.charCodeAt(o)?--o===-1&&(i=n):(o=-1,i=a))}return s===i?i=a:i===-1&&(i=t.length),t.slice(s,i)}for(n=t.length-1;n>=0;--n)if(t.charCodeAt(n)===U){if(!r){s=n+1;break}}else i===-1&&(r=!1,i=n+1);return i===-1?"":t.slice(s,i)},extname(t){N(t,"path");let e=-1,s=0,i=-1,r=!0,n=0;for(let o=t.length-1;o>=0;--o){const a=t[o];if(a==="/"){if(!r){s=o+1;break}continue}i===-1&&(r=!1,i=o+1),a==="."?e===-1?e=o:n!==1&&(n=1):e!==-1&&(n=-1)}return e===-1||i===-1||n===0||n===1&&e===i-1&&e===s+1?"":t.slice(e,i)},format:is.bind(null,"/"),parse(t){N(t,"path");const e={root:"",dir:"",base:"",ext:"",name:""};if(t.length===0)return e;const s=t.charCodeAt(0)===U;let i;s?(e.root="/",i=1):i=0;let r=-1,n=0,o=-1,a=!0,h=t.length-1,c=0;for(;h>=i;--h){const f=t.charCodeAt(h);if(f===U){if(!a){n=h+1;break}continue}o===-1&&(a=!1,o=h+1),f===At?r===-1?r=h:c!==1&&(c=1):r!==-1&&(c=-1)}if(o!==-1){const f=n===0&&s?1:n;r===-1||c===0||c===1&&r===o-1&&r===n+1?e.base=e.name=t.slice(f,o):(e.name=t.slice(f,r),e.base=t.slice(f,o),e.ext=t.slice(r,o))}return n>0?e.dir=t.slice(0,n-1):s&&(e.dir="/"),e},sep:"/",delimiter:":",win32:null,posix:null};S.win32=M.win32=M,S.posix=M.posix=S;var $e=K?M.normalize:S.normalize,hn=K?M.isAbsolute:S.isAbsolute,rt=K?M.join:S.join,ln=K?M.resolve:S.resolve,fn=K?M.relative:S.relative,n1=K?M.dirname:S.dirname,Ee=K?M.basename:S.basename,un=K?M.extname:S.extname,R2=K?M.format:S.format,I2=K?M.parse:S.parse,O2=K?M.toNamespacedPath:S.toNamespacedPath,nt=K?M.sep:S.sep,N2=K?M.delimiter:S.delimiter;function dn(t){return t}var gn=class{constructor(t,e){this.a=void 0,this.b=void 0,typeof t=="function"?(this.c=t,this.d=dn):(this.c=e,this.d=t.getCacheKey)}get(t){const e=this.d(t);return this.b!==e&&(this.b=e,this.a=this.c(t)),this.a}},Ct=class{constructor(t){this.d=t,this.a=!1}get hasValue(){return this.a}get value(){if(!this.a)try{this.b=this.d()}catch(t){this.c=t}finally{this.a=!0}if(this.c)throw this.c;return this.b}get rawValue(){return this.b}};function rs(t){return t.replace(/[\\\{\}\*\+\?\|\^\$\.\[\]\(\)]/g,"\\$&")}function pn(t,e){if(!t||!e)return t;const s=e.length;if(s===0||t.length===0)return t;let i=0;for(;t.indexOf(e,i)===i;)i=i+s;return t.substring(i)}function wn(t,e){if(!t||!e)return t;const s=e.length,i=t.length;if(s===0||i===0)return t;let r=i,n=-1;for(;n=t.lastIndexOf(e,r-1),!(n===-1||n+s!==r);){if(n===0)return"";r=n}return t.substring(0,r)}function o1(t,e){return t<e?-1:t>e?1:0}function a1(t,e,s=0,i=t.length,r=0,n=e.length){for(;s<i&&r<n;s++,r++){const h=t.charCodeAt(s),c=e.charCodeAt(r);if(h<c)return-1;if(h>c)return 1}const o=i-s,a=n-r;return o<a?-1:o>a?1:0}function ns(t,e){return Xt(t,e,0,t.length,0,e.length)}function Xt(t,e,s=0,i=t.length,r=0,n=e.length){for(;s<i&&r<n;s++,r++){let h=t.charCodeAt(s),c=e.charCodeAt(r);if(h===c)continue;if(h>=128||c>=128)return a1(t.toLowerCase(),e.toLowerCase(),s,i,r,n);os(h)&&(h-=32),os(c)&&(c-=32);const f=h-c;if(f!==0)return f}const o=i-s,a=n-r;return o<a?-1:o>a?1:0}function os(t){return t>=97&&t<=122}function as(t){return t>=65&&t<=90}function cs(t,e){return t.length===e.length&&Xt(t,e)===0}function hs(t,e){const s=e.length;return e.length>t.length?!1:Xt(t,e,0,s)===0}function vn(t){return 55296<=t&&t<=56319}function ls(t){return 56320<=t&&t<=57343}function mn(t,e){return(t-55296<<10)+(e-56320)+65536}var bn=/(?:\x1b\[|\x9b)[=?>!]?[\d;:]*["$#'* ]?[a-zA-Z@^`{}|~]/,yn=/(?:\x1b\]|\x9d).*?(?:\x1b\\|\x07|\x9c)/,$n=/\x1b(?:[ #%\(\)\*\+\-\.\/]?[a-zA-Z0-9\|}~@])/,j2=new RegExp("(?:"+[bn.source,yn.source,$n.source].join("|")+")","g"),M2="\uFEFF",fs;(function(t){t[t.Other=0]="Other",t[t.Prepend=1]="Prepend",t[t.CR=2]="CR",t[t.LF=3]="LF",t[t.Control=4]="Control",t[t.Extend=5]="Extend",t[t.Regional_Indicator=6]="Regional_Indicator",t[t.SpacingMark=7]="SpacingMark",t[t.L=8]="L",t[t.V=9]="V",t[t.T=10]="T",t[t.LV=11]="LV",t[t.LVT=12]="LVT",t[t.ZWJ=13]="ZWJ",t[t.Extended_Pictographic=14]="Extended_Pictographic"})(fs||(fs={}));var T2=class oe{static{this.c=null}static getInstance(){return oe.c||(oe.c=new oe),oe.c}constructor(){this.d=En()}getGraphemeBreakType(e){if(e<32)return e===10?3:e===13?2:4;if(e<127)return 0;const s=this.d,i=s.length/3;let r=1;for(;r<=i;)if(e<s[3*r])r=2*r;else if(e>s[3*r+1])r=2*r+1;else return s[3*r+2];return 0}};function En(){return JSON.parse("[0,0,0,51229,51255,12,44061,44087,12,127462,127487,6,7083,7085,5,47645,47671,12,54813,54839,12,128678,128678,14,3270,3270,5,9919,9923,14,45853,45879,12,49437,49463,12,53021,53047,12,71216,71218,7,128398,128399,14,129360,129374,14,2519,2519,5,4448,4519,9,9742,9742,14,12336,12336,14,44957,44983,12,46749,46775,12,48541,48567,12,50333,50359,12,52125,52151,12,53917,53943,12,69888,69890,5,73018,73018,5,127990,127990,14,128558,128559,14,128759,128760,14,129653,129655,14,2027,2035,5,2891,2892,7,3761,3761,5,6683,6683,5,8293,8293,4,9825,9826,14,9999,9999,14,43452,43453,5,44509,44535,12,45405,45431,12,46301,46327,12,47197,47223,12,48093,48119,12,48989,49015,12,49885,49911,12,50781,50807,12,51677,51703,12,52573,52599,12,53469,53495,12,54365,54391,12,65279,65279,4,70471,70472,7,72145,72147,7,119173,119179,5,127799,127818,14,128240,128244,14,128512,128512,14,128652,128652,14,128721,128722,14,129292,129292,14,129445,129450,14,129734,129743,14,1476,1477,5,2366,2368,7,2750,2752,7,3076,3076,5,3415,3415,5,4141,4144,5,6109,6109,5,6964,6964,5,7394,7400,5,9197,9198,14,9770,9770,14,9877,9877,14,9968,9969,14,10084,10084,14,43052,43052,5,43713,43713,5,44285,44311,12,44733,44759,12,45181,45207,12,45629,45655,12,46077,46103,12,46525,46551,12,46973,46999,12,47421,47447,12,47869,47895,12,48317,48343,12,48765,48791,12,49213,49239,12,49661,49687,12,50109,50135,12,50557,50583,12,51005,51031,12,51453,51479,12,51901,51927,12,52349,52375,12,52797,52823,12,53245,53271,12,53693,53719,12,54141,54167,12,54589,54615,12,55037,55063,12,69506,69509,5,70191,70193,5,70841,70841,7,71463,71467,5,72330,72342,5,94031,94031,5,123628,123631,5,127763,127765,14,127941,127941,14,128043,128062,14,128302,128317,14,128465,128467,14,128539,128539,14,128640,128640,14,128662,128662,14,128703,128703,14,128745,128745,14,129004,129007,14,129329,129330,14,129402,129402,14,129483,129483,14,129686,129704,14,130048,131069,14,173,173,4,1757,1757,1,2200,2207,5,2434,2435,7,2631,2632,5,2817,2817,5,3008,3008,5,3201,3201,5,3387,3388,5,3542,3542,5,3902,3903,7,4190,4192,5,6002,6003,5,6439,6440,5,6765,6770,7,7019,7027,5,7154,7155,7,8205,8205,13,8505,8505,14,9654,9654,14,9757,9757,14,9792,9792,14,9852,9853,14,9890,9894,14,9937,9937,14,9981,9981,14,10035,10036,14,11035,11036,14,42654,42655,5,43346,43347,7,43587,43587,5,44006,44007,7,44173,44199,12,44397,44423,12,44621,44647,12,44845,44871,12,45069,45095,12,45293,45319,12,45517,45543,12,45741,45767,12,45965,45991,12,46189,46215,12,46413,46439,12,46637,46663,12,46861,46887,12,47085,47111,12,47309,47335,12,47533,47559,12,47757,47783,12,47981,48007,12,48205,48231,12,48429,48455,12,48653,48679,12,48877,48903,12,49101,49127,12,49325,49351,12,49549,49575,12,49773,49799,12,49997,50023,12,50221,50247,12,50445,50471,12,50669,50695,12,50893,50919,12,51117,51143,12,51341,51367,12,51565,51591,12,51789,51815,12,52013,52039,12,52237,52263,12,52461,52487,12,52685,52711,12,52909,52935,12,53133,53159,12,53357,53383,12,53581,53607,12,53805,53831,12,54029,54055,12,54253,54279,12,54477,54503,12,54701,54727,12,54925,54951,12,55149,55175,12,68101,68102,5,69762,69762,7,70067,70069,7,70371,70378,5,70720,70721,7,71087,71087,5,71341,71341,5,71995,71996,5,72249,72249,7,72850,72871,5,73109,73109,5,118576,118598,5,121505,121519,5,127245,127247,14,127568,127569,14,127777,127777,14,127872,127891,14,127956,127967,14,128015,128016,14,128110,128172,14,128259,128259,14,128367,128368,14,128424,128424,14,128488,128488,14,128530,128532,14,128550,128551,14,128566,128566,14,128647,128647,14,128656,128656,14,128667,128673,14,128691,128693,14,128715,128715,14,128728,128732,14,128752,128752,14,128765,128767,14,129096,129103,14,129311,129311,14,129344,129349,14,129394,129394,14,129413,129425,14,129466,129471,14,129511,129535,14,129664,129666,14,129719,129722,14,129760,129767,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2307,2307,7,2382,2383,7,2497,2500,5,2563,2563,7,2677,2677,5,2763,2764,7,2879,2879,5,2914,2915,5,3021,3021,5,3142,3144,5,3263,3263,5,3285,3286,5,3398,3400,7,3530,3530,5,3633,3633,5,3864,3865,5,3974,3975,5,4155,4156,7,4229,4230,5,5909,5909,7,6078,6085,7,6277,6278,5,6451,6456,7,6744,6750,5,6846,6846,5,6972,6972,5,7074,7077,5,7146,7148,7,7222,7223,5,7416,7417,5,8234,8238,4,8417,8417,5,9000,9000,14,9203,9203,14,9730,9731,14,9748,9749,14,9762,9763,14,9776,9783,14,9800,9811,14,9831,9831,14,9872,9873,14,9882,9882,14,9900,9903,14,9929,9933,14,9941,9960,14,9974,9974,14,9989,9989,14,10006,10006,14,10062,10062,14,10160,10160,14,11647,11647,5,12953,12953,14,43019,43019,5,43232,43249,5,43443,43443,5,43567,43568,7,43696,43696,5,43765,43765,7,44013,44013,5,44117,44143,12,44229,44255,12,44341,44367,12,44453,44479,12,44565,44591,12,44677,44703,12,44789,44815,12,44901,44927,12,45013,45039,12,45125,45151,12,45237,45263,12,45349,45375,12,45461,45487,12,45573,45599,12,45685,45711,12,45797,45823,12,45909,45935,12,46021,46047,12,46133,46159,12,46245,46271,12,46357,46383,12,46469,46495,12,46581,46607,12,46693,46719,12,46805,46831,12,46917,46943,12,47029,47055,12,47141,47167,12,47253,47279,12,47365,47391,12,47477,47503,12,47589,47615,12,47701,47727,12,47813,47839,12,47925,47951,12,48037,48063,12,48149,48175,12,48261,48287,12,48373,48399,12,48485,48511,12,48597,48623,12,48709,48735,12,48821,48847,12,48933,48959,12,49045,49071,12,49157,49183,12,49269,49295,12,49381,49407,12,49493,49519,12,49605,49631,12,49717,49743,12,49829,49855,12,49941,49967,12,50053,50079,12,50165,50191,12,50277,50303,12,50389,50415,12,50501,50527,12,50613,50639,12,50725,50751,12,50837,50863,12,50949,50975,12,51061,51087,12,51173,51199,12,51285,51311,12,51397,51423,12,51509,51535,12,51621,51647,12,51733,51759,12,51845,51871,12,51957,51983,12,52069,52095,12,52181,52207,12,52293,52319,12,52405,52431,12,52517,52543,12,52629,52655,12,52741,52767,12,52853,52879,12,52965,52991,12,53077,53103,12,53189,53215,12,53301,53327,12,53413,53439,12,53525,53551,12,53637,53663,12,53749,53775,12,53861,53887,12,53973,53999,12,54085,54111,12,54197,54223,12,54309,54335,12,54421,54447,12,54533,54559,12,54645,54671,12,54757,54783,12,54869,54895,12,54981,55007,12,55093,55119,12,55243,55291,10,66045,66045,5,68325,68326,5,69688,69702,5,69817,69818,5,69957,69958,7,70089,70092,5,70198,70199,5,70462,70462,5,70502,70508,5,70750,70750,5,70846,70846,7,71100,71101,5,71230,71230,7,71351,71351,5,71737,71738,5,72000,72000,7,72160,72160,5,72273,72278,5,72752,72758,5,72882,72883,5,73031,73031,5,73461,73462,7,94192,94193,7,119149,119149,7,121403,121452,5,122915,122916,5,126980,126980,14,127358,127359,14,127535,127535,14,127759,127759,14,127771,127771,14,127792,127793,14,127825,127867,14,127897,127899,14,127945,127945,14,127985,127986,14,128000,128007,14,128021,128021,14,128066,128100,14,128184,128235,14,128249,128252,14,128266,128276,14,128335,128335,14,128379,128390,14,128407,128419,14,128444,128444,14,128481,128481,14,128499,128499,14,128526,128526,14,128536,128536,14,128543,128543,14,128556,128556,14,128564,128564,14,128577,128580,14,128643,128645,14,128649,128649,14,128654,128654,14,128660,128660,14,128664,128664,14,128675,128675,14,128686,128689,14,128695,128696,14,128705,128709,14,128717,128719,14,128725,128725,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129009,129023,14,129160,129167,14,129296,129304,14,129320,129327,14,129340,129342,14,129356,129356,14,129388,129392,14,129399,129400,14,129404,129407,14,129432,129442,14,129454,129455,14,129473,129474,14,129485,129487,14,129648,129651,14,129659,129660,14,129671,129679,14,129709,129711,14,129728,129730,14,129751,129753,14,129776,129782,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2274,2274,1,2363,2363,7,2377,2380,7,2402,2403,5,2494,2494,5,2507,2508,7,2558,2558,5,2622,2624,7,2641,2641,5,2691,2691,7,2759,2760,5,2786,2787,5,2876,2876,5,2881,2884,5,2901,2902,5,3006,3006,5,3014,3016,7,3072,3072,5,3134,3136,5,3157,3158,5,3260,3260,5,3266,3266,5,3274,3275,7,3328,3329,5,3391,3392,7,3405,3405,5,3457,3457,5,3536,3537,7,3551,3551,5,3636,3642,5,3764,3772,5,3895,3895,5,3967,3967,7,3993,4028,5,4146,4151,5,4182,4183,7,4226,4226,5,4253,4253,5,4957,4959,5,5940,5940,7,6070,6070,7,6087,6088,7,6158,6158,4,6432,6434,5,6448,6449,7,6679,6680,5,6742,6742,5,6754,6754,5,6783,6783,5,6912,6915,5,6966,6970,5,6978,6978,5,7042,7042,7,7080,7081,5,7143,7143,7,7150,7150,7,7212,7219,5,7380,7392,5,7412,7412,5,8203,8203,4,8232,8232,4,8265,8265,14,8400,8412,5,8421,8432,5,8617,8618,14,9167,9167,14,9200,9200,14,9410,9410,14,9723,9726,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9774,14,9786,9786,14,9794,9794,14,9823,9823,14,9828,9828,14,9833,9850,14,9855,9855,14,9875,9875,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9935,9935,14,9939,9939,14,9962,9962,14,9972,9972,14,9978,9978,14,9986,9986,14,9997,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10133,10135,14,10548,10549,14,11093,11093,14,12330,12333,5,12441,12442,5,42608,42610,5,43010,43010,5,43045,43046,5,43188,43203,7,43302,43309,5,43392,43394,5,43446,43449,5,43493,43493,5,43571,43572,7,43597,43597,7,43703,43704,5,43756,43757,5,44003,44004,7,44009,44010,7,44033,44059,12,44089,44115,12,44145,44171,12,44201,44227,12,44257,44283,12,44313,44339,12,44369,44395,12,44425,44451,12,44481,44507,12,44537,44563,12,44593,44619,12,44649,44675,12,44705,44731,12,44761,44787,12,44817,44843,12,44873,44899,12,44929,44955,12,44985,45011,12,45041,45067,12,45097,45123,12,45153,45179,12,45209,45235,12,45265,45291,12,45321,45347,12,45377,45403,12,45433,45459,12,45489,45515,12,45545,45571,12,45601,45627,12,45657,45683,12,45713,45739,12,45769,45795,12,45825,45851,12,45881,45907,12,45937,45963,12,45993,46019,12,46049,46075,12,46105,46131,12,46161,46187,12,46217,46243,12,46273,46299,12,46329,46355,12,46385,46411,12,46441,46467,12,46497,46523,12,46553,46579,12,46609,46635,12,46665,46691,12,46721,46747,12,46777,46803,12,46833,46859,12,46889,46915,12,46945,46971,12,47001,47027,12,47057,47083,12,47113,47139,12,47169,47195,12,47225,47251,12,47281,47307,12,47337,47363,12,47393,47419,12,47449,47475,12,47505,47531,12,47561,47587,12,47617,47643,12,47673,47699,12,47729,47755,12,47785,47811,12,47841,47867,12,47897,47923,12,47953,47979,12,48009,48035,12,48065,48091,12,48121,48147,12,48177,48203,12,48233,48259,12,48289,48315,12,48345,48371,12,48401,48427,12,48457,48483,12,48513,48539,12,48569,48595,12,48625,48651,12,48681,48707,12,48737,48763,12,48793,48819,12,48849,48875,12,48905,48931,12,48961,48987,12,49017,49043,12,49073,49099,12,49129,49155,12,49185,49211,12,49241,49267,12,49297,49323,12,49353,49379,12,49409,49435,12,49465,49491,12,49521,49547,12,49577,49603,12,49633,49659,12,49689,49715,12,49745,49771,12,49801,49827,12,49857,49883,12,49913,49939,12,49969,49995,12,50025,50051,12,50081,50107,12,50137,50163,12,50193,50219,12,50249,50275,12,50305,50331,12,50361,50387,12,50417,50443,12,50473,50499,12,50529,50555,12,50585,50611,12,50641,50667,12,50697,50723,12,50753,50779,12,50809,50835,12,50865,50891,12,50921,50947,12,50977,51003,12,51033,51059,12,51089,51115,12,51145,51171,12,51201,51227,12,51257,51283,12,51313,51339,12,51369,51395,12,51425,51451,12,51481,51507,12,51537,51563,12,51593,51619,12,51649,51675,12,51705,51731,12,51761,51787,12,51817,51843,12,51873,51899,12,51929,51955,12,51985,52011,12,52041,52067,12,52097,52123,12,52153,52179,12,52209,52235,12,52265,52291,12,52321,52347,12,52377,52403,12,52433,52459,12,52489,52515,12,52545,52571,12,52601,52627,12,52657,52683,12,52713,52739,12,52769,52795,12,52825,52851,12,52881,52907,12,52937,52963,12,52993,53019,12,53049,53075,12,53105,53131,12,53161,53187,12,53217,53243,12,53273,53299,12,53329,53355,12,53385,53411,12,53441,53467,12,53497,53523,12,53553,53579,12,53609,53635,12,53665,53691,12,53721,53747,12,53777,53803,12,53833,53859,12,53889,53915,12,53945,53971,12,54001,54027,12,54057,54083,12,54113,54139,12,54169,54195,12,54225,54251,12,54281,54307,12,54337,54363,12,54393,54419,12,54449,54475,12,54505,54531,12,54561,54587,12,54617,54643,12,54673,54699,12,54729,54755,12,54785,54811,12,54841,54867,12,54897,54923,12,54953,54979,12,55009,55035,12,55065,55091,12,55121,55147,12,55177,55203,12,65024,65039,5,65520,65528,4,66422,66426,5,68152,68154,5,69291,69292,5,69633,69633,5,69747,69748,5,69811,69814,5,69826,69826,5,69932,69932,7,70016,70017,5,70079,70080,7,70095,70095,5,70196,70196,5,70367,70367,5,70402,70403,7,70464,70464,5,70487,70487,5,70709,70711,7,70725,70725,7,70833,70834,7,70843,70844,7,70849,70849,7,71090,71093,5,71103,71104,5,71227,71228,7,71339,71339,5,71344,71349,5,71458,71461,5,71727,71735,5,71985,71989,7,71998,71998,5,72002,72002,7,72154,72155,5,72193,72202,5,72251,72254,5,72281,72283,5,72344,72345,5,72766,72766,7,72874,72880,5,72885,72886,5,73023,73029,5,73104,73105,5,73111,73111,5,92912,92916,5,94095,94098,5,113824,113827,4,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,125252,125258,5,127183,127183,14,127340,127343,14,127377,127386,14,127491,127503,14,127548,127551,14,127744,127756,14,127761,127761,14,127769,127769,14,127773,127774,14,127780,127788,14,127796,127797,14,127820,127823,14,127869,127869,14,127894,127895,14,127902,127903,14,127943,127943,14,127947,127950,14,127972,127972,14,127988,127988,14,127992,127994,14,128009,128011,14,128019,128019,14,128023,128041,14,128064,128064,14,128102,128107,14,128174,128181,14,128238,128238,14,128246,128247,14,128254,128254,14,128264,128264,14,128278,128299,14,128329,128330,14,128348,128359,14,128371,128377,14,128392,128393,14,128401,128404,14,128421,128421,14,128433,128434,14,128450,128452,14,128476,128478,14,128483,128483,14,128495,128495,14,128506,128506,14,128519,128520,14,128528,128528,14,128534,128534,14,128538,128538,14,128540,128542,14,128544,128549,14,128552,128555,14,128557,128557,14,128560,128563,14,128565,128565,14,128567,128576,14,128581,128591,14,128641,128642,14,128646,128646,14,128648,128648,14,128650,128651,14,128653,128653,14,128655,128655,14,128657,128659,14,128661,128661,14,128663,128663,14,128665,128666,14,128674,128674,14,128676,128677,14,128679,128685,14,128690,128690,14,128694,128694,14,128697,128702,14,128704,128704,14,128710,128714,14,128716,128716,14,128720,128720,14,128723,128724,14,128726,128727,14,128733,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129008,129008,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129661,129663,14,129667,129670,14,129680,129685,14,129705,129708,14,129712,129718,14,129723,129727,14,129731,129733,14,129744,129750,14,129754,129759,14,129768,129775,14,129783,129791,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2192,2193,1,2250,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3132,3132,5,3137,3140,7,3146,3149,5,3170,3171,5,3202,3203,7,3262,3262,7,3264,3265,7,3267,3268,7,3271,3272,7,3276,3277,5,3298,3299,5,3330,3331,7,3390,3390,5,3393,3396,5,3402,3404,7,3406,3406,1,3426,3427,5,3458,3459,7,3535,3535,5,3538,3540,5,3544,3550,7,3570,3571,7,3635,3635,7,3655,3662,5,3763,3763,7,3784,3789,5,3893,3893,5,3897,3897,5,3953,3966,5,3968,3972,5,3981,3991,5,4038,4038,5,4145,4145,7,4153,4154,5,4157,4158,5,4184,4185,5,4209,4212,5,4228,4228,7,4237,4237,5,4352,4447,8,4520,4607,10,5906,5908,5,5938,5939,5,5970,5971,5,6068,6069,5,6071,6077,5,6086,6086,5,6089,6099,5,6155,6157,5,6159,6159,5,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6862,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7679,5,8204,8204,5,8206,8207,4,8233,8233,4,8252,8252,14,8288,8292,4,8294,8303,4,8413,8416,5,8418,8420,5,8482,8482,14,8596,8601,14,8986,8987,14,9096,9096,14,9193,9196,14,9199,9199,14,9201,9202,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9729,14,9732,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9775,9775,14,9784,9785,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9874,14,9876,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9934,14,9936,9936,14,9938,9938,14,9940,9940,14,9961,9961,14,9963,9967,14,9970,9971,14,9973,9973,14,9975,9977,14,9979,9980,14,9982,9985,14,9987,9988,14,9992,9996,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10083,14,10085,10087,14,10145,10145,14,10175,10175,14,11013,11015,14,11088,11088,14,11503,11505,5,11744,11775,5,12334,12335,5,12349,12349,14,12951,12951,14,42607,42607,5,42612,42621,5,42736,42737,5,43014,43014,5,43043,43044,7,43047,43047,7,43136,43137,7,43204,43205,5,43263,43263,5,43335,43345,5,43360,43388,8,43395,43395,7,43444,43445,7,43450,43451,7,43454,43456,7,43561,43566,5,43569,43570,5,43573,43574,5,43596,43596,5,43644,43644,5,43698,43700,5,43710,43711,5,43755,43755,7,43758,43759,7,43766,43766,5,44005,44005,5,44008,44008,5,44012,44012,7,44032,44032,11,44060,44060,11,44088,44088,11,44116,44116,11,44144,44144,11,44172,44172,11,44200,44200,11,44228,44228,11,44256,44256,11,44284,44284,11,44312,44312,11,44340,44340,11,44368,44368,11,44396,44396,11,44424,44424,11,44452,44452,11,44480,44480,11,44508,44508,11,44536,44536,11,44564,44564,11,44592,44592,11,44620,44620,11,44648,44648,11,44676,44676,11,44704,44704,11,44732,44732,11,44760,44760,11,44788,44788,11,44816,44816,11,44844,44844,11,44872,44872,11,44900,44900,11,44928,44928,11,44956,44956,11,44984,44984,11,45012,45012,11,45040,45040,11,45068,45068,11,45096,45096,11,45124,45124,11,45152,45152,11,45180,45180,11,45208,45208,11,45236,45236,11,45264,45264,11,45292,45292,11,45320,45320,11,45348,45348,11,45376,45376,11,45404,45404,11,45432,45432,11,45460,45460,11,45488,45488,11,45516,45516,11,45544,45544,11,45572,45572,11,45600,45600,11,45628,45628,11,45656,45656,11,45684,45684,11,45712,45712,11,45740,45740,11,45768,45768,11,45796,45796,11,45824,45824,11,45852,45852,11,45880,45880,11,45908,45908,11,45936,45936,11,45964,45964,11,45992,45992,11,46020,46020,11,46048,46048,11,46076,46076,11,46104,46104,11,46132,46132,11,46160,46160,11,46188,46188,11,46216,46216,11,46244,46244,11,46272,46272,11,46300,46300,11,46328,46328,11,46356,46356,11,46384,46384,11,46412,46412,11,46440,46440,11,46468,46468,11,46496,46496,11,46524,46524,11,46552,46552,11,46580,46580,11,46608,46608,11,46636,46636,11,46664,46664,11,46692,46692,11,46720,46720,11,46748,46748,11,46776,46776,11,46804,46804,11,46832,46832,11,46860,46860,11,46888,46888,11,46916,46916,11,46944,46944,11,46972,46972,11,47000,47000,11,47028,47028,11,47056,47056,11,47084,47084,11,47112,47112,11,47140,47140,11,47168,47168,11,47196,47196,11,47224,47224,11,47252,47252,11,47280,47280,11,47308,47308,11,47336,47336,11,47364,47364,11,47392,47392,11,47420,47420,11,47448,47448,11,47476,47476,11,47504,47504,11,47532,47532,11,47560,47560,11,47588,47588,11,47616,47616,11,47644,47644,11,47672,47672,11,47700,47700,11,47728,47728,11,47756,47756,11,47784,47784,11,47812,47812,11,47840,47840,11,47868,47868,11,47896,47896,11,47924,47924,11,47952,47952,11,47980,47980,11,48008,48008,11,48036,48036,11,48064,48064,11,48092,48092,11,48120,48120,11,48148,48148,11,48176,48176,11,48204,48204,11,48232,48232,11,48260,48260,11,48288,48288,11,48316,48316,11,48344,48344,11,48372,48372,11,48400,48400,11,48428,48428,11,48456,48456,11,48484,48484,11,48512,48512,11,48540,48540,11,48568,48568,11,48596,48596,11,48624,48624,11,48652,48652,11,48680,48680,11,48708,48708,11,48736,48736,11,48764,48764,11,48792,48792,11,48820,48820,11,48848,48848,11,48876,48876,11,48904,48904,11,48932,48932,11,48960,48960,11,48988,48988,11,49016,49016,11,49044,49044,11,49072,49072,11,49100,49100,11,49128,49128,11,49156,49156,11,49184,49184,11,49212,49212,11,49240,49240,11,49268,49268,11,49296,49296,11,49324,49324,11,49352,49352,11,49380,49380,11,49408,49408,11,49436,49436,11,49464,49464,11,49492,49492,11,49520,49520,11,49548,49548,11,49576,49576,11,49604,49604,11,49632,49632,11,49660,49660,11,49688,49688,11,49716,49716,11,49744,49744,11,49772,49772,11,49800,49800,11,49828,49828,11,49856,49856,11,49884,49884,11,49912,49912,11,49940,49940,11,49968,49968,11,49996,49996,11,50024,50024,11,50052,50052,11,50080,50080,11,50108,50108,11,50136,50136,11,50164,50164,11,50192,50192,11,50220,50220,11,50248,50248,11,50276,50276,11,50304,50304,11,50332,50332,11,50360,50360,11,50388,50388,11,50416,50416,11,50444,50444,11,50472,50472,11,50500,50500,11,50528,50528,11,50556,50556,11,50584,50584,11,50612,50612,11,50640,50640,11,50668,50668,11,50696,50696,11,50724,50724,11,50752,50752,11,50780,50780,11,50808,50808,11,50836,50836,11,50864,50864,11,50892,50892,11,50920,50920,11,50948,50948,11,50976,50976,11,51004,51004,11,51032,51032,11,51060,51060,11,51088,51088,11,51116,51116,11,51144,51144,11,51172,51172,11,51200,51200,11,51228,51228,11,51256,51256,11,51284,51284,11,51312,51312,11,51340,51340,11,51368,51368,11,51396,51396,11,51424,51424,11,51452,51452,11,51480,51480,11,51508,51508,11,51536,51536,11,51564,51564,11,51592,51592,11,51620,51620,11,51648,51648,11,51676,51676,11,51704,51704,11,51732,51732,11,51760,51760,11,51788,51788,11,51816,51816,11,51844,51844,11,51872,51872,11,51900,51900,11,51928,51928,11,51956,51956,11,51984,51984,11,52012,52012,11,52040,52040,11,52068,52068,11,52096,52096,11,52124,52124,11,52152,52152,11,52180,52180,11,52208,52208,11,52236,52236,11,52264,52264,11,52292,52292,11,52320,52320,11,52348,52348,11,52376,52376,11,52404,52404,11,52432,52432,11,52460,52460,11,52488,52488,11,52516,52516,11,52544,52544,11,52572,52572,11,52600,52600,11,52628,52628,11,52656,52656,11,52684,52684,11,52712,52712,11,52740,52740,11,52768,52768,11,52796,52796,11,52824,52824,11,52852,52852,11,52880,52880,11,52908,52908,11,52936,52936,11,52964,52964,11,52992,52992,11,53020,53020,11,53048,53048,11,53076,53076,11,53104,53104,11,53132,53132,11,53160,53160,11,53188,53188,11,53216,53216,11,53244,53244,11,53272,53272,11,53300,53300,11,53328,53328,11,53356,53356,11,53384,53384,11,53412,53412,11,53440,53440,11,53468,53468,11,53496,53496,11,53524,53524,11,53552,53552,11,53580,53580,11,53608,53608,11,53636,53636,11,53664,53664,11,53692,53692,11,53720,53720,11,53748,53748,11,53776,53776,11,53804,53804,11,53832,53832,11,53860,53860,11,53888,53888,11,53916,53916,11,53944,53944,11,53972,53972,11,54000,54000,11,54028,54028,11,54056,54056,11,54084,54084,11,54112,54112,11,54140,54140,11,54168,54168,11,54196,54196,11,54224,54224,11,54252,54252,11,54280,54280,11,54308,54308,11,54336,54336,11,54364,54364,11,54392,54392,11,54420,54420,11,54448,54448,11,54476,54476,11,54504,54504,11,54532,54532,11,54560,54560,11,54588,54588,11,54616,54616,11,54644,54644,11,54672,54672,11,54700,54700,11,54728,54728,11,54756,54756,11,54784,54784,11,54812,54812,11,54840,54840,11,54868,54868,11,54896,54896,11,54924,54924,11,54952,54952,11,54980,54980,11,55008,55008,11,55036,55036,11,55064,55064,11,55092,55092,11,55120,55120,11,55148,55148,11,55176,55176,11,55216,55238,9,64286,64286,5,65056,65071,5,65438,65439,5,65529,65531,4,66272,66272,5,68097,68099,5,68108,68111,5,68159,68159,5,68900,68903,5,69446,69456,5,69632,69632,7,69634,69634,7,69744,69744,5,69759,69761,5,69808,69810,7,69815,69816,7,69821,69821,1,69837,69837,1,69927,69931,5,69933,69940,5,70003,70003,5,70018,70018,7,70070,70078,5,70082,70083,1,70094,70094,7,70188,70190,7,70194,70195,7,70197,70197,7,70206,70206,5,70368,70370,7,70400,70401,5,70459,70460,5,70463,70463,7,70465,70468,7,70475,70477,7,70498,70499,7,70512,70516,5,70712,70719,5,70722,70724,5,70726,70726,5,70832,70832,5,70835,70840,5,70842,70842,5,70845,70845,5,70847,70848,5,70850,70851,5,71088,71089,7,71096,71099,7,71102,71102,7,71132,71133,5,71219,71226,5,71229,71229,5,71231,71232,5,71340,71340,7,71342,71343,7,71350,71350,7,71453,71455,5,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,118528,118573,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123566,123566,5,125136,125142,5,126976,126979,14,126981,127182,14,127184,127231,14,127279,127279,14,127344,127345,14,127374,127374,14,127405,127461,14,127489,127490,14,127514,127514,14,127538,127546,14,127561,127567,14,127570,127743,14,127757,127758,14,127760,127760,14,127762,127762,14,127766,127768,14,127770,127770,14,127772,127772,14,127775,127776,14,127778,127779,14,127789,127791,14,127794,127795,14,127798,127798,14,127819,127819,14,127824,127824,14,127868,127868,14,127870,127871,14,127892,127893,14,127896,127896,14,127900,127901,14,127904,127940,14,127942,127942,14,127944,127944,14,127946,127946,14,127951,127955,14,127968,127971,14,127973,127984,14,127987,127987,14,127989,127989,14,127991,127991,14,127995,127999,5,128008,128008,14,128012,128014,14,128017,128018,14,128020,128020,14,128022,128022,14,128042,128042,14,128063,128063,14,128065,128065,14,128101,128101,14,128108,128109,14,128173,128173,14,128182,128183,14,128236,128237,14,128239,128239,14,128245,128245,14,128248,128248,14,128253,128253,14,128255,128258,14,128260,128263,14,128265,128265,14,128277,128277,14,128300,128301,14,128326,128328,14,128331,128334,14,128336,128347,14,128360,128366,14,128369,128370,14,128378,128378,14,128391,128391,14,128394,128397,14,128400,128400,14,128405,128406,14,128420,128420,14,128422,128423,14,128425,128432,14,128435,128443,14,128445,128449,14,128453,128464,14,128468,128475,14,128479,128480,14,128482,128482,14,128484,128487,14,128489,128494,14,128496,128498,14,128500,128505,14,128507,128511,14,128513,128518,14,128521,128525,14,128527,128527,14,128529,128529,14,128533,128533,14,128535,128535,14,128537,128537,14]")}var us;(function(t){t[t.zwj=8205]="zwj",t[t.emojiVariantSelector=65039]="emojiVariantSelector",t[t.enclosingKeyCap=8419]="enclosingKeyCap",t[t.space=32]="space"})(us||(us={}));var U2=class ae{static{this.c=new Ct(()=>JSON.parse('{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,1523,96,8242,96,1370,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,118002,50,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,118003,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,118004,52,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,118005,53,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,118006,54,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,118007,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,118008,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,118009,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,117974,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,117975,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71913,67,71922,67,65315,67,8557,67,8450,67,8493,67,117976,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,117977,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,117978,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,117979,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,117980,71,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,117981,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,117983,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,117984,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,118001,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,117982,108,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,117985,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,117986,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,117987,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,118000,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,117988,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,117989,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,117990,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,117991,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,117992,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,117993,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,117994,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,117995,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71910,87,71919,87,117996,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,117997,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,117998,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,71909,90,66293,90,65338,90,8484,90,8488,90,117999,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65283,35,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125,119846,109],"_default":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"cs":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"es":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"fr":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"it":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ja":[8211,45,8218,44,65281,33,8216,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65292,44,65297,49,65307,59],"ko":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pt-BR":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ru":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"zh-hans":[160,32,65374,126,8218,44,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65297,49],"zh-hant":[8211,45,65374,126,8218,44,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89]}'))}static{this.d=new gn({getCacheKey:JSON.stringify},e=>{function s(f){const l=new Map;for(let u=0;u<f.length;u+=2)l.set(f[u],f[u+1]);return l}function i(f,l){const u=new Map(f);for(const[d,v]of l)u.set(d,v);return u}function r(f,l){if(!f)return l;const u=new Map;for(const[d,v]of f)l.has(d)&&u.set(d,v);return u}const n=this.c.value;let o=e.filter(f=>!f.startsWith("_")&&f in n);o.length===0&&(o=["_default"]);let a;for(const f of o){const l=s(n[f]);a=r(a,l)}const h=s(n._common),c=i(h,a);return new ae(c)})}static getInstance(e){return ae.d.get(Array.from(e))}static{this.e=new Ct(()=>Object.keys(ae.c.value).filter(e=>!e.startsWith("_")))}static getLocales(){return ae.e.value}constructor(e){this.f=e}isAmbiguous(e){return this.f.has(e)}containsAmbiguousCharacter(e){for(let s=0;s<e.length;s++){const i=e.codePointAt(s);if(typeof i=="number"&&this.isAmbiguous(i))return!0}return!1}getPrimaryConfusable(e){return this.f.get(e)}getConfusableCodePoints(){return new Set(this.f.keys())}},F2=class ce{static c(){return JSON.parse('{"_common":[11,12,13,127,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999],"cs":[173,8203,12288],"de":[173,8203,12288],"es":[8203,12288],"fr":[173,8203,12288],"it":[160,173,12288],"ja":[173],"ko":[173,12288],"pl":[173,8203,12288],"pt-BR":[173,8203,12288],"qps-ploc":[160,173,8203,12288],"ru":[173,12288],"tr":[160,173,8203,12288],"zh-hans":[160,173,8203,12288],"zh-hant":[173,12288]}')}static{this.d=void 0}static e(){return this.d||(this.d=new Set([...Object.values(ce.c())].flat())),this.d}static isInvisibleCharacter(e){return ce.e().has(e)}static containsInvisibleCharacter(e){for(let s=0;s<e.length;s++){const i=e.codePointAt(s);if(typeof i=="number"&&(ce.isInvisibleCharacter(i)||i===32))return!0}return!1}static get codePoints(){return ce.e()}};function yt(t){return t===47||t===92}function ds(t){return t.replace(/[\\/]/g,S.sep)}function An(t){return t.indexOf("/")===-1&&(t=ds(t)),/^[a-zA-Z]:(\/|$)/.test(t)&&(t="/"+t),t}function gs(t,e=S.sep){if(!t)return"";const s=t.length,i=t.charCodeAt(0);if(yt(i)){if(yt(t.charCodeAt(1))&&!yt(t.charCodeAt(2))){let n=3;const o=n;for(;n<s&&!yt(t.charCodeAt(n));n++);if(o!==n&&!yt(t.charCodeAt(n+1))){for(n+=1;n<s;n++)if(yt(t.charCodeAt(n)))return t.slice(0,n+1).replace(/[\\/]/g,e)}}return e}else if(ps(i)&&t.charCodeAt(1)===58)return yt(t.charCodeAt(2))?t.slice(0,2)+e:t.slice(0,2);let r=t.indexOf("://");if(r!==-1){for(r+=3;r<s;r++)if(yt(t.charCodeAt(r)))return t.slice(0,r+1)}return""}function c1(t,e,s){const i=t===e;return!s||i?i:!t||!e?!1:cs(t,e)}function jt(t,e,s,i=nt){if(t===e)return!0;if(!t||!e||e.length>t.length)return!1;if(s){if(!hs(t,e))return!1;if(e.length===t.length)return!0;let n=e.length;return e.charAt(e.length-1)===i&&n--,t.charAt(n)===i}return e.charAt(e.length-1)!==i&&(e+=i),t.indexOf(e)===0}function ps(t){return t>=65&&t<=90||t>=97&&t<=122}function Cn(t){const e=$e(t);return Y?t.length>3?!1:xn(e)&&(t.length===2||e.charCodeAt(2)===92):e===S.sep}function xn(t,e=Y){return e?ps(t.charCodeAt(0))&&t.charCodeAt(1)===58:!1}var Ln="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",Sn="BDEFGHIJKMOQRSTUVWXYZbdefghijkmoqrstuvwxyz0123456789";function ws(t,e,s=8){let i="";for(let n=0;n<s;n++){let o;n===0&&Y&&!e&&(s===3||s===4)?o=Sn:o=Ln,i+=o.charAt(Math.floor(Math.random()*o.length))}let r;return e?r=`${e}-${i}`:r=i,t?rt(t,r):r}var Dn=/^\w[\w\d+.-]*$/,Pn=/^\//,kn=/^\/\//;function _n(t,e){if(!t.scheme&&e)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${t.authority}", path: "${t.path}", query: "${t.query}", fragment: "${t.fragment}"}`);if(t.scheme&&!Dn.test(t.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(t.path){if(t.authority){if(!Pn.test(t.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(kn.test(t.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}function Rn(t,e){return!t&&!e?"file":t}function In(t,e){switch(t){case"https":case"http":case"file":e?e[0]!==ot&&(e=ot+e):e=ot;break}return e}var _="",ot="/",On=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,F=class qe{static isUri(e){return e instanceof qe?!0:e?typeof e.authority=="string"&&typeof e.fragment=="string"&&typeof e.path=="string"&&typeof e.query=="string"&&typeof e.scheme=="string"&&typeof e.fsPath=="string"&&typeof e.with=="function"&&typeof e.toString=="function":!1}constructor(e,s,i,r,n,o=!1){typeof e=="object"?(this.scheme=e.scheme||_,this.authority=e.authority||_,this.path=e.path||_,this.query=e.query||_,this.fragment=e.fragment||_):(this.scheme=Rn(e,o),this.authority=s||_,this.path=In(this.scheme,i||_),this.query=r||_,this.fragment=n||_,_n(this,o))}get fsPath(){return Ae(this,!1)}with(e){if(!e)return this;let{scheme:s,authority:i,path:r,query:n,fragment:o}=e;return s===void 0?s=this.scheme:s===null&&(s=_),i===void 0?i=this.authority:i===null&&(i=_),r===void 0?r=this.path:r===null&&(r=_),n===void 0?n=this.query:n===null&&(n=_),o===void 0?o=this.fragment:o===null&&(o=_),s===this.scheme&&i===this.authority&&r===this.path&&n===this.query&&o===this.fragment?this:new Mt(s,i,r,n,o)}static parse(e,s=!1){const i=On.exec(e);return i?new Mt(i[2]||_,Ce(i[4]||_),Ce(i[5]||_),Ce(i[7]||_),Ce(i[9]||_),s):new Mt(_,_,_,_,_)}static file(e){let s=_;if(Y&&(e=e.replace(/\\/g,ot)),e[0]===ot&&e[1]===ot){const i=e.indexOf(ot,2);i===-1?(s=e.substring(2),e=ot):(s=e.substring(2,i),e=e.substring(i)||ot)}return new Mt("file",s,e,_,_)}static from(e,s){return new Mt(e.scheme,e.authority,e.path,e.query,e.fragment,s)}static joinPath(e,...s){if(!e.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let i;return Y&&e.scheme==="file"?i=qe.file(M.join(Ae(e,!0),...s)).path:i=S.join(e.path,...s),e.with({path:i})}toString(e=!1){return h1(this,e)}toJSON(){return this}static revive(e){if(e){if(e instanceof qe)return e;{const s=new Mt(e);return s._formatted=e.external??null,s._fsPath=e._sep===vs?e.fsPath??null:null,s}}else return e}[Symbol.for("debug.description")](){return`URI(${this.toString()})`}},vs=Y?1:void 0,Mt=class extends F{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=Ae(this,!1)),this._fsPath}toString(t=!1){return t?h1(this,!0):(this._formatted||(this._formatted=h1(this,!1)),this._formatted)}toJSON(){const t={$mid:1};return this._fsPath&&(t.fsPath=this._fsPath,t._sep=vs),this._formatted&&(t.external=this._formatted),this.path&&(t.path=this.path),this.scheme&&(t.scheme=this.scheme),this.authority&&(t.authority=this.authority),this.query&&(t.query=this.query),this.fragment&&(t.fragment=this.fragment),t}},ms={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function bs(t,e,s){let i,r=-1;for(let n=0;n<t.length;n++){const o=t.charCodeAt(n);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||o===45||o===46||o===95||o===126||e&&o===47||s&&o===91||s&&o===93||s&&o===58)r!==-1&&(i+=encodeURIComponent(t.substring(r,n)),r=-1),i!==void 0&&(i+=t.charAt(n));else{i===void 0&&(i=t.substr(0,n));const a=ms[o];a!==void 0?(r!==-1&&(i+=encodeURIComponent(t.substring(r,n)),r=-1),i+=a):r===-1&&(r=n)}}return r!==-1&&(i+=encodeURIComponent(t.substring(r))),i!==void 0?i:t}function Nn(t){let e;for(let s=0;s<t.length;s++){const i=t.charCodeAt(s);i===35||i===63?(e===void 0&&(e=t.substr(0,s)),e+=ms[i]):e!==void 0&&(e+=t[s])}return e!==void 0?e:t}function Ae(t,e){let s;return t.authority&&t.path.length>1&&t.scheme==="file"?s=`//${t.authority}${t.path}`:t.path.charCodeAt(0)===47&&(t.path.charCodeAt(1)>=65&&t.path.charCodeAt(1)<=90||t.path.charCodeAt(1)>=97&&t.path.charCodeAt(1)<=122)&&t.path.charCodeAt(2)===58?e?s=t.path.substr(1):s=t.path[1].toLowerCase()+t.path.substr(2):s=t.path,Y&&(s=s.replace(/\//g,"\\")),s}function h1(t,e){const s=e?Nn:bs;let i="",{scheme:r,authority:n,path:o,query:a,fragment:h}=t;if(r&&(i+=r,i+=":"),(n||r==="file")&&(i+=ot,i+=ot),n){let c=n.indexOf("@");if(c!==-1){const f=n.substr(0,c);n=n.substr(c+1),c=f.lastIndexOf(":"),c===-1?i+=s(f,!1,!1):(i+=s(f.substr(0,c),!1,!1),i+=":",i+=s(f.substr(c+1),!1,!0)),i+="@"}n=n.toLowerCase(),c=n.lastIndexOf(":"),c===-1?i+=s(n,!1,!0):(i+=s(n.substr(0,c),!1,!0),i+=n.substr(c))}if(o){if(o.length>=3&&o.charCodeAt(0)===47&&o.charCodeAt(2)===58){const c=o.charCodeAt(1);c>=65&&c<=90&&(o=`/${String.fromCharCode(c+32)}:${o.substr(3)}`)}else if(o.length>=2&&o.charCodeAt(1)===58){const c=o.charCodeAt(0);c>=65&&c<=90&&(o=`${String.fromCharCode(c+32)}:${o.substr(2)}`)}i+=s(o,!0,!1)}return a&&(i+="?",i+=s(a,!1,!1)),h&&(i+="#",i+=e?h:bs(h,!1,!1)),i}function ys(t){try{return decodeURIComponent(t)}catch{return t.length>3?t.substr(0,3)+ys(t.substr(3)):t}}var $s=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function Ce(t){return t.match($s)?t.replace($s,e=>ys(e)):t}var W;(function(t){t.inMemory="inmemory",t.vscode="vscode",t.internal="private",t.walkThrough="walkThrough",t.walkThroughSnippet="walkThroughSnippet",t.http="http",t.https="https",t.file="file",t.mailto="mailto",t.untitled="untitled",t.data="data",t.command="command",t.vscodeRemote="vscode-remote",t.vscodeRemoteResource="vscode-remote-resource",t.vscodeManagedRemoteResource="vscode-managed-remote-resource",t.vscodeUserData="vscode-userdata",t.vscodeCustomEditor="vscode-custom-editor",t.vscodeNotebookCell="vscode-notebook-cell",t.vscodeNotebookCellMetadata="vscode-notebook-cell-metadata",t.vscodeNotebookCellMetadataDiff="vscode-notebook-cell-metadata-diff",t.vscodeNotebookCellOutput="vscode-notebook-cell-output",t.vscodeNotebookCellOutputDiff="vscode-notebook-cell-output-diff",t.vscodeNotebookMetadata="vscode-notebook-metadata",t.vscodeInteractiveInput="vscode-interactive-input",t.vscodeSettings="vscode-settings",t.vscodeWorkspaceTrust="vscode-workspace-trust",t.vscodeTerminal="vscode-terminal",t.vscodeChatCodeBlock="vscode-chat-code-block",t.vscodeChatCodeCompareBlock="vscode-chat-code-compare-block",t.vscodeChatSesssion="vscode-chat-editor",t.webviewPanel="webview-panel",t.vscodeWebview="vscode-webview",t.extension="extension",t.vscodeFileResource="vscode-file",t.tmp="tmp",t.vsls="vsls",t.vscodeSourceControl="vscode-scm",t.commentsInput="comment",t.codeSetting="code-setting",t.outputChannel="output",t.accessibleView="accessible-view"})(W||(W={}));var jn="tkn",Mn=class{constructor(){this.a=Object.create(null),this.b=Object.create(null),this.c=Object.create(null),this.d="http",this.e=null,this.f="/"}setPreferredWebSchema(t){this.d=t}setDelegate(t){this.e=t}setServerRootPath(t,e){this.f=S.join(e??"/",Un(t))}getServerRootPath(){return this.f}get g(){return S.join(this.f,W.vscodeRemoteResource)}set(t,e,s){this.a[t]=e,this.b[t]=s}setConnectionToken(t,e){this.c[t]=e}getPreferredWebSchema(){return this.d}rewrite(t){if(this.e)try{return this.e(t)}catch(o){return Ht(o),t}const e=t.authority;let s=this.a[e];s&&s.indexOf(":")!==-1&&s.indexOf("[")===-1&&(s=`[${s}]`);const i=this.b[e],r=this.c[e];let n=`path=${encodeURIComponent(t.path)}`;return typeof r=="string"&&(n+=`&${jn}=${encodeURIComponent(r)}`),F.from({scheme:Vr?this.d:W.vscodeRemoteResource,authority:`${s}:${i}`,path:this.g,query:n})}},Tn=new Mn;function Un(t){return`${t.quality??"oss"}-${t.commit??"dev"}`}var Fn="vscode-app",Wn=class ze{static{this.a=Fn}asBrowserUri(e){const s=this.b(e);return this.uriToBrowserUri(s)}uriToBrowserUri(e){return e.scheme===W.vscodeRemote?Tn.rewrite(e):e.scheme===W.file&&(Hr||Kr===`${W.vscodeFileResource}://${ze.a}`)?e.with({scheme:W.vscodeFileResource,authority:e.authority||ze.a,query:null,fragment:null}):e}asFileUri(e){const s=this.b(e);return this.uriToFileUri(s)}uriToFileUri(e){return e.scheme===W.vscodeFileResource?e.with({scheme:W.file,authority:e.authority!==ze.a?e.authority:null,query:null,fragment:null}):e}b(e){if(F.isUri(e))return e;if(globalThis._VSCODE_FILE_ROOT){const s=globalThis._VSCODE_FILE_ROOT;if(/^\w[\w\d+.-]*:\/\//.test(s))return F.joinPath(F.parse(s,!0),e);const i=rt(s,e);return F.file(i)}throw new Error("Cannot determine URI for module id!")}},W2=new Wn,B2=Object.freeze({"Cache-Control":"no-cache, no-store"}),q2=Object.freeze({"Document-Policy":"include-js-call-stacks-in-crash-reports"}),Es;(function(t){const e=new Map([["1",{"Cross-Origin-Opener-Policy":"same-origin"}],["2",{"Cross-Origin-Embedder-Policy":"require-corp"}],["3",{"Cross-Origin-Opener-Policy":"same-origin","Cross-Origin-Embedder-Policy":"require-corp"}]]);t.CoopAndCoep=Object.freeze(e.get("3"));const s="vscode-coi";function i(n){let o;typeof n=="string"?o=new URL(n).searchParams:n instanceof URL?o=n.searchParams:F.isUri(n)&&(o=new URL(n.toString(!0)).searchParams);const a=o?.get(s);if(a)return e.get(a)}t.getHeadersFromQuery=i;function r(n,o,a){if(!globalThis.crossOriginIsolated)return;const h=o&&a?"3":a?"2":"1";n instanceof URLSearchParams?n.set(s,h):n[s]=h}t.addSearchParam=r})(Es||(Es={}));function pt(t){return Ae(t,!0)}var l1=class{constructor(t){this.a=t}compare(t,e,s=!1){return t===e?0:o1(this.getComparisonKey(t,s),this.getComparisonKey(e,s))}isEqual(t,e,s=!1){return t===e?!0:!t||!e?!1:this.getComparisonKey(t,s)===this.getComparisonKey(e,s)}getComparisonKey(t,e=!1){return t.with({path:this.a(t)?t.path.toLowerCase():void 0,fragment:e?null:void 0}).toString()}ignorePathCasing(t){return this.a(t)}isEqualOrParent(t,e,s=!1){if(t.scheme===e.scheme){if(t.scheme===W.file)return jt(pt(t),pt(e),this.a(t))&&t.query===e.query&&(s||t.fragment===e.fragment);if(Cs(t.authority,e.authority))return jt(t.path,e.path,this.a(t),"/")&&t.query===e.query&&(s||t.fragment===e.fragment)}return!1}joinPath(t,...e){return F.joinPath(t,...e)}basenameOrAuthority(t){return qn(t)||t.authority}basename(t){return S.basename(t.path)}extname(t){return S.extname(t.path)}dirname(t){if(t.path.length===0)return t;let e;return t.scheme===W.file?e=F.file(n1(pt(t))).path:(e=S.dirname(t.path),t.authority&&e.length&&e.charCodeAt(0)!==47&&(console.error(`dirname("${t.toString})) resulted in a relative path`),e="/")),t.with({path:e})}normalizePath(t){if(!t.path.length)return t;let e;return t.scheme===W.file?e=F.file($e(pt(t))).path:e=S.normalize(t.path),t.with({path:e})}relativePath(t,e){if(t.scheme!==e.scheme||!Cs(t.authority,e.authority))return;if(t.scheme===W.file){const r=fn(pt(t),pt(e));return Y?ds(r):r}let s=t.path||"/";const i=e.path||"/";if(this.a(t)){let r=0;for(const n=Math.min(s.length,i.length);r<n&&!(s.charCodeAt(r)!==i.charCodeAt(r)&&s.charAt(r).toLowerCase()!==i.charAt(r).toLowerCase());r++);s=i.substr(0,r)+s.substr(r)}return S.relative(s,i)}resolvePath(t,e){if(t.scheme===W.file){const s=F.file(ln(pt(t),e));return t.with({authority:s.authority,path:s.path})}return e=An(e),t.with({path:S.resolve(t.path,e)})}isAbsolutePath(t){return!!t.path&&t.path[0]==="/"}isEqualAuthority(t,e){return t===e||t!==void 0&&e!==void 0&&cs(t,e)}hasTrailingPathSeparator(t,e=nt){if(t.scheme===W.file){const s=pt(t);return s.length>gs(s).length&&s[s.length-1]===e}else{const s=t.path;return s.length>1&&s.charCodeAt(s.length-1)===47&&!/^[a-zA-Z]:(\/$|\\$)/.test(t.fsPath)}}removeTrailingPathSeparator(t,e=nt){return xs(t,e)?t.with({path:t.path.substr(0,t.path.length-1)}):t}addTrailingPathSeparator(t,e=nt){let s=!1;if(t.scheme===W.file){const i=pt(t);s=i!==void 0&&i.length===gs(i).length&&i[i.length-1]===e}else{e="/";const i=t.path;s=i.length===1&&i.charCodeAt(i.length-1)===47}return!s&&!xs(t,e)?t.with({path:t.path+"/"}):t}},L=new l1(()=>!1),Bn=new l1(t=>t.scheme===W.file?!z:!0),z2=new l1(t=>!0),H2=L.isEqual.bind(L),V2=L.isEqualOrParent.bind(L),Q2=L.getComparisonKey.bind(L),K2=L.basenameOrAuthority.bind(L),qn=L.basename.bind(L),G2=L.extname.bind(L),J2=L.dirname.bind(L),As=L.joinPath.bind(L),Y2=L.normalizePath.bind(L),Z2=L.relativePath.bind(L),X2=L.resolvePath.bind(L),t0=L.isAbsolutePath.bind(L),Cs=L.isEqualAuthority.bind(L),xs=L.hasTrailingPathSeparator.bind(L),e0=L.removeTrailingPathSeparator.bind(L),s0=L.addTrailingPathSeparator.bind(L),Ls;(function(t){t.META_DATA_LABEL="label",t.META_DATA_DESCRIPTION="description",t.META_DATA_SIZE="size",t.META_DATA_MIME="mime";function e(s){const i=new Map;s.path.substring(s.path.indexOf(";")+1,s.path.lastIndexOf(";")).split(";").forEach(o=>{const[a,h]=o.split(":");a&&h&&i.set(a,h)});const n=s.path.substring(0,s.path.indexOf(";"));return n&&i.set(t.META_DATA_MIME,n),i}t.parseMetaData=e})(Ls||(Ls={}));var zn=Symbol("MicrotaskDelay");function f1(t){return!!t&&typeof t.then=="function"}function u1(t){const e=new It,s=t(e.token);let i=!1;const r=new Promise((n,o)=>{const a=e.token.onCancellationRequested(()=>{i=!0,a.dispose(),o(new lt)});Promise.resolve(s).then(h=>{a.dispose(),e.dispose(),i?yr(h)&&h.dispose():n(h)},h=>{a.dispose(),e.dispose(),o(h)})});return new class{cancel(){e.cancel(),e.dispose()}then(n,o){return r.then(n,o)}catch(n){return this.then(void 0,n)}finally(n){return r.finally(n)}}}var Hn=class{constructor(){this.f=!1,this.a=null,this.b=null,this.d=null}queue(t){if(this.f)return Promise.reject(new Error("Throttler is disposed"));if(this.a){if(this.d=t,!this.b){const e=()=>{if(this.b=null,this.f)return;const s=this.queue(this.d);return this.d=null,s};this.b=new Promise(s=>{this.a.then(e,e).then(s)})}return new Promise((e,s)=>{this.b.then(e,s)})}return this.a=t(),new Promise((e,s)=>{this.a.then(i=>{this.a=null,e(i)},i=>{this.a=null,s(i)})})}dispose(){this.f=!0}},Vn=(t,e)=>{let s=!0;const i=setTimeout(()=>{s=!1,e()},t);return{isTriggered:()=>s,dispose:()=>{clearTimeout(i),s=!1}}},Qn=t=>{let e=!0;return queueMicrotask(()=>{e&&(e=!1,t())}),{isTriggered:()=>e,dispose:()=>{e=!1}}},Kn=class{constructor(t){this.defaultDelay=t,this.a=null,this.b=null,this.d=null,this.f=null,this.g=null}trigger(t,e=this.defaultDelay){this.g=t,this.h(),this.b||(this.b=new Promise((i,r)=>{this.d=i,this.f=r}).then(()=>{if(this.b=null,this.d=null,this.g){const i=this.g;return this.g=null,i()}}));const s=()=>{this.a=null,this.d?.(null)};return this.a=e===zn?Qn(s):Vn(e,s),this.b}isTriggered(){return!!this.a?.isTriggered()}cancel(){this.h(),this.b&&(this.f?.(new lt),this.b=null)}h(){this.a?.dispose(),this.a=null}dispose(){this.cancel()}},Gn=class{constructor(t){this.a=new Kn(t),this.b=new Hn}trigger(t,e){return this.a.trigger(()=>this.b.queue(t),e)}isTriggered(){return this.a.isTriggered()}cancel(){this.a.cancel()}dispose(){this.a.dispose(),this.b.dispose()}};function Ss(t,e){return e?new Promise((s,i)=>{const r=setTimeout(()=>{n.dispose(),s()},t),n=e.onCancellationRequested(()=>{clearTimeout(r),n.dispose(),i(new lt)})}):u1(s=>Ss(t,s))}var Jn=class{constructor(t){this.a=0,this.b=!1,this.f=t,this.g=[],this.d=0,this.h=new I}whenIdle(){return this.size>0?R.toPromise(this.onDrained):Promise.resolve()}get onDrained(){return this.h.event}get size(){return this.a}queue(t){if(this.b)throw new Error("Object has been disposed");return this.a++,new Promise((e,s)=>{this.g.push({factory:t,c:e,e:s}),this.j()})}j(){for(;this.g.length&&this.d<this.f;){const t=this.g.shift();this.d++;const e=t.factory();e.then(t.c,t.e),e.then(()=>this.k(),()=>this.k())}}k(){this.b||(this.d--,--this.a===0&&this.h.fire(),this.g.length>0&&this.j())}clear(){if(this.b)throw new Error("Object has been disposed");this.g.length=0,this.a=this.d}dispose(){this.b=!0,this.g.length=0,this.a=0,this.h.dispose()}},Yn=class extends Jn{constructor(){super(1)}},Zn=class{constructor(){this.a=new Map,this.b=new Set,this.d=void 0,this.f=0}async whenDrained(){if(this.g())return;const t=new Tt;return this.b.add(t),t.p}g(){for(const[,t]of this.a)if(t.size>0)return!1;return!0}queueSize(t,e=L){const s=e.getComparisonKey(t);return this.a.get(s)?.size??0}queueFor(t,e,s=L){const i=s.getComparisonKey(t);let r=this.a.get(i);if(!r){r=new Yn;const n=this.f++,o=R.once(r.onDrained)(()=>{r?.dispose(),this.a.delete(i),this.h(),this.d?.deleteAndDispose(n),this.d?.size===0&&(this.d.dispose(),this.d=void 0)});this.d||(this.d=new z1),this.d.set(n,o),this.a.set(i,r)}return r.queue(e)}h(){this.g()&&this.j()}j(){for(const t of this.b)t.complete();this.b.clear()}dispose(){for(const[,t]of this.a)t.dispose();this.a.clear(),this.j(),this.d?.dispose()}},xe=class{constructor(t,e){this.b=-1,this.a=t,this.d=e,this.f=this.g.bind(this)}dispose(){this.cancel(),this.a=null}cancel(){this.isScheduled()&&(clearTimeout(this.b),this.b=-1)}schedule(t=this.d){this.cancel(),this.b=setTimeout(this.f,t)}get delay(){return this.d}set delay(t){this.d=t}isScheduled(){return this.b!==-1}flush(){this.isScheduled()&&(this.cancel(),this.h())}g(){this.b=-1,this.a&&this.h()}h(){this.a?.()}},d1=class extends xe{constructor(t,e){super(t,e),this.j=[]}work(t){this.j.push(t),this.isScheduled()||this.schedule()}h(){const t=this.j;this.j=[],this.a?.(t)}dispose(){this.j=[],super.dispose()}},g1=class extends X{constructor(t,e){super(),this.h=t,this.j=e,this.a=[],this.b=this.B(new Xe),this.f=!1,this.g=0}get pending(){return this.a.length}work(t){if(this.f)return!1;if(typeof this.h.maxBufferedWork=="number"){if(this.b.value){if(this.pending+t.length>this.h.maxBufferedWork)return!1}else if(this.pending+t.length-this.h.maxWorkChunkSize>this.h.maxBufferedWork)return!1}for(const s of t)this.a.push(s);const e=Date.now()-this.g;return!this.b.value&&(!this.h.waitThrottleDelayBetweenWorkUnits||e>=this.h.throttleDelay)?this.m():!this.b.value&&this.h.waitThrottleDelayBetweenWorkUnits&&this.r(Math.max(this.h.throttleDelay-e,0)),!0}m(){this.g=Date.now(),this.j(this.a.splice(0,this.h.maxWorkChunkSize)),this.a.length>0&&this.r()}r(t=this.h.throttleDelay){this.b.value=new xe(()=>{this.b.clear(),this.m()},t),this.b.value.schedule()}dispose(){super.dispose(),this.a.length=0,this.f=!0}},Xn,p1;(function(){typeof globalThis.requestIdleCallback!="function"||typeof globalThis.cancelIdleCallback!="function"?p1=(t,e,s)=>{Jr(()=>{if(i)return;const r=Date.now()+15;e(Object.freeze({didTimeout:!0,timeRemaining(){return Math.max(0,r-Date.now())}}))});let i=!1;return{dispose(){i||(i=!0)}}}:p1=(t,e,s)=>{const i=t.requestIdleCallback(e,typeof s=="number"?{timeout:s}:void 0);let r=!1;return{dispose(){r||(r=!0,t.cancelIdleCallback(i))}}},Xn=(t,e)=>p1(globalThis,t,e)})();var Ds;(function(t){t[t.Resolved=0]="Resolved",t[t.Rejected=1]="Rejected"})(Ds||(Ds={}));var Tt=class{get isRejected(){return this.d?.outcome===1}get isResolved(){return this.d?.outcome===0}get isSettled(){return!!this.d}get value(){return this.d?.outcome===0?this.d?.value:void 0}constructor(){this.p=new Promise((t,e)=>{this.a=t,this.b=e})}complete(t){return new Promise(e=>{this.a(t),this.d={outcome:0,value:t},e()})}error(t){return new Promise(e=>{this.b(t),this.d={outcome:1,value:t},e()})}cancel(){return this.error(new lt)}},Le;(function(t){async function e(i){let r;const n=await Promise.all(i.map(o=>o.then(a=>a,a=>{r||(r=a)})));if(typeof r<"u")throw r;return n}t.settled=e;function s(i){return new Promise(async(r,n)=>{try{await i(r,n)}catch(o){n(o)}})}t.withAsyncBody=s})(Le||(Le={}));var Ps;(function(t){t[t.Initial=0]="Initial",t[t.DoneOK=1]="DoneOK",t[t.DoneError=2]="DoneError"})(Ps||(Ps={}));var i0=class Z{static fromArray(e){return new Z(s=>{s.emitMany(e)})}static fromPromise(e){return new Z(async s=>{s.emitMany(await e)})}static fromPromisesResolveOrder(e){return new Z(async s=>{await Promise.all(e.map(async i=>s.emitOne(await i)))})}static merge(e){return new Z(async s=>{await Promise.all(e.map(async i=>{for await(const r of i)s.emitOne(r)}))})}static{this.EMPTY=Z.fromArray([])}constructor(e,s){this.a=0,this.b=[],this.d=null,this.f=s,this.g=new I,queueMicrotask(async()=>{const i={emitOne:r=>this.h(r),emitMany:r=>this.j(r),reject:r=>this.l(r)};try{await Promise.resolve(e(i)),this.k()}catch(r){this.l(r)}finally{i.emitOne=void 0,i.emitMany=void 0,i.reject=void 0}})}[Symbol.asyncIterator](){let e=0;return{next:async()=>{do{if(this.a===2)throw this.d;if(e<this.b.length)return{done:!1,value:this.b[e++]};if(this.a===1)return{done:!0,value:void 0};await R.toPromise(this.g.event)}while(!0)},return:async()=>(this.f?.(),{done:!0,value:void 0})}}static map(e,s){return new Z(async i=>{for await(const r of e)i.emitOne(s(r))})}map(e){return Z.map(this,e)}static filter(e,s){return new Z(async i=>{for await(const r of e)s(r)&&i.emitOne(r)})}filter(e){return Z.filter(this,e)}static coalesce(e){return Z.filter(e,s=>!!s)}coalesce(){return Z.coalesce(this)}static async toPromise(e){const s=[];for await(const i of e)s.push(i);return s}toPromise(){return Z.toPromise(this)}h(e){this.a===0&&(this.b.push(e),this.g.fire())}j(e){this.a===0&&(this.b=this.b.concat(e),this.g.fire())}k(){this.a===0&&(this.a=1,this.g.fire())}l(e){this.a===0&&(this.a=2,this.d=e,this.g.fire())}},Se=typeof Buffer<"u",to=new Ct(()=>new Uint8Array(256)),w1,v1,at=class tt{static alloc(e){return Se?new tt(Buffer.allocUnsafe(e)):new tt(new Uint8Array(e))}static wrap(e){return Se&&!Buffer.isBuffer(e)&&(e=Buffer.from(e.buffer,e.byteOffset,e.byteLength)),new tt(e)}static fromString(e,s){return!(s?.dontUseNodeBuffer||!1)&&Se?new tt(Buffer.from(e)):(w1||(w1=new TextEncoder),new tt(w1.encode(e)))}static fromByteArray(e){const s=tt.alloc(e.length);for(let i=0,r=e.length;i<r;i++)s.buffer[i]=e[i];return s}static concat(e,s){if(typeof s>"u"){s=0;for(let n=0,o=e.length;n<o;n++)s+=e[n].byteLength}const i=tt.alloc(s);let r=0;for(let n=0,o=e.length;n<o;n++){const a=e[n];i.set(a,r),r+=a.byteLength}return i}constructor(e){this.buffer=e,this.byteLength=this.buffer.byteLength}clone(){const e=tt.alloc(this.byteLength);return e.set(this),e}toString(){return Se?this.buffer.toString():(v1||(v1=new TextDecoder),v1.decode(this.buffer))}slice(e,s){return new tt(this.buffer.subarray(e,s))}set(e,s){if(e instanceof tt)this.buffer.set(e.buffer,s);else if(e instanceof Uint8Array)this.buffer.set(e,s);else if(e instanceof ArrayBuffer)this.buffer.set(new Uint8Array(e),s);else if(ArrayBuffer.isView(e))this.buffer.set(new Uint8Array(e.buffer,e.byteOffset,e.byteLength),s);else throw new Error("Unknown argument 'array'")}readUInt32BE(e){return so(this.buffer,e)}writeUInt32BE(e,s){io(this.buffer,e,s)}readUInt32LE(e){return ro(this.buffer,e)}writeUInt32LE(e,s){no(this.buffer,e,s)}readUInt8(e){return oo(this.buffer,e)}writeUInt8(e,s){ao(this.buffer,e,s)}indexOf(e,s=0){return eo(this.buffer,e instanceof tt?e.buffer:e,s)}equals(e){return this===e?!0:this.byteLength!==e.byteLength?!1:this.buffer.every((s,i)=>s===e.buffer[i])}};function eo(t,e,s=0){const i=e.byteLength,r=t.byteLength;if(i===0)return 0;if(i===1)return t.indexOf(e[0]);if(i>r-s)return-1;const n=to.value;n.fill(e.length);for(let c=0;c<e.length;c++)n[e[c]]=e.length-c-1;let o=s+e.length-1,a=o,h=-1;for(;o<r;)if(t[o]===e[a]){if(a===0){h=o;break}o--,a--}else o+=Math.max(e.length-a,n[t[o]]),a=e.length-1;return h}function so(t,e){return t[e]*2**24+t[e+1]*2**16+t[e+2]*2**8+t[e+3]}function io(t,e,s){t[s+3]=e,e=e>>>8,t[s+2]=e,e=e>>>8,t[s+1]=e,e=e>>>8,t[s]=e}function ro(t,e){return t[e+0]<<0>>>0|t[e+1]<<8>>>0|t[e+2]<<16>>>0|t[e+3]<<24>>>0}function no(t,e,s){t[s+0]=e&255,e=e>>>8,t[s+1]=e&255,e=e>>>8,t[s+2]=e&255,e=e>>>8,t[s+3]=e&255}function oo(t,e){return t[e]}function ao(t,e,s){t[s]=e}function co(t,e,s){let i=null,r=null;if(typeof s.value=="function"?(i="value",r=s.value,r.length!==0&&console.warn("Memoize should only be used in functions with zero parameters")):typeof s.get=="function"&&(i="get",r=s.get),!r)throw new Error("not supported");const n=`$memoize$${e}`;s[i]=function(...o){return this.hasOwnProperty(n)||Object.defineProperty(this,n,{configurable:!1,enumerable:!1,writable:!1,value:r.apply(this,o)}),this[n]}}function De(t,e=0){if(!t||e>200)return t;if(typeof t=="object"){switch(t.$mid){case 1:return F.revive(t);case 2:return new RegExp(t.source,t.flags);case 17:return new Date(t.source)}if(t instanceof at||t instanceof Uint8Array)return t;if(Array.isArray(t))for(let s=0;s<t.length;++s)t[s]=De(t[s],e+1);else for(const s in t)Object.hasOwnProperty.call(t,s)&&(t[s]=De(t[s],e+1))}return t}var ks;(function(t){t[t.Promise=100]="Promise",t[t.PromiseCancel=101]="PromiseCancel",t[t.EventListen=102]="EventListen",t[t.EventDispose=103]="EventDispose"})(ks||(ks={}));function Ut(t){switch(t){case 100:return"req";case 101:return"cancel";case 102:return"subscribe";case 103:return"unsubscribe"}}var _s;(function(t){t[t.Initialize=200]="Initialize",t[t.PromiseSuccess=201]="PromiseSuccess",t[t.PromiseError=202]="PromiseError",t[t.PromiseErrorObj=203]="PromiseErrorObj",t[t.EventFire=204]="EventFire"})(_s||(_s={}));function Pe(t){switch(t){case 200:return"init";case 201:return"reply:";case 202:case 203:return"replyErr:";case 204:return"event:"}}var xt;(function(t){t[t.Uninitialized=0]="Uninitialized",t[t.Idle=1]="Idle"})(xt||(xt={}));function Ft(t){let e=0;for(let s=0;;s+=7){const i=t.read(1);if(e|=(i.buffer[0]&127)<<s,!(i.buffer[0]&128))return e}}var ho=$t(0);function Wt(t,e){if(e===0){t.write(ho);return}let s=0;for(let r=e;r!==0;r=r>>>7)s++;const i=at.alloc(s);for(let r=0;e!==0;r++)i.buffer[r]=e&127,e=e>>>7,e>0&&(i.buffer[r]|=128);t.write(i)}var m1=class{constructor(t){this.b=t,this.a=0}read(t){const e=this.b.slice(this.a,this.a+t);return this.a+=e.byteLength,e}},Rs=class{constructor(){this.a=[]}get buffer(){return at.concat(this.a)}write(t){this.a.push(t)}},q;(function(t){t[t.Undefined=0]="Undefined",t[t.String=1]="String",t[t.Buffer=2]="Buffer",t[t.VSBuffer=3]="VSBuffer",t[t.Array=4]="Array",t[t.Object=5]="Object",t[t.Int=6]="Int"})(q||(q={}));function $t(t){const e=at.alloc(1);return e.writeUInt8(t,0),e}var Lt={Undefined:$t(q.Undefined),String:$t(q.String),Buffer:$t(q.Buffer),VSBuffer:$t(q.VSBuffer),Array:$t(q.Array),Object:$t(q.Object),Uint:$t(q.Int)},lo=typeof Buffer<"u";function te(t,e){if(typeof e>"u")t.write(Lt.Undefined);else if(typeof e=="string"){const s=at.fromString(e);t.write(Lt.String),Wt(t,s.byteLength),t.write(s)}else if(lo&&Buffer.isBuffer(e)){const s=at.wrap(e);t.write(Lt.Buffer),Wt(t,s.byteLength),t.write(s)}else if(e instanceof at)t.write(Lt.VSBuffer),Wt(t,e.byteLength),t.write(e);else if(Array.isArray(e)){t.write(Lt.Array),Wt(t,e.length);for(const s of e)te(t,s)}else if(typeof e=="number"&&(e|0)===e)t.write(Lt.Uint),Wt(t,e);else{const s=at.fromString(JSON.stringify(e));t.write(Lt.Object),Wt(t,s.byteLength),t.write(s)}}function Bt(t){switch(t.read(1).readUInt8(0)){case q.Undefined:return;case q.String:return t.read(Ft(t)).toString();case q.Buffer:return t.read(Ft(t)).buffer;case q.VSBuffer:return t.read(Ft(t));case q.Array:{const s=Ft(t),i=[];for(let r=0;r<s;r++)i.push(Bt(t));return i}case q.Object:return JSON.parse(t.read(Ft(t)).toString());case q.Int:return Ft(t)}}var Is=class{constructor(t,e,s=null,i=1e3){this.h=t,this.j=e,this.k=s,this.l=i,this.b=new Map,this.d=new Map,this.g=new Map,this.f=this.h.onMessage(r=>this.q(r)),this.m({type:200})}registerChannel(t,e){this.b.set(t,e),setTimeout(()=>this.w(t),0)}m(t){switch(t.type){case 200:{const e=this.o([t.type]);this.k?.logOutgoing(e,0,1,Pe(t.type));return}case 201:case 202:case 204:case 203:{const e=this.o([t.type,t.id],t.data);this.k?.logOutgoing(e,t.id,1,Pe(t.type),t.data);return}}}o(t,e=void 0){const s=new Rs;return te(s,t),te(s,e),this.p(s.buffer)}p(t){try{return this.h.send(t),t.byteLength}catch{return 0}}q(t){const e=new m1(t),s=Bt(e),i=Bt(e),r=s[0];switch(r){case 100:return this.k?.logIncoming(t.byteLength,s[1],1,`${Ut(r)}: ${s[2]}.${s[3]}`,i),this.s({type:r,id:s[1],channelName:s[2],name:s[3],arg:i});case 102:return this.k?.logIncoming(t.byteLength,s[1],1,`${Ut(r)}: ${s[2]}.${s[3]}`,i),this.t({type:r,id:s[1],channelName:s[2],name:s[3],arg:i});case 101:return this.k?.logIncoming(t.byteLength,s[1],1,`${Ut(r)}`),this.u({type:r,id:s[1]});case 103:return this.k?.logIncoming(t.byteLength,s[1],1,`${Ut(r)}`),this.u({type:r,id:s[1]})}}s(t){const e=this.b.get(t.channelName);if(!e){this.v(t);return}const s=new It;let i;try{i=e.call(this.j,t.name,t.arg,s.token)}catch(o){i=Promise.reject(o)}const r=t.id;i.then(o=>{this.m({id:r,data:o,type:201})},o=>{o instanceof Error?this.m({id:r,data:{message:o.message,name:o.name,stack:o.stack?o.stack.split(`
`):void 0},type:202}):this.m({id:r,data:o,type:203})}).finally(()=>{n.dispose(),this.d.delete(t.id)});const n=G(()=>s.cancel());this.d.set(t.id,n)}t(t){const e=this.b.get(t.channelName);if(!e){this.v(t);return}const s=t.id,r=e.listen(this.j,t.name,t.arg)(n=>this.m({id:s,data:n,type:204}));this.d.set(t.id,r)}u(t){const e=this.d.get(t.id);e&&(e.dispose(),this.d.delete(t.id))}v(t){let e=this.g.get(t.channelName);e||(e=[],this.g.set(t.channelName,e));const s=setTimeout(()=>{console.error(`Unknown channel: ${t.channelName}`),t.type===100&&this.m({id:t.id,data:{name:"Unknown channel",message:`Channel name '${t.channelName}' timed out after ${this.l}ms`,stack:void 0},type:202})},this.l);e.push({request:t,timeoutTimer:s})}w(t){const e=this.g.get(t);if(e){for(const s of e)switch(clearTimeout(s.timeoutTimer),s.request.type){case 100:this.s(s.request);break;case 102:this.t(s.request);break}this.g.delete(t)}}dispose(){this.f&&(this.f.dispose(),this.f=null),Jt(this.d.values()),this.d.clear()}},Os;(function(t){t[t.LocalSide=0]="LocalSide",t[t.OtherSide=1]="OtherSide"})(Os||(Os={}));var Ns=class{constructor(t,e=null){this.l=t,this.a=!1,this.b=xt.Uninitialized,this.d=new Set,this.f=new Map,this.g=0,this.k=new I,this.onDidInitialize=this.k.event,this.h=this.l.onMessage(s=>this.s(s)),this.j=e}getChannel(t){const e=this;return{call(s,i,r){return e.a?Promise.reject(new lt):e.m(t,s,i,r)},listen(s,i){return e.a?R.None:e.o(t,s,i)}}}m(t,e,s,i=Yt.None){const r=this.g++,o={id:r,type:100,channelName:t,name:e,arg:s};if(i.isCancellationRequested)return Promise.reject(new lt);let a,h;return new Promise((f,l)=>{if(i.isCancellationRequested)return l(new lt);const u=()=>{const y=E=>{switch(E.type){case 201:this.f.delete(r),f(E.data);break;case 202:{this.f.delete(r);const O=new Error(E.data.message);O.stack=Array.isArray(E.data.stack)?E.data.stack.join(`
`):E.data.stack,O.name=E.data.name,l(O);break}case 203:this.f.delete(r),l(E.data);break}};this.f.set(r,y),this.p(o)};let d=null;this.b===xt.Idle?u():(d=u1(y=>this.u()),d.then(()=>{d=null,u()}));const v=()=>{d?(d.cancel(),d=null):this.p({id:r,type:101}),l(new lt)};a=i.onCancellationRequested(v),h={dispose:Ye(()=>{v(),a.dispose()})},this.d.add(h)}).finally(()=>{a?.dispose(),this.d.delete(h)})}o(t,e,s){const i=this.g++,n={id:i,type:102,channelName:t,name:e,arg:s};let o=null;const a=new I({onWillAddFirstListener:()=>{const c=()=>{this.d.add(a),this.p(n)};this.b===xt.Idle?c():(o=u1(f=>this.u()),o.then(()=>{o=null,c()}))},onDidRemoveLastListener:()=>{o?(o.cancel(),o=null):(this.d.delete(a),this.p({id:i,type:103}))}}),h=c=>a.fire(c.data);return this.f.set(i,h),a.event}p(t){switch(t.type){case 100:case 102:{const e=this.q([t.type,t.id,t.channelName,t.name],t.arg);this.j?.logOutgoing(e,t.id,0,`${Ut(t.type)}: ${t.channelName}.${t.name}`,t.arg);return}case 101:case 103:{const e=this.q([t.type,t.id]);this.j?.logOutgoing(e,t.id,0,Ut(t.type));return}}}q(t,e=void 0){const s=new Rs;return te(s,t),te(s,e),this.r(s.buffer)}r(t){try{return this.l.send(t),t.byteLength}catch{return 0}}s(t){const e=new m1(t),s=Bt(e),i=Bt(e),r=s[0];switch(r){case 200:return this.j?.logIncoming(t.byteLength,0,0,Pe(r)),this.t({type:s[0]});case 201:case 202:case 204:case 203:return this.j?.logIncoming(t.byteLength,s[1],0,Pe(r),i),this.t({type:s[0],id:s[1],data:i})}}t(t){if(t.type===200){this.b=xt.Idle,this.k.fire();return}this.f.get(t.id)?.(t)}get onDidInitializePromise(){return R.toPromise(this.onDidInitialize)}u(){return this.b===xt.Idle?Promise.resolve():this.onDidInitializePromise}dispose(){this.a=!0,this.h&&(this.h.dispose(),this.h=null),Jt(this.d.values()),this.d.clear()}};__decorate([co],Ns.prototype,"onDidInitializePromise",null);var fo=class{get connections(){const t=[];return this.f.forEach(e=>t.push(e)),t}constructor(t,e,s){this.a=new Map,this.f=new Set,this.g=new I,this.onDidAddConnection=this.g.event,this.h=new I,this.onDidRemoveConnection=this.h.event,this.j=new st,this.j.add(t(({protocol:i,onDidClientDisconnect:r})=>{const n=R.once(i.onMessage);this.j.add(n(o=>{const a=new m1(o),h=Bt(a),c=new Is(i,h,e,s),f=new Ns(i,e);this.a.forEach((u,d)=>c.registerChannel(d,u));const l={channelServer:c,channelClient:f,ctx:h};this.f.add(l),this.g.fire(l),this.j.add(r(()=>{c.dispose(),f.dispose(),this.f.delete(l),this.h.fire(l)}))}))}))}getChannel(t,e){const s=this;return{call(i,r,n){let o;if(q1(e)){const h=sr(s.connections.filter(e));o=h?Promise.resolve(h):R.toPromise(R.filter(s.onDidAddConnection,e))}else o=e.routeCall(s,i,r);const a=o.then(h=>h.channelClient.getChannel(t));return js(a).call(i,r,n)},listen(i,r){if(q1(e))return s.k(t,e,i,r);const n=e.routeEvent(s,i,r).then(o=>o.channelClient.getChannel(t));return js(n).listen(i,r)}}}k(t,e,s,i){const r=this;let n;const o=new I({onWillAddFirstListener:()=>{n=new st;const a=new Ir,h=new Map,c=l=>{const d=l.channelClient.getChannel(t).listen(s,i),v=a.add(d);h.set(l,v)},f=l=>{const u=h.get(l);u&&(u.dispose(),h.delete(l))};r.connections.filter(e).forEach(c),R.filter(r.onDidAddConnection,e)(c,void 0,n),r.onDidRemoveConnection(f,void 0,n),a.event(o.fire,o,n),n.add(a)},onDidRemoveLastListener:()=>{n?.dispose(),n=void 0}});return r.j.add(o),o.event}registerChannel(t,e){this.a.set(t,e);for(const s of this.f)s.channelServer.registerChannel(t,e)}dispose(){this.j.dispose();for(const t of this.f)t.channelClient.dispose(),t.channelServer.dispose();this.f.clear(),this.a.clear(),this.g.dispose(),this.h.dispose()}};function js(t){return{call(e,s,i){return t.then(r=>r.call(e,s,i))},listen(e,s){const i=new Or;return t.then(r=>i.input=r.listen(e,s)),i.event}}}var b1;(function(t){function e(n,o,a){const h=n,c=a&&a.disableMarshalling,f=new Map;for(const l in h)i(l)&&f.set(l,R.buffer(h[l],!0,void 0,o));return new class{listen(l,u,d){const v=f.get(u);if(v)return v;const y=h[u];if(typeof y=="function"){if(r(u))return y.call(h,d);if(i(u))return f.set(u,R.buffer(h[u],!0,void 0,o)),f.get(u)}throw new Vt(`Event not found: ${u}`)}call(l,u,d){const v=h[u];if(typeof v=="function"){if(!c&&Array.isArray(d))for(let E=0;E<d.length;E++)d[E]=De(d[E]);let y=v.apply(h,d);return y instanceof Promise||(y=Promise.resolve(y)),y}throw new Vt(`Method not found: ${u}`)}}}t.fromService=e;function s(n,o){const a=o&&o.disableMarshalling;return new Proxy({},{get(h,c){if(typeof c=="string")return o?.properties?.has(c)?o.properties.get(c):r(c)?function(f){return n.listen(c,f)}:i(c)?n.listen(c):async function(...f){let l;o&&!pr(o.context)?l=[o.context,...f]:l=f;const u=await n.call(c,l);return a?u:De(u)};throw new Vt(`Property not found: ${String(c)}`)}})}t.toService=s;function i(n){return n[0]==="o"&&n[1]==="n"&&as(n.charCodeAt(2))}function r(n){return/^onDynamic/.test(n)&&as(n.charCodeAt(9))}})(b1||(b1={}));import"child_process";import"fs";var Ms;(function(t){t[t.stdout=0]="stdout",t[t.stderr=1]="stderr"})(Ms||(Ms={}));var Ts;(function(t){t[t.Success=0]="Success",t[t.Unknown=1]="Unknown",t[t.AccessDenied=2]="AccessDenied",t[t.ProcessNotFound=3]="ProcessNotFound"})(Ts||(Ts={}));import*as P from"fs";import{tmpdir as uo}from"os";import{promisify as ee}from"util";var go=new Je(1e4);function ke(t){return wo(t,"NFC",go)}var l0=new Je(1e4),po=/[^\u0000-\u0080]/;function wo(t,e,s){if(!t)return t;const i=s.get(t);if(i)return i;let r;return po.test(t)?r=t.normalize(e):r=t,s.set(t,r),r}var se;(function(t){t[t.UNLINK=0]="UNLINK",t[t.MOVE=1]="MOVE"})(se||(se={}));async function Us(t,e=se.UNLINK,s){if(Cn(t))throw new Error("rimraf - will refuse to recursively delete root");return e===se.UNLINK?y1(t):vo(t,s)}async function vo(t,e=ws(uo())){try{try{await P.promises.rename(t,e)}catch(s){return s.code==="ENOENT"?void 0:y1(t)}y1(e).catch(s=>{})}catch(s){if(s.code!=="ENOENT")throw s}}async function y1(t){return P.promises.rm(t,{recursive:!0,force:!0,maxRetries:3})}async function _e(t,e){return bo(await(e?mo(t):P.promises.readdir(t)))}async function mo(t){try{return await P.promises.readdir(t,{withFileTypes:!0})}catch(i){console.warn("[node.js fs] readdir with filetypes failed with error: ",i)}const e=[],s=await _e(t);for(const i of s){let r=!1,n=!1,o=!1;try{const a=await P.promises.lstat(rt(t,i));r=a.isFile(),n=a.isDirectory(),o=a.isSymbolicLink()}catch(a){console.warn("[node.js fs] unexpected error from lstat after readdir: ",a)}e.push({name:i,isFile:()=>r,isDirectory:()=>n,isSymbolicLink:()=>o})}return e}function bo(t){return t.map(e=>typeof e=="string"?Nt?ke(e):e:(e.name=Nt?ke(e.name):e.name,e))}async function yo(t){const e=await _e(t),s=[];for(const i of e)await ie.existsDirectory(rt(t,i))&&s.push(i);return s}var ie;(function(t){async function e(r){let n;try{if(n=await P.promises.lstat(r),!n.isSymbolicLink())return{stat:n}}catch{}try{return{stat:await P.promises.stat(r),symbolicLink:n?.isSymbolicLink()?{dangling:!1}:void 0}}catch(o){if(o.code==="ENOENT"&&n)return{stat:n,symbolicLink:{dangling:!0}};if(Y&&o.code==="EACCES")try{return{stat:await P.promises.stat(await P.promises.readlink(r)),symbolicLink:{dangling:!1}}}catch(a){if(a.code==="ENOENT"&&n)return{stat:n,symbolicLink:{dangling:!0}};throw a}throw o}}t.stat=e;async function s(r){try{const{stat:n,symbolicLink:o}=await t.stat(r);return n.isFile()&&o?.dangling!==!0}catch{}return!1}t.existsFile=s;async function i(r){try{const{stat:n,symbolicLink:o}=await t.stat(r);return n.isDirectory()&&o?.dangling!==!0}catch{}return!1}t.existsDirectory=i})(ie||(ie={}));var $o=new Zn;function Eo(t,e,s){return $o.queueFor(F.file(t),()=>{const i=xo(s);return new Promise((r,n)=>Co(t,e,i,o=>o?n(o):r()))},Bn)}var Fs=!0;function Ao(t){Fs=t}function Co(t,e,s,i){if(!Fs)return P.writeFile(t,e,{mode:s.mode,flag:s.flag},i);P.open(t,s.flag,s.mode,(r,n)=>{if(r)return i(r);P.writeFile(n,e,o=>{if(o)return P.close(n,()=>i(o));P.fdatasync(n,a=>(a&&(console.warn("[node.js fs] fdatasync is now disabled for this session because it failed: ",a),Ao(!1)),P.close(n,h=>i(h))))})})}function xo(t){return t?{mode:typeof t.mode=="number"?t.mode:438,flag:typeof t.flag=="string"?t.flag:"w"}:{mode:438,flag:"w"}}async function Lo(t,e,s=6e4){if(t!==e)try{Y&&typeof s=="number"?await Ws(t,e,Date.now(),s):await P.promises.rename(t,e)}catch(i){if(t.toLowerCase()!==e.toLowerCase()&&i.code==="EXDEV"||t.endsWith("."))await Bs(t,e,{preserveSymlinks:!1}),await Us(t,se.MOVE);else throw i}}async function Ws(t,e,s,i,r=0){try{return await P.promises.rename(t,e)}catch(n){if(n.code!=="EACCES"&&n.code!=="EPERM"&&n.code!=="EBUSY")throw n;if(Date.now()-s>=i)throw console.error(`[node.js fs] rename failed after ${r} retries with error: ${n}`),n;if(r===0){let o=!1;try{const{stat:a}=await ie.stat(e);a.isFile()||(o=!0)}catch{}if(o)throw n}return await Ss(Math.min(100,r*10)),Ws(t,e,s,i,r+1)}}async function Bs(t,e,s){return zs(t,e,{root:{source:t,target:e},options:s,handledSourcePaths:new Set})}var qs=511;async function zs(t,e,s){if(s.handledSourcePaths.has(t))return;s.handledSourcePaths.add(t);const{stat:i,symbolicLink:r}=await ie.stat(t);if(r){if(s.options.preserveSymlinks)try{return await Po(t,e,s)}catch{}if(r.dangling)return}return i.isDirectory()?So(t,e,i.mode&qs,s):Do(t,e,i.mode&qs)}async function So(t,e,s,i){await P.promises.mkdir(e,{recursive:!0,mode:s});const r=await _e(t);for(const n of r)await zs(rt(t,n),rt(e,n),i)}async function Do(t,e,s){await P.promises.copyFile(t,e),await P.promises.chmod(e,s)}async function Po(t,e,s){let i=await P.promises.readlink(t);jt(i,s.root.source,!z)&&(i=rt(s.root.target,i.substr(s.root.source.length+1))),await P.promises.symlink(i,e)}var St=new class{get read(){return(t,e,s,i,r)=>new Promise((n,o)=>{P.read(t,e,s,i,r,(a,h,c)=>a?o(a):n({bytesRead:h,buffer:c}))})}get write(){return(t,e,s,i,r)=>new Promise((n,o)=>{P.write(t,e,s,i,r,(a,h,c)=>a?o(a):n({bytesWritten:h,buffer:c}))})}get fdatasync(){return ee(P.fdatasync)}get open(){return ee(P.open)}get close(){return ee(P.close)}get realpath(){return ee(P.realpath)}get ftruncate(){return ee(P.ftruncate)}async exists(t){try{return await P.promises.access(t),!0}catch{return!1}}get readdir(){return _e}get readDirsInDir(){return yo}get writeFile(){return Eo}get rm(){return Us}get rename(){return Lo}get copy(){return Bs}},ko=class extends Is{constructor(t){super({send:e=>{try{process.send?.(e.buffer.toString("base64"))}catch{}},onMessage:R.fromNodeEventEmitter(process,"message",e=>at.wrap(Buffer.from(e,"base64")))},t),process.once("disconnect",()=>this.dispose())}};function Hs(t){return!!t.parentPort}var _o=class{constructor(t){this.a=t,this.onMessage=R.fromNodeEventEmitter(this.a,"message",e=>e.data?at.wrap(e.data):at.alloc(0)),t.start()}send(t){this.a.postMessage(t.buffer)}disconnect(){this.a.close()}},Ro=class Ui extends fo{static b(e){wr(Hs(process),"Electron Utility Process");const s=new I;return process.parentPort.on("message",i=>{if(e?.handledClientConnection(i))return;const r=i.ports.at(0);r&&s.fire(r)}),R.map(s.event,i=>({protocol:new _o(i),onDidClientDisconnect:R.fromNodeEventEmitter(i,"close")}))}constructor(e){super(Ui.b(e))}},qt="**",Vs="/",Re="[/\\\\]",Ie="[^/\\\\]",Io=/\//g;function Qs(t,e){switch(t){case 0:return"";case 1:return`${Ie}*?`;default:return`(?:${Re}|${Ie}+${Re}${e?`|${Re}${Ie}+`:""})*?`}}function Ks(t,e){if(!t)return[];const s=[];let i=!1,r=!1,n="";for(const o of t){switch(o){case e:if(!i&&!r){s.push(n),n="";continue}break;case"{":i=!0;break;case"}":i=!1;break;case"[":r=!0;break;case"]":r=!1;break}n+=o}return n&&s.push(n),s}function Gs(t){if(!t)return"";let e="";const s=Ks(t,Vs);if(s.every(i=>i===qt))e=".*";else{let i=!1;s.forEach((r,n)=>{if(r===qt){if(i)return;e+=Qs(2,n===s.length-1)}else{let o=!1,a="",h=!1,c="";for(const f of r){if(f!=="}"&&o){a+=f;continue}if(h&&(f!=="]"||!c)){let l;f==="-"?l=f:(f==="^"||f==="!")&&!c?l="^":f===Vs?l="":l=rs(f),c+=l;continue}switch(f){case"{":o=!0;continue;case"[":h=!0;continue;case"}":{const u=`(?:${Ks(a,",").map(d=>Gs(d)).join("|")})`;e+=u,o=!1,a="";break}case"]":{e+="["+c+"]",h=!1,c="";break}case"?":e+=Ie;continue;case"*":e+=Qs(1);continue;default:e+=rs(f)}}n<s.length-1&&(s[n+1]!==qt||n+2<s.length)&&(e+=Re)}i=r===qt})}return e}var Oo=/^\*\*\/\*\.[\w\.-]+$/,No=/^\*\*\/([\w\.-]+)\/?$/,jo=/^{\*\*\/\*?[\w\.-]+\/?(,\*\*\/\*?[\w\.-]+\/?)*}$/,Mo=/^{\*\*\/\*?[\w\.-]+(\/(\*\*)?)?(,\*\*\/\*?[\w\.-]+(\/(\*\*)?)?)*}$/,To=/^\*\*((\/[\w\.-]+)+)\/?$/,Uo=/^([\w\.-]+(\/[\w\.-]+)*)\/?$/,Js=new Je(1e4),Ys=function(){return!1},ht=function(){return null};function $1(t,e){if(!t)return ht;let s;typeof t!="string"?s=t.pattern:s=t,s=s.trim();const i=`${s}_${!!e.trimForExclusions}`;let r=Js.get(i);if(r)return Zs(r,t);let n;return Oo.test(s)?r=Fo(s.substr(4),s):(n=No.exec(E1(s,e)))?r=Wo(n[1],s):(e.trimForExclusions?Mo:jo).test(s)?r=Bo(s,e):(n=To.exec(E1(s,e)))?r=Xs(n[1].substr(1),s,!0):(n=Uo.exec(E1(s,e)))?r=Xs(n[1],s,!1):r=qo(s),Js.set(i,r),Zs(r,t)}function Zs(t,e){if(typeof e=="string")return t;const s=function(i,r){return jt(i,e.base,!z)?t(pn(i.substr(e.base.length),nt),r):null};return s.allBasenames=t.allBasenames,s.allPaths=t.allPaths,s.basenames=t.basenames,s.patterns=t.patterns,s}function E1(t,e){return e.trimForExclusions&&t.endsWith("/**")?t.substr(0,t.length-2):t}function Fo(t,e){return function(s,i){return typeof s=="string"&&s.endsWith(t)?e:null}}function Wo(t,e){const s=`/${t}`,i=`\\${t}`,r=function(o,a){return typeof o!="string"?null:a?a===t?e:null:o===t||o.endsWith(s)||o.endsWith(i)?e:null},n=[t];return r.basenames=n,r.patterns=[e],r.allBasenames=n,r}function Bo(t,e){const s=ti(t.slice(1,-1).split(",").map(a=>$1(a,e)).filter(a=>a!==ht),t),i=s.length;if(!i)return ht;if(i===1)return s[0];const r=function(a,h){for(let c=0,f=s.length;c<f;c++)if(s[c](a,h))return t;return null},n=s.find(a=>!!a.allBasenames);n&&(r.allBasenames=n.allBasenames);const o=s.reduce((a,h)=>h.allPaths?a.concat(h.allPaths):a,[]);return o.length&&(r.allPaths=o),r}function Xs(t,e,s){const i=nt===S.sep,r=i?t:t.replace(Io,nt),n=nt+r,o=S.sep+t;let a;return s?a=function(h,c){return typeof h=="string"&&(h===r||h.endsWith(n)||!i&&(h===t||h.endsWith(o)))?e:null}:a=function(h,c){return typeof h=="string"&&(h===r||!i&&h===t)?e:null},a.allPaths=[(s?"*/":"./")+t],a}function qo(t){try{const e=new RegExp(`^${Gs(t)}$`);return function(s){return e.lastIndex=0,typeof s=="string"&&e.test(s)?t:null}}catch{return ht}}function zo(t,e={}){if(!t)return Ys;if(typeof t=="string"||Ho(t)){const s=$1(t,e);if(s===ht)return Ys;const i=function(r,n){return!!s(r,n)};return s.allBasenames&&(i.allBasenames=s.allBasenames),s.allPaths&&(i.allPaths=s.allPaths),i}return Vo(t,e)}function Ho(t){const e=t;return e?typeof e.base=="string"&&typeof e.pattern=="string":!1}function Vo(t,e){const s=ti(Object.getOwnPropertyNames(t).map(a=>Qo(a,t[a],e)).filter(a=>a!==ht)),i=s.length;if(!i)return ht;if(!s.some(a=>!!a.requiresSiblings)){if(i===1)return s[0];const a=function(f,l){let u;for(let d=0,v=s.length;d<v;d++){const y=s[d](f,l);if(typeof y=="string")return y;f1(y)&&(u||(u=[]),u.push(y))}return u?(async()=>{for(const d of u){const v=await d;if(typeof v=="string")return v}return null})():null},h=s.find(f=>!!f.allBasenames);h&&(a.allBasenames=h.allBasenames);const c=s.reduce((f,l)=>l.allPaths?f.concat(l.allPaths):f,[]);return c.length&&(a.allPaths=c),a}const r=function(a,h,c){let f,l;for(let u=0,d=s.length;u<d;u++){const v=s[u];v.requiresSiblings&&c&&(h||(h=Ee(a)),f||(f=h.substr(0,h.length-un(a).length)));const y=v(a,h,f,c);if(typeof y=="string")return y;f1(y)&&(l||(l=[]),l.push(y))}return l?(async()=>{for(const u of l){const d=await u;if(typeof d=="string")return d}return null})():null},n=s.find(a=>!!a.allBasenames);n&&(r.allBasenames=n.allBasenames);const o=s.reduce((a,h)=>h.allPaths?a.concat(h.allPaths):a,[]);return o.length&&(r.allPaths=o),r}function Qo(t,e,s){if(e===!1)return ht;const i=$1(t,s);if(i===ht)return ht;if(typeof e=="boolean")return i;if(e){const r=e.when;if(typeof r=="string"){const n=(o,a,h,c)=>{if(!c||!i(o,a))return null;const f=r.replace("$(basename)",()=>h),l=c(f);return f1(l)?l.then(u=>u?t:null):l?t:null};return n.requiresSiblings=!0,n}}return i}function ti(t,e){const s=t.filter(a=>!!a.basenames);if(s.length<2)return t;const i=s.reduce((a,h)=>{const c=h.basenames;return c?a.concat(c):a},[]);let r;if(e){r=[];for(let a=0,h=i.length;a<h;a++)r.push(e)}else r=s.reduce((a,h)=>{const c=h.patterns;return c?a.concat(c):a},[]);const n=function(a,h){if(typeof a!="string")return null;if(!h){let f;for(f=a.length;f>0;f--){const l=a.charCodeAt(f-1);if(l===47||l===92)break}h=a.substr(f)}const c=i.indexOf(h);return c!==-1?r[c]:null};n.basenames=i,n.patterns=r,n.allBasenames=i;const o=t.filter(a=>!a.basenames);return o.push(n),o}function Oe(t,e){return tr(t,e,(s,i)=>typeof s=="string"&&typeof i=="string"?s===i:typeof s!="string"&&typeof i!="string"?s.base===i.base&&s.pattern===i.pattern:!1)}var Ko=class{constructor(){this.b="",this.c=0}reset(t){return this.b=t,this.c=0,this}next(){return this.c+=1,this}hasNext(){return this.c<this.b.length-1}cmp(t){const e=t.charCodeAt(0),s=this.b.charCodeAt(this.c);return e-s}value(){return this.b[this.c]}},Go=class{constructor(t=!0){this.e=t}reset(t){return this.b=t,this.c=0,this.d=0,this.next()}hasNext(){return this.d<this.b.length}next(){this.c=this.d;let t=!0;for(;this.d<this.b.length;this.d++)if(this.b.charCodeAt(this.d)===46)if(t)this.c++;else break;else t=!1;return this}cmp(t){return this.e?a1(t,this.b,0,t.length,this.c,this.d):Xt(t,this.b,0,t.length,this.c,this.d)}value(){return this.b.substring(this.c,this.d)}},ei=class{constructor(t=!0,e=!0){this.f=t,this.g=e}reset(t){this.d=0,this.e=0,this.b=t,this.c=t.length;for(let e=t.length-1;e>=0;e--,this.c--){const s=this.b.charCodeAt(e);if(!(s===47||this.f&&s===92))break}return this.next()}hasNext(){return this.e<this.c}next(){this.d=this.e;let t=!0;for(;this.e<this.c;this.e++){const e=this.b.charCodeAt(this.e);if(e===47||this.f&&e===92)if(t)this.d++;else break;else t=!1}return this}cmp(t){return this.g?a1(t,this.b,0,t.length,this.d,this.e):Xt(t,this.b,0,t.length,this.d,this.e)}value(){return this.b.substring(this.d,this.e)}},si;(function(t){t[t.Scheme=1]="Scheme",t[t.Authority=2]="Authority",t[t.Path=3]="Path",t[t.Query=4]="Query",t[t.Fragment=5]="Fragment"})(si||(si={}));var Jo=class{constructor(t,e){this.f=t,this.g=e,this.d=[],this.e=0}reset(t){return this.c=t,this.d=[],this.c.scheme&&this.d.push(1),this.c.authority&&this.d.push(2),this.c.path&&(this.b=new ei(!1,!this.f(t)),this.b.reset(t.path),this.b.value()&&this.d.push(3)),this.g(t)||(this.c.query&&this.d.push(4),this.c.fragment&&this.d.push(5)),this.e=0,this}next(){return this.d[this.e]===3&&this.b.hasNext()?this.b.next():this.e+=1,this}hasNext(){return this.d[this.e]===3&&this.b.hasNext()||this.e<this.d.length-1}cmp(t){if(this.d[this.e]===1)return ns(t,this.c.scheme);if(this.d[this.e]===2)return ns(t,this.c.authority);if(this.d[this.e]===3)return this.b.cmp(t);if(this.d[this.e]===4)return o1(t,this.c.query);if(this.d[this.e]===5)return o1(t,this.c.fragment);throw new Error}value(){if(this.d[this.e]===1)return this.c.scheme;if(this.d[this.e]===2)return this.c.authority;if(this.d[this.e]===3)return this.b.value();if(this.d[this.e]===4)return this.c.query;if(this.d[this.e]===5)return this.c.fragment;throw new Error}},Dt=class I1{static{this.Val=Symbol("undefined_placeholder")}static wrap(e){return e===void 0?I1.Val:e}static unwrap(e){return e===I1.Val?void 0:e}},Ne=class{constructor(){this.height=1,this.value=void 0,this.key=void 0,this.left=void 0,this.mid=void 0,this.right=void 0}isEmpty(){return!this.left&&!this.mid&&!this.right&&this.value===void 0}rotateLeft(){const t=this.right;return this.right=t.left,t.left=this,this.updateHeight(),t.updateHeight(),t}rotateRight(){const t=this.left;return this.left=t.right,t.right=this,this.updateHeight(),t.updateHeight(),t}updateHeight(){this.height=1+Math.max(this.heightLeft,this.heightRight)}balanceFactor(){return this.heightRight-this.heightLeft}get heightLeft(){return this.left?.height??0}get heightRight(){return this.right?.height??0}},ii;(function(t){t[t.Left=-1]="Left",t[t.Mid=0]="Mid",t[t.Right=1]="Right"})(ii||(ii={}));var je=class he{static forUris(e=()=>!1,s=()=>!1){return new he(new Jo(e,s))}static forPaths(e=!1){return new he(new ei(void 0,!e))}static forStrings(){return new he(new Ko)}static forConfigKeys(){return new he(new Go)}constructor(e){this.b=e}clear(){this.c=void 0}fill(e,s){if(s){const i=s.slice(0);N1(i);for(const r of i)this.set(r,e)}else{const i=e.slice(0);N1(i);for(const r of i)this.set(r[0],r[1])}}set(e,s){const i=this.b.reset(e);let r;this.c||(this.c=new Ne,this.c.segment=i.value());const n=[];for(r=this.c;;){const a=i.cmp(r.segment);if(a>0)r.left||(r.left=new Ne,r.left.segment=i.value()),n.push([-1,r]),r=r.left;else if(a<0)r.right||(r.right=new Ne,r.right.segment=i.value()),n.push([1,r]),r=r.right;else if(i.hasNext())i.next(),r.mid||(r.mid=new Ne,r.mid.segment=i.value()),n.push([0,r]),r=r.mid;else break}const o=Dt.unwrap(r.value);r.value=Dt.wrap(s),r.key=e;for(let a=n.length-1;a>=0;a--){const h=n[a][1];h.updateHeight();const c=h.balanceFactor();if(c<-1||c>1){const f=n[a][0],l=n[a+1][0];if(f===1&&l===1)n[a][1]=h.rotateLeft();else if(f===-1&&l===-1)n[a][1]=h.rotateRight();else if(f===1&&l===-1)h.right=n[a+1][1]=n[a+1][1].rotateRight(),n[a][1]=h.rotateLeft();else if(f===-1&&l===1)h.left=n[a+1][1]=n[a+1][1].rotateLeft(),n[a][1]=h.rotateRight();else throw new Error;if(a>0)switch(n[a-1][0]){case-1:n[a-1][1].left=n[a][1];break;case 1:n[a-1][1].right=n[a][1];break;case 0:n[a-1][1].mid=n[a][1];break}else this.c=n[0][1]}}return o}get(e){return Dt.unwrap(this.d(e)?.value)}d(e){const s=this.b.reset(e);let i=this.c;for(;i;){const r=s.cmp(i.segment);if(r>0)i=i.left;else if(r<0)i=i.right;else if(s.hasNext())s.next(),i=i.mid;else break}return i}has(e){const s=this.d(e);return!(s?.value===void 0&&s?.mid===void 0)}delete(e){return this.e(e,!1)}deleteSuperstr(e){return this.e(e,!0)}e(e,s){const i=this.b.reset(e),r=[];let n=this.c;for(;n;){const o=i.cmp(n.segment);if(o>0)r.push([-1,n]),n=n.left;else if(o<0)r.push([1,n]),n=n.right;else if(i.hasNext())i.next(),r.push([0,n]),n=n.mid;else break}if(n){if(s?(n.left=void 0,n.mid=void 0,n.right=void 0,n.height=1):(n.key=void 0,n.value=void 0),!n.mid&&!n.value)if(n.left&&n.right){const o=[[1,n]],a=this.f(n.right,o);if(a.key){n.key=a.key,n.value=a.value,n.segment=a.segment;const h=a.right;if(o.length>1){const[f,l]=o[o.length-1];switch(f){case-1:l.left=h;break;case 0:B1(!1);case 1:B1(!1)}}else n.right=h;const c=this.g(o);if(r.length>0){const[f,l]=r[r.length-1];switch(f){case-1:l.left=c;break;case 0:l.mid=c;break;case 1:l.right=c;break}}else this.c=c}}else{const o=n.left??n.right;if(r.length>0){const[a,h]=r[r.length-1];switch(a){case-1:h.left=o;break;case 0:h.mid=o;break;case 1:h.right=o;break}}else this.c=o}this.c=this.g(r)??this.c}}f(e,s){for(;e.left;)s.push([-1,e]),e=e.left;return e}g(e){for(let s=e.length-1;s>=0;s--){const i=e[s][1];i.updateHeight();const r=i.balanceFactor();if(r>1?(i.right.balanceFactor()>=0||(i.right=i.right.rotateRight()),e[s][1]=i.rotateLeft()):r<-1&&(i.left.balanceFactor()<=0||(i.left=i.left.rotateLeft()),e[s][1]=i.rotateRight()),s>0)switch(e[s-1][0]){case-1:e[s-1][1].left=e[s][1];break;case 1:e[s-1][1].right=e[s][1];break;case 0:e[s-1][1].mid=e[s][1];break}else return e[0][1]}}findSubstr(e){const s=this.b.reset(e);let i=this.c,r;for(;i;){const n=s.cmp(i.segment);if(n>0)i=i.left;else if(n<0)i=i.right;else if(s.hasNext())s.next(),r=Dt.unwrap(i.value)||r,i=i.mid;else break}return i&&Dt.unwrap(i.value)||r}findSuperstr(e){return this.h(e,!1)}h(e,s){const i=this.b.reset(e);let r=this.c;for(;r;){const n=i.cmp(r.segment);if(n>0)r=r.left;else if(n<0)r=r.right;else if(i.hasNext())i.next(),r=r.mid;else return r.mid?this.j(r.mid):s?Dt.unwrap(r.value):void 0}}hasElementOrSubtree(e){return this.h(e,!0)!==void 0}forEach(e){for(const[s,i]of this)e(i,s)}*[Symbol.iterator](){yield*this.j(this.c)}j(e){const s=[];return this.l(e,s),s[Symbol.iterator]()}l(e,s){e&&(e.left&&this.l(e.left,s),e.value!==void 0&&s.push([e.key,Dt.unwrap(e.value)]),e.mid&&this.l(e.mid,s),e.right&&this.l(e.right,s))}_isBalanced(){const e=s=>{if(!s)return!0;const i=s.balanceFactor();return i<-1||i>1?!1:e(s.left)&&e(s.right)};return e(this.c)}},wt;(function(t){t.serviceIds=new Map,t.DI_TARGET="$di$target",t.DI_DEPENDENCIES="$di$dependencies";function e(s){return s[t.DI_DEPENDENCIES]||[]}t.getServiceDependencies=e})(wt||(wt={}));var f0=ri("instantiationService");function Yo(t,e,s){e[wt.DI_TARGET]===e?e[wt.DI_DEPENDENCIES].push({id:t,index:s}):(e[wt.DI_DEPENDENCIES]=[{id:t,index:s}],e[wt.DI_TARGET]=e)}function ri(t){if(wt.serviceIds.has(t))return wt.serviceIds.get(t);const e=function(s,i,r){if(arguments.length!==3)throw new Error("@IServiceName-decorator can only be used to decorate a parameter");Yo(e,s,r)};return e.toString=()=>t,wt.serviceIds.set(t,e),e}var u0=ri("fileService"),ni;(function(t){t[t.Unknown=0]="Unknown",t[t.File=1]="File",t[t.Directory=2]="Directory",t[t.SymbolicLink=64]="SymbolicLink"})(ni||(ni={}));var oi;(function(t){t[t.Readonly=1]="Readonly",t[t.Locked=2]="Locked"})(oi||(oi={}));var ai;(function(t){t[t.UPDATED=2]="UPDATED",t[t.ADDED=4]="ADDED",t[t.DELETED=8]="DELETED"})(ai||(ai={}));var ci;(function(t){t[t.None=0]="None",t[t.FileReadWrite=2]="FileReadWrite",t[t.FileOpenReadWriteClose=4]="FileOpenReadWriteClose",t[t.FileReadStream=16]="FileReadStream",t[t.FileFolderCopy=8]="FileFolderCopy",t[t.PathCaseSensitive=1024]="PathCaseSensitive",t[t.Readonly=2048]="Readonly",t[t.Trash=4096]="Trash",t[t.FileWriteUnlock=8192]="FileWriteUnlock",t[t.FileAtomicRead=16384]="FileAtomicRead",t[t.FileAtomicWrite=32768]="FileAtomicWrite",t[t.FileAtomicDelete=65536]="FileAtomicDelete",t[t.FileClone=131072]="FileClone"})(ci||(ci={}));var hi;(function(t){t.FileExists="EntryExists",t.FileNotFound="EntryNotFound",t.FileNotADirectory="EntryNotADirectory",t.FileIsADirectory="EntryIsADirectory",t.FileExceedsStorageQuota="EntryExceedsStorageQuota",t.FileTooLarge="EntryTooLarge",t.FileWriteLocked="EntryWriteLocked",t.NoPermissions="NoPermissions",t.Unavailable="Unavailable",t.Unknown="Unknown"})(hi||(hi={}));var li;(function(t){t[t.CREATE=0]="CREATE",t[t.DELETE=1]="DELETE",t[t.MOVE=2]="MOVE",t[t.COPY=3]="COPY",t[t.WRITE=4]="WRITE"})(li||(li={}));var fi;(function(t){t[t.UPDATED=0]="UPDATED",t[t.ADDED=1]="ADDED",t[t.DELETED=2]="DELETED"})(fi||(fi={}));var d0=class He{static{this.a=null}constructor(e,s){this.c=s,this.b=void 0,this.d=new Ct(()=>{const i=je.forUris(()=>this.c);return i.fill(this.rawAdded.map(r=>[r,!0])),i}),this.f=new Ct(()=>{const i=je.forUris(()=>this.c);return i.fill(this.rawUpdated.map(r=>[r,!0])),i}),this.g=new Ct(()=>{const i=je.forUris(()=>this.c);return i.fill(this.rawDeleted.map(r=>[r,!0])),i}),this.rawAdded=[],this.rawUpdated=[],this.rawDeleted=[];for(const i of e){switch(i.type){case 1:this.rawAdded.push(i.resource);break;case 0:this.rawUpdated.push(i.resource);break;case 2:this.rawDeleted.push(i.resource);break}this.b!==He.a&&(typeof i.cId=="number"?this.b===void 0?this.b=i.cId:this.b!==i.cId&&(this.b=He.a):this.b!==void 0&&(this.b=He.a))}}contains(e,...s){return this.h(e,{includeChildren:!1},...s)}affects(e,...s){return this.h(e,{includeChildren:!0},...s)}h(e,s,...i){if(!e)return!1;const r=i.length>0;return!!((!r||i.includes(1))&&(this.d.value.get(e)||s.includeChildren&&this.d.value.findSuperstr(e))||(!r||i.includes(0))&&(this.f.value.get(e)||s.includeChildren&&this.f.value.findSuperstr(e))||(!r||i.includes(2))&&(this.g.value.findSubstr(e)||s.includeChildren&&this.g.value.findSuperstr(e)))}gotAdded(){return this.rawAdded.length>0}gotDeleted(){return this.rawDeleted.length>0}gotUpdated(){return this.rawUpdated.length>0}correlates(e){return this.b===e}hasCorrelation(){return typeof this.b=="number"}};function Zo(t,e,s){return!t||!e||t===e||e.length>t.length?!1:(e.charAt(e.length-1)!==nt&&(e+=nt),s?hs(t,e):t.indexOf(e)===0)}var ui;(function(t){t[t.FILE_IS_DIRECTORY=0]="FILE_IS_DIRECTORY",t[t.FILE_NOT_FOUND=1]="FILE_NOT_FOUND",t[t.FILE_NOT_MODIFIED_SINCE=2]="FILE_NOT_MODIFIED_SINCE",t[t.FILE_MODIFIED_SINCE=3]="FILE_MODIFIED_SINCE",t[t.FILE_MOVE_CONFLICT=4]="FILE_MOVE_CONFLICT",t[t.FILE_WRITE_LOCKED=5]="FILE_WRITE_LOCKED",t[t.FILE_PERMISSION_DENIED=6]="FILE_PERMISSION_DENIED",t[t.FILE_TOO_LARGE=7]="FILE_TOO_LARGE",t[t.FILE_INVALID_PATH=8]="FILE_INVALID_PATH",t[t.FILE_NOT_DIRECTORY=9]="FILE_NOT_DIRECTORY",t[t.FILE_OTHER_ERROR=10]="FILE_OTHER_ERROR"})(ui||(ui={}));var di;(function(t){t[t.FILE=0]="FILE",t[t.FOLDER=1]="FOLDER",t[t.ROOT_FOLDER=2]="ROOT_FOLDER"})(di||(di={}));var g0=class H{static{this.KB=1024}static{this.MB=H.KB*H.KB}static{this.GB=H.MB*H.KB}static{this.TB=H.GB*H.KB}static formatSize(e){return ur(e)||(e=0),e<H.KB?it(2025,null,e.toFixed(0)):e<H.MB?it(2026,null,(e/H.KB).toFixed(2)):e<H.GB?it(2027,null,(e/H.MB).toFixed(2)):e<H.TB?it(2028,null,(e/H.GB).toFixed(2)):it(2029,null,(e/H.TB).toFixed(2))}};function gi(t){return typeof t.correlationId=="number"}function Me(t){return t.recursive===!0}var p0=class Fi extends X{static{this.a=5}constructor(e,s,i,r){super(),this.h=e,this.j=s,this.m=i,this.n=r,this.c=this.B(new Xe),this.f=void 0,this.g=0}s(){const e=new st;this.c.value=e,this.b=this.r(e),this.b.setVerboseLogging(this.m),e.add(this.b.onDidChangeFile(s=>this.h(s))),e.add(this.b.onDidLogMessage(s=>this.j(s))),e.add(this.b.onDidError(s=>this.t(s.error,s.request)))}t(e,s){this.u(e,s)?this.g<Fi.a&&this.f?(this.y(`restarting watcher after unexpected error: ${e}`),this.w(this.f)):this.y(`gave up attempting to restart watcher after unexpected error: ${e}`):this.y(e)}u(e,s){return!(!this.n.restartOnError||s||e.indexOf("No space left on device")!==-1||e.indexOf("EMFILE")!==-1)}w(e){this.g++,this.s(),this.watch(e)}async watch(e){this.f=e,await this.b?.watch(e)}async setVerboseLogging(e){this.m=e,await this.b?.setVerboseLogging(e)}y(e){this.j({type:"error",message:`[File Watcher (${this.n.type})] ${e}`})}z(e){this.j({type:"trace",message:`[File Watcher (${this.n.type})] ${e}`})}dispose(){return this.b=void 0,super.dispose()}};function pi(t){const e=new t2;for(const s of t)e.processEvent(s);return e.coalesce()}function Xo(t,e){return typeof e=="string"&&!e.startsWith(qt)&&!hn(e)?{base:t,pattern:e}:e}function Te(t,e){const s=[];for(const i of e)s.push(zo(Xo(t,i)));return s}var t2=class{constructor(){this.a=new Set,this.b=new Map}c(t){return z?t.resource.fsPath:t.resource.fsPath.toLowerCase()}processEvent(t){const e=this.b.get(this.c(t));let s=!1;if(e){const i=e.type,r=t.type;e.resource.fsPath!==t.resource.fsPath&&(t.type===2||t.type===1)?s=!0:i===1&&r===2?(this.b.delete(this.c(t)),this.a.delete(e)):i===2&&r===1?e.type=0:i===1&&r===0||(e.type=r)}else s=!0;s&&(this.a.add(t),this.b.set(this.c(t),t))}coalesce(){const t=[],e=[];return Array.from(this.a).filter(s=>s.type!==2?(t.push(s),!1):!0).sort((s,i)=>s.resource.fsPath.length-i.resource.fsPath.length).filter(s=>e.some(i=>Zo(s.resource.fsPath,i,!z))?!1:(e.push(s.resource.fsPath),!0)).concat(t)}};function wi(t,e){if(typeof e=="number")switch(t.type){case 1:return(e&4)===0;case 2:return(e&8)===0;case 0:return(e&2)===0}return!1}function vi(t){if(typeof t=="number"){const e=[];return t&4&&e.push("Added"),t&8&&e.push("Deleted"),t&2&&e.push("Updated"),e.length===0?"<all>":`[${e.join(", ")}]`}return"<none>"}import mi from"@parcel/watcher";import{promises as A1}from"fs";import{tmpdir as e2,homedir as s2}from"os";function C1(t,e){return e&&(t.stack||t.stacktrace)?it(110,null,yi(t),bi(t.stack)||bi(t.stacktrace)):yi(t)}function bi(t){return Array.isArray(t)?t.join(`
`):t}function yi(t){return t.code==="ERR_UNC_HOST_NOT_ALLOWED"?`${t.message}. Please update the 'security.allowedUNCHosts' setting if you want to allow this host.`:typeof t.code=="string"&&typeof t.errno=="number"&&typeof t.syscall=="string"?it(111,null,t.message):t.message||it(112,null)}function x1(t=null,e=!1){if(!t)return it(113,null);if(Array.isArray(t)){const s=er(t),i=x1(s[0],e);return s.length>1?it(114,null,i,s.length):i}if(fr(t))return t;if(t.detail){const s=t.detail;if(s.error)return C1(s.error,e);if(s.exception)return C1(s.exception,e)}return t.stack?C1(t,e):t.message?t.message:it(115,null)}import{watchFile as i2,unwatchFile as r2}from"fs";function n2(t){return L1(t,0)}function L1(t,e){switch(typeof t){case"object":return t===null?vt(349,e):Array.isArray(t)?a2(t,e):c2(t,e);case"string":return $i(t,e);case"boolean":return o2(t,e);case"number":return vt(t,e);case"undefined":return vt(937,e);default:return vt(617,e)}}function vt(t,e){return(e<<5)-e+t|0}function o2(t,e){return vt(t?433:863,e)}function $i(t,e){e=vt(149417,e);for(let s=0,i=t.length;s<i;s++)e=vt(t.charCodeAt(s),e);return e}function a2(t,e){return e=vt(104579,e),t.reduce((s,i)=>L1(i,s),e)}function c2(t,e){return e=vt(181387,e),Object.keys(t).sort().reduce((s,i)=>(s=$i(i,s),L1(t[i],s)),e)}var Ei;(function(t){t[t.BLOCK_SIZE=64]="BLOCK_SIZE",t[t.UNICODE_REPLACEMENT=65533]="UNICODE_REPLACEMENT"})(Ei||(Ei={}));function S1(t,e,s=32){const i=s-e,r=~((1<<i)-1);return(t<<e|(r&t)>>>i)>>>0}function re(t,e=32){return t instanceof ArrayBuffer?Array.from(new Uint8Array(t)).map(s=>s.toString(16).padStart(2,"0")).join(""):(t>>>0).toString(16).padStart(e/4,"0")}var y0=class Wi{static{this.g=new DataView(new ArrayBuffer(320))}constructor(){this.h=1732584193,this.l=4023233417,this.m=2562383102,this.n=271733878,this.o=3285377520,this.p=new Uint8Array(67),this.q=new DataView(this.p.buffer),this.r=0,this.t=0,this.u=0,this.v=!1}update(e){const s=e.length;if(s===0)return;const i=this.p;let r=this.r,n=this.u,o,a;for(n!==0?(o=n,a=-1,n=0):(o=e.charCodeAt(0),a=0);;){let h=o;if(vn(o))if(a+1<s){const c=e.charCodeAt(a+1);ls(c)?(a++,h=mn(o,c)):h=65533}else{n=o;break}else ls(o)&&(h=65533);if(r=this.w(i,r,h),a++,a<s)o=e.charCodeAt(a);else break}this.r=r,this.u=n}w(e,s,i){return i<128?e[s++]=i:i<2048?(e[s++]=192|(i&1984)>>>6,e[s++]=128|(i&63)>>>0):i<65536?(e[s++]=224|(i&61440)>>>12,e[s++]=128|(i&4032)>>>6,e[s++]=128|(i&63)>>>0):(e[s++]=240|(i&1835008)>>>18,e[s++]=128|(i&258048)>>>12,e[s++]=128|(i&4032)>>>6,e[s++]=128|(i&63)>>>0),s>=64&&(this.y(),s-=64,this.t+=64,e[0]=e[64],e[1]=e[65],e[2]=e[66]),s}digest(){return this.v||(this.v=!0,this.u&&(this.u=0,this.r=this.w(this.p,this.r,65533)),this.t+=this.r,this.x()),re(this.h)+re(this.l)+re(this.m)+re(this.n)+re(this.o)}x(){this.p[this.r++]=128,this.p.subarray(this.r).fill(0),this.r>56&&(this.y(),this.p.fill(0));const e=8*this.t;this.q.setUint32(56,Math.floor(e/4294967296),!1),this.q.setUint32(60,e%4294967296,!1),this.y()}y(){const e=Wi.g,s=this.q;for(let l=0;l<64;l+=4)e.setUint32(l,s.getUint32(l,!1),!1);for(let l=64;l<320;l+=4)e.setUint32(l,S1(e.getUint32(l-12,!1)^e.getUint32(l-32,!1)^e.getUint32(l-56,!1)^e.getUint32(l-64,!1),1),!1);let i=this.h,r=this.l,n=this.m,o=this.n,a=this.o,h,c,f;for(let l=0;l<80;l++)l<20?(h=r&n|~r&o,c=1518500249):l<40?(h=r^n^o,c=1859775393):l<60?(h=r&n|r&o|n&o,c=2400959708):(h=r^n^o,c=3395469782),f=S1(i,5)+h+a+c+e.getUint32(l*4,!1)&4294967295,a=o,o=n,n=S1(r,30),r=i,i=f;this.h=this.h+i&4294967295,this.l=this.l+r&4294967295,this.m=this.m+n&4294967295,this.n=this.n+o&4294967295,this.o=this.o+a&4294967295}},Ai=class extends X{constructor(){super(),this.a=this.B(new I),this.onDidChangeFile=this.a.event,this.b=this.B(new I),this.onDidLogMessage=this.b.event,this.c=this.B(new I),this.f=this.c.event,this.g=new Map,this.h=new Map,this.j=this.B(new z1),this.m=new Set,this.n=this.B(new Gn(this.y())),this.r=5007,this.s=new Tt,this.R=!1,this.B(this.f(t=>this.z({id:this.u(t),correlationId:this.t(t)?t.correlationId:void 0,path:t.path})))}t(t){return gi(t)}u(t){return this.t(t)?t.correlationId:n2(t)}async watch(t){this.s.isSettled||this.s.complete(),this.s=new Tt;try{this.g.clear(),this.h.clear();for(const e of t)this.t(e)?this.g.set(e.correlationId,e):this.h.set(this.u(e),e);for(const[e]of this.j)!this.h.has(e)&&!this.g.has(e)&&(this.j.deleteAndDispose(e),this.m.delete(e));return await this.w(!1)}finally{this.s.complete()}}w(t){const e=[];for(const[s,i]of[...this.h,...this.g])this.j.has(s)||e.push(i);return this.n.trigger(()=>this.N(e),t?this.y():0).catch(s=>Ht(s))}y(){return 800}isSuspended(t){const e=this.u(t);return this.m.has(e)?"polling":this.j.has(e)}async z(t){if(this.j.has(t.id))return;const e=new st;this.j.set(t.id,e),await this.s.p,!e.isDisposed&&(this.D(t,e),this.w(!0))}C(t){this.j.deleteAndDispose(t.id),this.m.delete(t.id),this.w(!1)}D(t,e){this.F(t,e)?(this.P(`reusing an existing recursive watcher to monitor ${t.path}`),this.m.delete(t.id)):(this.G(t,e),this.m.add(t.id))}F(t,e){const s=this.O?.subscribe(t.path,(i,r)=>{e.isDisposed||(i?this.D(t,e):r?.type===1&&this.H(t))});return s?(e.add(s),!0):!1}G(t,e){let s=!1;const i=(r,n)=>{if(e.isDisposed)return;const o=this.I(r),a=this.I(n),h=s;s=o,!o&&(a||h)&&this.H(t)};this.P(`starting fs.watchFile() on ${t.path} (correlationId: ${t.correlationId})`);try{i2(t.path,{persistent:!1,interval:this.r},i)}catch(r){this.Q(`fs.watchFile() failed with error ${r} on path ${t.path} (correlationId: ${t.correlationId})`)}e.add(G(()=>{this.P(`stopping fs.watchFile() on ${t.path} (correlationId: ${t.correlationId})`);try{r2(t.path,i)}catch(r){this.Q(`fs.unwatchFile() failed with error ${r} on path ${t.path} (correlationId: ${t.correlationId})`)}}))}H(t){this.P(`detected ${t.path} exists again, resuming watcher (correlationId: ${t.correlationId})`);const e={resource:F.file(t.path),type:1,cId:t.correlationId};this.a.fire([e]),this.J(e,t),this.C(t)}I(t){return t.ctimeMs===0&&t.ino===0}async stop(){this.j.clearAndDisposeAll(),this.m.clear()}J(t,e){if(this.R){const s=` >> normalized ${t.type===1?"[ADDED]":t.type===2?"[DELETED]":"[CHANGED]"} ${t.resource.fsPath}`;this.L(s,e)}}L(t,e){this.R&&this.P(`${t}${typeof e.correlationId=="number"?` <${e.correlationId}> `:""}`)}M(t){return`${t.path} (excludes: ${t.excludes.length>0?t.excludes:"<none>"}, includes: ${t.includes&&t.includes.length>0?JSON.stringify(t.includes):"<all>"}, filter: ${vi(t.filter)}, correlationId: ${typeof t.correlationId=="number"?t.correlationId:"<none>"})`}async setVerboseLogging(t){this.R=t}};import*as Ci from"fs";async function D1(t,e){if(z)return t;const s=n1(t);if(t===s)return t;const i=(Ee(t)||t).toLowerCase();try{if(e?.isCancellationRequested)return null;const n=(await St.readdir(s)).filter(o=>o.toLowerCase()===i);if(n.length===1){const o=await D1(s,e);if(o)return rt(o,n[0])}else if(n.length>1){const o=n.indexOf(i);if(o>=0){const a=await D1(s,e);if(a)return rt(a,n[o])}}}catch{}return null}async function xi(t){try{return await St.realpath(t)}catch{const s=h2(t);return await Ci.promises.access(s,Ci.constants.R_OK),s}}function h2(t){return wn($e(t),nt)}var Li=class extends X{get failed(){return this.c}get stopped(){return this.f}constructor(t,e,s,i,r,n){super(),this.ready=t,this.request=e,this.restarts=s,this.token=i,this.worker=r,this.m=n,this.a=this.B(new I),this.onDidStop=this.a.event,this.b=this.B(new I),this.onDidFail=this.b.event,this.c=!1,this.f=!1,this.j=new Map,this.g=this.request.includes?Te(this.request.path,this.request.includes):void 0,this.h=this.request.excludes?Te(this.request.path,this.request.excludes):void 0,this.B(G(()=>this.j.clear()))}subscribe(t,e){t=F.file(t).fsPath;let s=this.j.get(t);return s||(s=new Set,this.j.set(t,s)),s.add(e),G(()=>{const i=this.j.get(t);i&&(i.delete(e),i.size===0&&this.j.delete(t))})}get subscriptionsCount(){return this.j.size}notifyFileChange(t,e){const s=this.j.get(t);if(s)for(const i of s)i(e)}notifyWatchFailed(){this.c=!0,this.b.fire()}include(t){return!this.g||this.g.length===0?!0:this.g.some(e=>e(t))}exclude(t){return!!this.h?.some(e=>e(t))}async stop(t){this.f=!0;try{await this.m()}finally{this.a.fire({joinRestart:t}),this.dispose()}}},l2=class mt extends Ai{static{this.S=new Map([["create",1],["update",0],["delete",2]])}static{this.U={win32:[],darwin:[rt(s2(),"Library","Containers")],linux:[]}}static{this.W=Y?"windows":z?"inotify":"fs-events"}get watchers(){return this.Y.values()}static{this.Z=75}constructor(){super(),this.X=this.B(new I),this.onDidError=this.X.event,this.Y=new Map,this.$=this.B(new g1({maxWorkChunkSize:500,throttleDelay:200,maxBufferedWork:3e4},e=>this.a.fire(e))),this.ab=!1,this.bb()}bb(){const e=i=>this.qb(i),s=i=>this.qb(i);process.on("uncaughtException",e),process.on("unhandledRejection",s),this.B(G(()=>{process.off("uncaughtException",e),process.off("unhandledRejection",s)}))}async N(e){e=await this.tb(e);const s=[],i=new Set(Array.from(this.watchers));for(const r of e){const n=this.Y.get(this.db(r));n&&Oe(n.request.excludes,r.excludes)&&Oe(n.request.includes,r.includes)&&n.request.pollingInterval===r.pollingInterval?i.delete(n):s.push(r)}s.length&&this.P(`Request to start watching: ${s.map(r=>this.M(r)).join(",")}`),i.size&&this.P(`Request to stop watching: ${Array.from(i).map(r=>this.M(r.request)).join(",")}`);for(const r of i)await this.sb(r);for(const r of s)r.pollingInterval?await this.fb(r,r.pollingInterval):await this.gb(r)}db(e){return typeof e.correlationId=="number"?e.correlationId:this.eb(e.path)}eb(e){return z?e:e.toLowerCase()}async fb(e,s,i=0){const r=new It,n=new Tt,o=ws(e2(),"vscode-watcher-snapshot"),a=new Li(n.p,e,i,r.token,new d1(d=>this.kb(d,a),mt.Z),async()=>{r.dispose(!0),a.worker.flush(),a.worker.dispose(),u.dispose(),await A1.unlink(o)});this.Y.set(this.db(e),a);const{realPath:h,realPathDiffers:c,realPathLength:f}=await this.mb(e);this.P(`Started watching: '${h}' with polling interval '${s}'`);let l=0;const u=new xe(async()=>{if(l++,r.token.isCancellationRequested)return;const d=mi;try{if(l>1){const v=await d.getEventsSince(h,o,{ignore:this.hb(e.excludes),backend:mt.W});if(r.token.isCancellationRequested)return;this.ib(v,a,c,f)}await d.writeSnapshot(h,o,{ignore:this.hb(e.excludes),backend:mt.W})}catch(v){this.qb(v,e)}l===1&&n.complete(),!r.token.isCancellationRequested&&u.schedule()},s);u.schedule(0)}async gb(e,s=0){const i=new It,r=new Tt,n=new Li(r.p,e,s,i.token,new d1(c=>this.kb(c,n),mt.Z),async()=>{i.dispose(!0),n.worker.flush(),n.worker.dispose(),await(await r.p)?.unsubscribe()});this.Y.set(this.db(e),n);const{realPath:o,realPathDiffers:a,realPathLength:h}=await this.mb(e);try{const f=await mi.subscribe(o,(l,u)=>{n.token.isCancellationRequested||(l&&this.qb(l,e),this.ib(u,n,a,h))},{backend:mt.W,ignore:this.hb(n.request.excludes)});this.P(`Started watching: '${o}' with backend '${mt.W}'`),r.complete(f)}catch(c){this.qb(c,e),r.complete(void 0),n.notifyWatchFailed(),this.c.fire(e)}}hb(e){const s=[...e],i=mt.U[process.platform];if(Array.isArray(i))for(const r of i)s.includes(r)||s.push(r);return s}ib(e,s,i,r){if(e.length===0)return;this.nb(e,s.request,i,r);const n=this.jb(s,e);for(const o of n)s.worker.work(o)}jb(e,s){const i=[];for(const{path:r,type:n}of s){const o=mt.S.get(n);this.R&&this.L(`${o===1?"[ADDED]":o===2?"[DELETED]":"[CHANGED]"} ${r}`,e.request),e.include(r)?i.push({type:o,resource:F.file(r),cId:e.request.correlationId}):this.R&&this.L(` >> ignored (not included) ${r}`,e.request)}return i}kb(e,s){const i=pi(e),{events:r,rootDeleted:n}=this.ob(i,s);this.lb(r,s),n&&this.pb(s)}lb(e,s){if(e.length===0)return;this.$.work(e)?this.$.pending>0&&this.P(`started throttling events due to large amount of file change events at once (pending: ${this.$.pending}, most recent change: ${e[0].resource.fsPath}). Use 'files.watcherExclude' setting to exclude folders with lots of changing files (e.g. compilation output).`,s):this.Q(`started ignoring events due to too many file change events at once (incoming: ${e.length}, most recent change: ${e[0].resource.fsPath}). Use 'files.watcherExclude' setting to exclude folders with lots of changing files (e.g. compilation output).`)}async mb(e){let s=e.path,i=!1,r=e.path.length;try{s=await xi(e.path),e.path===s&&(s=await D1(e.path)??e.path),e.path!==s&&(r=s.length,i=!0,this.P(`correcting a path to watch that seems to be a symbolic link or wrong casing (original: ${e.path}, real: ${s})`))}catch{}return{realPath:s,realPathDiffers:i,realPathLength:r}}nb(e,s,i,r){for(const n of e)Nt&&(n.path=ke(n.path)),Y&&s.path.length<=3&&(n.path=$e(n.path)),i&&(n.path=s.path+n.path.substr(r))}ob(e,s){const i=[];let r=!1;const n=this.t(s.request)?s.request.filter:void 0;for(const o of e){if(s.subscriptionsCount>0&&s.notifyFileChange(o.resource.fsPath,o),r=o.type===2&&c1(o.resource.fsPath,s.request.path,!z),wi(o,n)){this.R&&this.L(` >> ignored (filtered) ${o.resource.fsPath}`,s.request);continue}this.J(o,s.request),i.push(o)}return{events:i,rootDeleted:r}}pb(e){this.Q("Watcher shutdown because watched path got deleted",e),e.notifyWatchFailed(),this.c.fire(e.request)}qb(e,s){const i=x1(e);i.indexOf("No space left on device")!==-1?this.ab||(this.xb("Inotify limit reached (ENOSPC)",s),this.ab=!0):i.indexOf("File system must be re-scanned")!==-1?this.xb(i,s):(this.xb(`Unexpected error: ${i} (EUNKNOWN)`,s),this.X.fire({request:s,error:i}))}async stop(){await super.stop();for(const e of this.watchers)await this.sb(e)}rb(e,s=800){const i=new xe(async()=>{if(e.token.isCancellationRequested)return;const r=new Tt;try{await this.sb(e,r.p),e.request.pollingInterval?await this.fb(e.request,e.request.pollingInterval,e.restarts+1):await this.gb(e.request,e.restarts+1)}finally{r.complete()}},s);i.schedule(),e.token.onCancellationRequested(()=>i.dispose())}async sb(e,s){this.P("stopping file watcher",e),this.Y.delete(this.db(e.request));try{await e.stop(s)}catch(i){this.xb(`Unexpected error stopping watcher: ${x1(i)}`,e.request)}}async tb(e,s=!0){e.sort((n,o)=>n.path.length-o.path.length);const i=new Map;for(const n of e){if(n.excludes.includes(qt))continue;let o=i.get(n.correlationId);o||(o=new Map,i.set(n.correlationId,o));const a=this.eb(n.path);o.has(a)&&this.P(`ignoring a request for watching who's path is already watched: ${this.M(n)}`),o.set(a,n)}const r=[];for(const n of i.values()){const o=je.forPaths(!z);for(const a of n.values()){if(o.findSubstr(a.path))if(o.has(a.path))this.P(`ignoring a request for watching who's path is already watched: ${this.M(a)}`);else try{if(!(await A1.lstat(a.path)).isSymbolicLink()){this.P(`ignoring a request for watching who's parent is already watched: ${this.M(a)}`);continue}}catch(h){this.P(`ignoring a request for watching who's lstat failed to resolve: ${this.M(a)} (error: ${h})`),this.c.fire(a);continue}if(s&&!await this.ub(a.path)){this.c.fire(a);continue}o.set(a.path,a)}r.push(...Array.from(o).map(([,a])=>a))}return r}async ub(e){try{if(!(await A1.stat(e)).isDirectory())return this.P(`ignoring a path for watching that is a file and not a folder: ${e}`),!1}catch(s){return this.P(`ignoring a path for watching who's stat info failed to resolve: ${e} (error: ${s})`),!1}return!0}subscribe(e,s){for(const i of this.watchers){if(i.failed||!jt(e,i.request.path,!z)||i.exclude(e)||!i.include(e))continue;const r=new st;return r.add(R.once(i.onDidStop)(async n=>{await n.joinRestart,!r.isDisposed&&s(!0)})),r.add(R.once(i.onDidFail)(()=>s(!0))),r.add(i.subscribe(e,n=>s(null,n))),r}}P(e,s){this.R&&this.b.fire({type:"trace",message:this.yb(e,s?.request)})}Q(e,s){this.b.fire({type:"warn",message:this.yb(e,s?.request)})}xb(e,s){this.b.fire({type:"error",message:this.yb(e,s)})}yb(e,s){return s?`[File Watcher] ${e} (path: ${s.path})`:`[File Watcher ('parcel')] ${e}`}get O(){return this}};import{watch as f2,promises as u2}from"fs";var d2=class Ve extends X{static{this.a=100}static{this.b=75}get isReusingRecursiveWatcher(){return this.r}get failed(){return this.s}constructor(e,s,i,r,n,o){super(),this.t=e,this.u=s,this.w=i,this.y=r,this.z=n,this.C=o,this.c=this.B(new g1({maxWorkChunkSize:100,throttleDelay:200,maxBufferedWork:1e4},a=>this.w(a))),this.f=this.B(new d1(a=>this.M(a),Ve.b)),this.m=new It,this.n=new Ct(async()=>{let a=this.t.path;try{a=await xi(this.t.path),this.t.path!==a&&this.Q(`correcting a path to watch that seems to be a symbolic link (original: ${this.t.path}, real: ${a})`)}catch{}return a}),this.r=!1,this.s=!1,this.g=Te(this.t.path,this.t.excludes),this.h=this.t.includes?Te(this.t.path,this.t.includes):void 0,this.j=gi(this.t)?this.t.filter:void 0,this.ready=this.D()}async D(){try{const e=await u2.stat(this.t.path);if(this.m.token.isCancellationRequested)return;this.B(await this.G(e.isDirectory()))}catch(e){e.code!=="ENOENT"?this.O(e):this.Q(`ignoring a path for watching who's stat info failed to resolve: ${this.t.path} (error: ${e})`),this.F()}}F(){this.s=!0,this.y?.()}async G(e){const s=new st;return this.H(e,s)?(this.Q(`reusing an existing recursive watcher for ${this.t.path}`),this.r=!0):(this.r=!1,await this.I(e,s)),s}H(e,s){if(e)return!1;const i=F.file(this.t.path),r=this.u?.subscribe(this.t.path,async(n,o)=>{if(!s.isDisposed)if(n){const a=await this.G(e);s.isDisposed?a.dispose():s.add(a)}else o&&(typeof o.cId=="number"||typeof this.t.correlationId=="number")&&this.L({resource:i,type:o.type,cId:this.t.correlationId},!0)});return r?(s.add(r),!0):!1}async I(e,s){const i=await this.n.value;if(this.m.token.isCancellationRequested)return;if(Nt&&jt(i,"/Volumes/",!0)){this.O(`Refusing to watch ${i} for changes using fs.watch() for possibly being a network share where watching is unreliable and unstable.`);return}const r=new It(this.m.token);s.add(G(()=>r.dispose(!0)));const n=new st;s.add(n);try{const o=F.file(this.t.path),a=Ee(i),h=f2(i);n.add(G(()=>{h.removeAllListeners(),h.close()})),this.Q(`Started watching: '${i}'`);const c=new Set;if(e)try{for(const l of await St.readdir(i))c.add(l)}catch(l){this.O(l)}if(r.token.isCancellationRequested)return;const f=new Map;n.add(G(()=>{for(const[,l]of f)l.dispose();f.clear()})),h.on("error",(l,u)=>{r.token.isCancellationRequested||(this.O(`Failed to watch ${i} for changes using fs.watch() (${l}, ${u})`),this.F())}),h.on("change",(l,u)=>{if(r.token.isCancellationRequested)return;this.C&&this.R(`[raw] ["${l}"] ${u}`);let d="";if(u&&(d=u.toString(),Nt&&(d=ke(d))),!(!d||l!=="change"&&l!=="rename"))if(e)if(l==="rename"){f.get(d)?.dispose();const v=setTimeout(async()=>{if(f.delete(d),c1(d,a,!z)&&!await St.exists(i)){this.J(o);return}if(r.token.isCancellationRequested)return;const y=await this.N(rt(i,d));if(r.token.isCancellationRequested)return;let E;y?c.has(d)?E=0:(E=1,c.add(d)):(c.delete(d),E=2),this.L({resource:As(o,d),type:E,cId:this.t.correlationId})},Ve.a);f.set(d,G(()=>clearTimeout(v)))}else{let v;c.has(d)?v=0:(v=1,c.add(d)),this.L({resource:As(o,d),type:v,cId:this.t.correlationId})}else if(l==="rename"||!c1(d,a,!z)){const v=setTimeout(async()=>{const y=await St.exists(i);r.token.isCancellationRequested||(y?(this.L({resource:o,type:0,cId:this.t.correlationId},!0),n.add(await this.G(!1))):this.J(o))},Ve.a);n.clear(),n.add(G(()=>clearTimeout(v)))}else this.L({resource:o,type:0,cId:this.t.correlationId},!0)})}catch(o){if(r.token.isCancellationRequested)return;this.O(`Failed to watch ${i} for changes using fs.watch() (${o.toString()})`),this.F()}}J(e){this.P("Watcher shutdown because watched path got deleted"),this.L({resource:e,type:2,cId:this.t.correlationId},!0),this.f.flush(),this.F()}L(e,s=!1){this.m.token.isCancellationRequested||(this.C&&this.R(`${e.type===1?"[ADDED]":e.type===2?"[DELETED]":"[CHANGED]"} ${e.resource.fsPath}`),!s&&this.g.some(i=>i(e.resource.fsPath))?this.C&&this.R(` >> ignored (excluded) ${e.resource.fsPath}`):!s&&this.h&&this.h.length>0&&!this.h.some(i=>i(e.resource.fsPath))?this.C&&this.R(` >> ignored (not included) ${e.resource.fsPath}`):this.f.work(e))}M(e){const s=pi(e),i=[];for(const n of s){if(wi(n,this.j)){this.C&&this.R(` >> ignored (filtered) ${n.resource.fsPath}`);continue}i.push(n)}if(i.length===0)return;if(this.C)for(const n of i)this.R(` >> normalized ${n.type===1?"[ADDED]":n.type===2?"[DELETED]":"[CHANGED]"} ${n.resource.fsPath}`);this.c.work(i)?this.c.pending>0&&this.Q(`started throttling events due to large amount of file change events at once (pending: ${this.c.pending}, most recent change: ${i[0].resource.fsPath}). Use 'files.watcherExclude' setting to exclude folders with lots of changing files (e.g. compilation output).`):this.P(`started ignoring events due to too many file change events at once (incoming: ${i.length}, most recent change: ${i[0].resource.fsPath}). Use 'files.watcherExclude' setting to exclude folders with lots of changing files (e.g. compilation output).`)}async N(e){if(z)return St.exists(e);try{const s=Ee(e);return(await St.readdir(n1(e))).some(r=>r===s)}catch(s){return this.Q(s),!1}}setVerboseLogging(e){this.C=e}O(e){this.m.token.isCancellationRequested||this.z?.({type:"error",message:`[File Watcher (node.js)] ${e}`})}P(e){this.m.token.isCancellationRequested||this.z?.({type:"warn",message:`[File Watcher (node.js)] ${e}`})}Q(e){!this.m.token.isCancellationRequested&&this.C&&this.z?.({type:"trace",message:`[File Watcher (node.js)] ${e}`})}R(e){!this.m.token.isCancellationRequested&&this.C&&this.Q(`${e}${typeof this.t.correlationId=="number"?` <${this.t.correlationId}> `:""}`)}dispose(){this.m.dispose(!0),super.dispose()}},g2=class extends Ai{get watchers(){return this.S.values()}constructor(t){super(),this.O=t,this.onDidError=R.None,this.S=new Map,this.U=this.B(new Xe)}async N(t){t=this.cb(t);const e=[],s=new Set(Array.from(this.watchers));for(const i of t){const r=this.S.get(this.Z(i));r&&Oe(r.request.excludes,i.excludes)&&Oe(r.request.includes,i.includes)?s.delete(r):e.push(i)}e.length&&this.P(`Request to start watching: ${e.map(i=>this.M(i)).join(",")}`),s.size&&this.P(`Request to stop watching: ${Array.from(s).map(i=>this.M(i.request)).join(",")}`),this.U.clear();for(const i of s)this.bb(i);this.Y().work(e)}Y(){return this.U.value=new g1({maxWorkChunkSize:100,throttleDelay:100,maxBufferedWork:Number.MAX_VALUE},t=>{for(const e of t)this.ab(e)}),this.U.value}Z(t){return typeof t.correlationId=="number"?t.correlationId:this.$(t.path)}$(t){return z?t:t.toLowerCase()}ab(t){const e=new d2(t,this.O,i=>this.a.fire(i),()=>this.c.fire(t),i=>this.b.fire(i),this.R),s={request:t,instance:e};this.S.set(this.Z(t),s)}async stop(){await super.stop();for(const t of this.watchers)this.bb(t)}bb(t){this.P("stopping file watcher",t),this.S.delete(this.Z(t.request)),t.instance.dispose()}cb(t){const e=new Map;for(const s of t){let i=e.get(s.correlationId);i||(i=new Map,e.set(s.correlationId,i));const r=this.$(s.path);i.has(r)&&this.P(`ignoring a request for watching who's path is already watched: ${this.M(s)}`),i.set(r,s)}return Array.from(e.values()).map(s=>Array.from(s.values())).flat()}async setVerboseLogging(t){super.setVerboseLogging(t);for(const e of this.watchers)e.instance.setVerboseLogging(t)}P(t,e){this.R&&this.b.fire({type:"trace",message:this.fb(t,e)})}Q(t){this.b.fire({type:"warn",message:this.fb(t)})}fb(t,e){return e?`[File Watcher (node.js)] ${t} (${this.M(e.request)})`:`[File Watcher (node.js)] ${t}`}};function p2(t,e,s,i){const r=[],n=Fe(t.filter(p=>Me(p))),o=n.filter(p=>s.isSuspended(p)===!1),a=n.filter(p=>s.isSuspended(p)==="polling"),h=n.filter(p=>s.isSuspended(p)===!0),c=Si(n,s),f=Di(s),l=Fe(t.filter(p=>!Me(p))),u=l.filter(p=>i.isSuspended(p)===!1),d=l.filter(p=>i.isSuspended(p)==="polling"),v=l.filter(p=>i.isSuspended(p)===!0),y=Si(l,i),E=Pi(i);r.push("[Summary]"),r.push(`- Recursive Requests:     total: ${n.length}, suspended: ${c.suspended}, polling: ${c.polling}, failed: ${e}`),r.push(`- Non-Recursive Requests: total: ${l.length}, suspended: ${y.suspended}, polling: ${y.polling}`),r.push(`- Recursive Watchers:     total: ${Array.from(s.watchers).length}, active: ${f.active}, failed: ${f.failed}, stopped: ${f.stopped}`),r.push(`- Non-Recursive Watchers: total: ${Array.from(i.watchers).length}, active: ${E.active}, failed: ${E.failed}, reusing: ${E.reusing}`),r.push(`- I/O Handles Impact:     total: ${c.polling+y.polling+f.active+E.active}`),r.push(`
[Recursive Requests (${n.length}, suspended: ${c.suspended}, polling: ${c.polling})]:`);const O=[];for(const p of[o,a,h].flat())_i(O,p,s);r.push(...Ue(O));const et=[];w2(et,s),r.push(...Ue(et)),r.push(`
[Non-Recursive Requests (${l.length}, suspended: ${y.suspended}, polling: ${y.polling})]:`);const j=[];for(const p of[u,d,v].flat())_i(j,p,i);r.push(...Ue(j));const Pt=[];return v2(Pt,i),r.push(...Ue(Pt)),`

[File Watcher] request stats:

${r.join(`
`)}

`}function Ue(t){let e=0;for(const s of t)e=Math.max(e,s.split("	")[0].length);for(let s=0;s<t.length;s++){const r=t[s].split("	");if(r.length===2){const n=" ".repeat(e-r[0].length);t[s]=`${r[0]}${n}	${r[1]}`}}return t}function Si(t,e){let s=0,i=0;for(const r of t){const n=e.isSuspended(r);n!==!1&&(i++,n==="polling"&&s++)}return{suspended:i,polling:s}}function Di(t){let e=0,s=0,i=0;for(const r of t.watchers)!r.failed&&!r.stopped&&e++,r.failed&&s++,r.stopped&&i++;return{active:e,failed:s,stopped:i}}function Pi(t){let e=0,s=0,i=0;for(const r of t.watchers)!r.instance.failed&&!r.instance.isReusingRecursiveWatcher&&e++,r.instance.failed&&s++,r.instance.isReusingRecursiveWatcher&&i++;return{active:e,failed:s,reusing:i}}function Fe(t){return t.sort((e,s)=>{const i=ki(e)?e.path:e.request.path,r=ki(s)?s.path:s.request.path,n=Math.min(i.length,r.length);for(let o=0;o<n;o++)if(i[o]!==r[o])return i[o]<r[o]?-1:1;return i.length-r.length}),t}function ki(t){return typeof t?.path=="string"}function _i(t,e,s){const i=[],r=s.isSuspended(e);r!==!1&&(r==="polling"?i.push("[SUSPENDED <polling>]"):i.push("[SUSPENDED <non-polling>]")),t.push(` ${e.path}	${i.length>0?i.join(" ")+" ":""}(${P1(e)})`)}function P1(t){return`excludes: ${t.excludes.length>0?t.excludes:"<none>"}, includes: ${t.includes&&t.includes.length>0?JSON.stringify(t.includes):"<all>"}, filter: ${vi(t.filter)}, correlationId: ${typeof t.correlationId=="number"?t.correlationId:"<none>"}`}function w2(t,e){const s=Fe(Array.from(e.watchers)),{active:i,failed:r,stopped:n}=Di(e);t.push(`
[Recursive Watchers (${s.length}, active: ${i}, failed: ${r}, stopped: ${n})]:`);for(const o of s){const a=[];o.failed&&a.push("[FAILED]"),o.stopped&&a.push("[STOPPED]"),o.subscriptionsCount>0&&a.push(`[SUBSCRIBED:${o.subscriptionsCount}]`),o.restarts>0&&a.push(`[RESTARTED:${o.restarts}]`),t.push(` ${o.request.path}	${a.length>0?a.join(" ")+" ":""}(${P1(o.request)})`)}}function v2(t,e){const s=Fe(Array.from(e.watchers)),i=s.filter(c=>!c.instance.failed&&!c.instance.isReusingRecursiveWatcher),r=s.filter(c=>c.instance.failed),n=s.filter(c=>c.instance.isReusingRecursiveWatcher),{active:o,failed:a,reusing:h}=Pi(e);t.push(`
[Non-Recursive Watchers (${s.length}, active: ${o}, failed: ${a}, reusing: ${h})]:`);for(const c of[i,r,n].flat()){const f=[];c.instance.failed&&f.push("[FAILED]"),c.instance.isReusingRecursiveWatcher&&f.push("[REUSING]"),t.push(` ${c.request.path}	${f.length>0?f.join(" ")+" ":""}(${P1(c.request)})`)}}var m2=class extends X{constructor(){super(),this.a=this.B(new l2),this.b=this.B(new g2(this.a)),this.onDidChangeFile=R.any(this.a.onDidChangeFile,this.b.onDidChangeFile),this.onDidError=R.any(this.a.onDidError,this.b.onDidError),this.c=this.B(new I),this.onDidLogMessage=R.any(this.c.event,this.a.onDidLogMessage,this.b.onDidLogMessage),this.f=[],this.g=0,this.B(this.a.onDidError(t=>{t.request&&this.g++}))}async watch(t){this.f=t,this.g=0;let e;try{await this.a.watch(t.filter(s=>Me(s)))}catch(s){e=s}try{await this.b.watch(t.filter(s=>!Me(s)))}catch(s){e||(e=s)}if(e)throw e}async setVerboseLogging(t){t&&this.f.length>0&&this.c.fire({type:"trace",message:p2(this.f,this.g,this.a,this.b)}),await Le.settled([this.a.setVerboseLogging(t),this.b.setVerboseLogging(t)])}async stop(){await Le.settled([this.a.stop(),this.b.stop()])}},k1;Hs(process)?k1=new Ro:k1=new ko("watcher");var b2=new m2;k1.registerChannel("watcher",b1.fromService(b2,new st));

//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/848b80aeb52026648a8ff9f7c45a9b0a80641e2e/core/vs/platform/files/node/watcher/watcherMain.js.map
