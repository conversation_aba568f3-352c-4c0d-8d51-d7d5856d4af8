import * as vscode from "vscode";
import { TerminalManager } from "./terminal/TerminalManager";
import { TerminalRegistry } from "./terminal/TerminalRegistry";
import { getLatestTerminalOutput } from "./terminal/get-latest-output";
import { Logger } from "./services/logging/Logger";

export function registerCommands(context: vscode.ExtensionContext, terminalManager: TerminalManager) {
	// Create new terminal command
	const createTerminalCommand = vscode.commands.registerCommand(
		"terminalManager.createTerminal",
		async () => {
			try {
				const workspaceFolders = vscode.workspace.workspaceFolders;
				const defaultCwd = workspaceFolders ? workspaceFolders[0].uri.fsPath : require("process").cwd();

				const cwd = await vscode.window.showInputBox({
					prompt: "Enter the working directory for the new terminal",
					value: defaultCwd,
					validateInput: (value) => {
						if (!value || value.trim() === "") {
							return "Working directory cannot be empty";
						}
						return null;
					}
				});

				if (cwd) {
					const terminalInfo = await terminalManager.getOrCreateTerminal(cwd.trim());
					terminalInfo.terminal.show();
					vscode.window.showInformationMessage(`Created terminal ${terminalInfo.id} in ${cwd}`);
					Logger.info(`Created terminal ${terminalInfo.id} in ${cwd}`);
				}
			} catch (error) {
				const errorMessage = `Failed to create terminal: ${error}`;
				vscode.window.showErrorMessage(errorMessage);
				Logger.error(errorMessage, error);
			}
		}
	);

	// Run command in terminal
	const runCommandCommand = vscode.commands.registerCommand(
		"terminalManager.runCommand",
		async () => {
			try {
				const command = await vscode.window.showInputBox({
					prompt: "Enter the command to run",
					placeHolder: "e.g., npm install, ls, dir",
					validateInput: (value) => {
						if (!value || value.trim() === "") {
							return "Command cannot be empty";
						}
						return null;
					}
				});

				if (!command) {
					return;
				}

				const workspaceFolders = vscode.workspace.workspaceFolders;
				const defaultCwd = workspaceFolders ? workspaceFolders[0].uri.fsPath : require("process").cwd();

				const cwd = await vscode.window.showInputBox({
					prompt: "Enter the working directory",
					value: defaultCwd
				});

				if (!cwd) {
					return;
				}

				const terminalInfo = await terminalManager.getOrCreateTerminal(cwd.trim());
				terminalInfo.terminal.show();

				const terminalProcess = terminalManager.runCommand(terminalInfo, command.trim());
				
				// Show progress
				vscode.window.withProgress({
					location: vscode.ProgressLocation.Notification,
					title: `Running: ${command}`,
					cancellable: true
				}, async (progress, token) => {
					let output = "";

					terminalProcess.on("line", (line) => {
						output += line + "\n";
						progress.report({ message: line.substring(0, 50) + "..." });
					});

					token.onCancellationRequested(() => {
						terminalProcess.continue();
					});

					try {
						await terminalProcess;
						vscode.window.showInformationMessage(`Command completed: ${command}`);
						Logger.info(`Command completed: ${command}`);
					} catch (error) {
						vscode.window.showErrorMessage(`Command failed: ${error}`);
						Logger.error(`Command failed: ${command}`, error);
					}
				});

			} catch (error) {
				const errorMessage = `Failed to run command: ${error}`;
				vscode.window.showErrorMessage(errorMessage);
				Logger.error(errorMessage, error);
			}
		}
	);

	// Get terminal output command
	const getOutputCommand = vscode.commands.registerCommand(
		"terminalManager.getOutput",
		async () => {
			try {
				const output = await getLatestTerminalOutput();
				
				if (!output) {
					vscode.window.showInformationMessage("No terminal output available");
					return;
				}

				// Show output in a new document
				const doc = await vscode.workspace.openTextDocument({
					content: output,
					language: "plaintext"
				});
				
				await vscode.window.showTextDocument(doc);
				Logger.info("Terminal output retrieved and displayed");
				
			} catch (error) {
				const errorMessage = `Failed to get terminal output: ${error}`;
				vscode.window.showErrorMessage(errorMessage);
				Logger.error(errorMessage, error);
			}
		}
	);

	// List terminals command
	const listTerminalsCommand = vscode.commands.registerCommand(
		"terminalManager.listTerminals",
		async () => {
			try {
				const allTerminals = TerminalRegistry.getAllTerminals();
				
				if (allTerminals.length === 0) {
					vscode.window.showInformationMessage("No terminals available");
					return;
				}

				const terminalItems = allTerminals.map(terminal => ({
					label: `Terminal ${terminal.id}`,
					description: terminal.busy ? "🔴 Busy" : "🟢 Available",
					detail: `Last command: ${terminal.lastCommand || "None"}`,
					terminal
				}));

				const selected = await vscode.window.showQuickPick(terminalItems, {
					placeHolder: "Select a terminal to focus"
				});

				if (selected) {
					selected.terminal.terminal.show();
					Logger.info(`Focused terminal ${selected.terminal.id}`);
				}
				
			} catch (error) {
				const errorMessage = `Failed to list terminals: ${error}`;
				vscode.window.showErrorMessage(errorMessage);
				Logger.error(errorMessage, error);
			}
		}
	);

	// Clear terminal command
	const clearTerminalCommand = vscode.commands.registerCommand(
		"terminalManager.clearTerminal",
		async () => {
			try {
				const activeTerminal = vscode.window.activeTerminal;
				
				if (!activeTerminal) {
					vscode.window.showWarningMessage("No active terminal to clear");
					return;
				}

				await vscode.commands.executeCommand("workbench.action.terminal.clear");
				vscode.window.showInformationMessage("Terminal cleared");
				Logger.info("Terminal cleared");
				
			} catch (error) {
				const errorMessage = `Failed to clear terminal: ${error}`;
				vscode.window.showErrorMessage(errorMessage);
				Logger.error(errorMessage, error);
			}
		}
	);

	// Register all commands
	context.subscriptions.push(
		createTerminalCommand,
		runCommandCommand,
		getOutputCommand,
		listTerminalsCommand,
		clearTerminalCommand
	);

	Logger.info("All commands registered successfully");
}
