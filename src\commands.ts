import * as vscode from "vscode";
import { TerminalManager } from "./terminal/TerminalManager";
import { Logger } from "./services/logging/Logger";

export function registerCommands(context: vscode.ExtensionContext, terminalManager: TerminalManager) {
	// Create output channel for command results
	const outputChannel = vscode.window.createOutputChannel("Terminal Manager - Command Output");
	context.subscriptions.push(outputChannel);

	// Set up global terminal output monitoring
	setupGlobalTerminalMonitoring(outputChannel, context);

	// Execute command in new terminal
	const executeCommandCommand = vscode.commands.registerCommand(
		"terminalManager.executeCommand",
		async () => {
			try {
				// Get command from user
				const command = await vscode.window.showInputBox({
					prompt: "Enter the command to execute",
					placeHolder: "e.g., npm install, ls, dir, python script.py",
					validateInput: (value) => {
						if (!value || value.trim() === "") {
							return "Command cannot be empty";
						}
						return null;
					}
				});

				if (!command) {
					Logger.debug("Command execution cancelled - no command provided");
					return;
				}

				// Use default working directory (workspace root or current directory)
				const workspaceFolders = vscode.workspace.workspaceFolders;
				const cwd = workspaceFolders ? workspaceFolders[0].uri.fsPath : require("process").cwd();

				Logger.info(`Executing command: ${command} in directory: ${cwd}`);
				Logger.debug(`Command details - Input: "${command}", Working Directory: "${cwd}"`);

				// Clear previous output and show the output channel
				outputChannel.clear();
				outputChannel.show(true);

				// Add header to output
				const timestamp = new Date().toLocaleString();
				outputChannel.appendLine("=".repeat(80));
				outputChannel.appendLine(`Terminal Manager - Extension Command Execution`);
				outputChannel.appendLine(`Timestamp: ${timestamp}`);
				outputChannel.appendLine(`Command: ${command}`);
				outputChannel.appendLine(`Working Directory: ${cwd}`);
				outputChannel.appendLine(`Source: Extension (via Command Palette)`);
				outputChannel.appendLine("=".repeat(80));
				outputChannel.appendLine("");

				// Add debug info to output
				outputChannel.appendLine(`[DEBUG] Creating terminal for directory: ${cwd}`);
				Logger.debug(`Creating terminal for directory: ${cwd}`);

				// Create new terminal and execute command
				const terminalInfo = await terminalManager.getOrCreateTerminal(cwd.trim());
				terminalInfo.terminal.show();

				outputChannel.appendLine(`[DEBUG] Terminal created with ID: ${terminalInfo.id}`);
				outputChannel.appendLine(`[DEBUG] Terminal busy status: ${terminalInfo.busy}`);
				Logger.debug(`Terminal created - ID: ${terminalInfo.id}, Busy: ${terminalInfo.busy}`);

				// Execute the command and capture output
				outputChannel.appendLine(`[DEBUG] Starting command execution...`);
				Logger.debug(`Starting command execution: ${command}`);

				const terminalProcess = terminalManager.runCommand(terminalInfo, command.trim());

				// Show progress and capture output
				let fullOutput = "";
				let hasOutput = false;
				let lineCount = 0;

				vscode.window.withProgress({
					location: vscode.ProgressLocation.Notification,
					title: `Executing: ${command}`,
					cancellable: true
				}, async (progress, token) => {

					outputChannel.appendLine(`[DEBUG] Command process started, listening for output...`);
					Logger.debug("Command process started, setting up output listeners");

					terminalProcess.on("line", (line) => {
						hasOutput = true;
						lineCount++;
						fullOutput += line + "\n";

						// Update progress
						progress.report({ message: line.substring(0, 50) + (line.length > 50 ? "..." : "") });

						// Output to OUTPUT panel with line numbers
						outputChannel.appendLine(`[${lineCount.toString().padStart(3, '0')}] ${line}`);

						// Debug logging
						Logger.debug(`Output line ${lineCount}: ${line.substring(0, 100)}${line.length > 100 ? '...' : ''}`);

						// Console output for development
						console.log(`[Terminal Output ${lineCount}] ${line}`);
					});

					token.onCancellationRequested(() => {
						outputChannel.appendLine(`[DEBUG] Command execution cancelled by user`);
						Logger.info("Command execution cancelled by user");
						terminalProcess.continue();
					});

					try {
						outputChannel.appendLine(`[DEBUG] Waiting for command completion...`);
						await terminalProcess;

						// Command completed successfully
						const completionTime = new Date().toLocaleString();
						outputChannel.appendLine("");
						outputChannel.appendLine("=".repeat(80));
						outputChannel.appendLine(`[SUCCESS] Command completed successfully at ${completionTime}`);
						outputChannel.appendLine(`[STATS] Total output lines: ${lineCount}`);
						outputChannel.appendLine(`[STATS] Total output length: ${fullOutput.length} characters`);
						outputChannel.appendLine("=".repeat(80));

						const message = `Command completed: ${command} (${lineCount} lines of output)`;
						vscode.window.showInformationMessage(message);
						Logger.info(`Command completed successfully: ${command}, Lines: ${lineCount}, Length: ${fullOutput.length}`);

						if (!hasOutput) {
							outputChannel.appendLine(`[INFO] Command completed but no output was captured`);
							Logger.debug("Command completed but no output was captured");
						}

					} catch (error) {
						// Command failed
						const errorTime = new Date().toLocaleString();
						outputChannel.appendLine("");
						outputChannel.appendLine("=".repeat(80));
						outputChannel.appendLine(`[ERROR] Command failed at ${errorTime}`);
						outputChannel.appendLine(`[ERROR] Error details: ${error}`);
						outputChannel.appendLine(`[STATS] Output lines before failure: ${lineCount}`);
						outputChannel.appendLine("=".repeat(80));

						const errorMessage = `Command failed: ${error}`;
						vscode.window.showErrorMessage(errorMessage);
						Logger.error(`Command failed: ${command}`, error);
						Logger.debug(`Command failure details - Lines captured: ${lineCount}, Error: ${error}`);
					}
				});

			} catch (error) {
				const errorMessage = `Failed to execute command: ${error}`;
				outputChannel.appendLine("");
				outputChannel.appendLine("=".repeat(80));
				outputChannel.appendLine(`[FATAL ERROR] Failed to execute command`);
				outputChannel.appendLine(`[FATAL ERROR] Error details: ${error}`);
				outputChannel.appendLine("=".repeat(80));

				vscode.window.showErrorMessage(errorMessage);
				Logger.error(errorMessage, error);
				Logger.debug(`Fatal error details: ${error}`);
			}
		}
	);

	// Register the command
	context.subscriptions.push(executeCommandCommand);

	Logger.info("Commands registered successfully");
	Logger.debug("Output channel created and command registered");
}

/**
 * Set up global terminal output monitoring to capture all terminal activity
 */
function setupGlobalTerminalMonitoring(outputChannel: vscode.OutputChannel, context: vscode.ExtensionContext) {
	Logger.info("Setting up global terminal output monitoring...");

	// Track active terminal executions
	const activeExecutions = new Map<string, {
		execution: any;
		startTime: Date;
		commandLine?: string;
		outputLines: number;
	}>();

	try {
		// Monitor shell execution start events
		const onDidStartExecution = (vscode.window as any).onDidStartTerminalShellExecution?.((e: any) => {
			if (!e || !e.execution) {
				return;
			}

			const executionId = generateExecutionId(e);
			const startTime = new Date();

			Logger.debug(`Shell execution started: ${executionId}`);

			// Store execution info
			activeExecutions.set(executionId, {
				execution: e.execution,
				startTime,
				commandLine: e.execution.commandLine?.value || "Unknown command",
				outputLines: 0
			});

			// Add header for manual command execution
			outputChannel.appendLine("");
			outputChannel.appendLine("=".repeat(80));
			outputChannel.appendLine(`Terminal Manager - Manual Command Detected`);
			outputChannel.appendLine(`Timestamp: ${startTime.toLocaleString()}`);
			outputChannel.appendLine(`Command: ${e.execution.commandLine?.value || "Unknown command"}`);
			outputChannel.appendLine(`Terminal: ${e.terminal.name || "Unnamed"}`);
			outputChannel.appendLine("=".repeat(80));
			outputChannel.appendLine("");

			// Set up output stream monitoring
			try {
				const stream = e.execution.read();
				let lineCount = 0;

				// Process the async iterable stream
				(async () => {
					try {
						for await (const data of stream) {
							if (data && typeof data === 'string') {
								const lines = data.split('\n');
								for (const line of lines) {
									if (line.trim()) {
										lineCount++;
										outputChannel.appendLine(`[${lineCount.toString().padStart(3, '0')}] ${line}`);
										Logger.debug(`Manual command output line ${lineCount}: ${line.substring(0, 100)}${line.length > 100 ? '...' : ''}`);
									}
								}

								// Update execution info
								const execInfo = activeExecutions.get(executionId);
								if (execInfo) {
									execInfo.outputLines = lineCount;
								}
							}
						}
					} catch (streamError) {
						Logger.error(`Error reading terminal stream for ${executionId}`, streamError);
						outputChannel.appendLine(`[ERROR] Failed to read terminal output: ${streamError}`);
					}
				})();

			} catch (error) {
				Logger.error(`Error setting up stream monitoring for ${executionId}`, error);
				outputChannel.appendLine(`[ERROR] Failed to monitor command output: ${error}`);
			}
		});

		if (onDidStartExecution) {
			context.subscriptions.push(onDidStartExecution);
			Logger.info("Global terminal execution monitoring enabled");
		} else {
			Logger.warn("Terminal shell execution monitoring not available in this VSCode version");
		}

		// Monitor shell execution end events
		const onDidEndExecution = (vscode.window as any).onDidEndTerminalShellExecution?.((e: any) => {
			if (!e || !e.execution) {
				return;
			}

			const executionId = generateExecutionId(e);
			const execInfo = activeExecutions.get(executionId);

			if (execInfo) {
				const endTime = new Date();
				const duration = endTime.getTime() - execInfo.startTime.getTime();
				const exitCode = e.exitCode;

				outputChannel.appendLine("");
				outputChannel.appendLine("=".repeat(80));

				if (exitCode === 0) {
					outputChannel.appendLine(`[SUCCESS] Manual command completed successfully at ${endTime.toLocaleString()}`);
				} else {
					outputChannel.appendLine(`[ERROR] Manual command failed at ${endTime.toLocaleString()}`);
					outputChannel.appendLine(`[ERROR] Exit code: ${exitCode}`);
				}

				outputChannel.appendLine(`[STATS] Duration: ${duration}ms`);
				outputChannel.appendLine(`[STATS] Output lines: ${execInfo.outputLines}`);
				outputChannel.appendLine(`[STATS] Command: ${execInfo.commandLine}`);
				outputChannel.appendLine("=".repeat(80));

				Logger.info(`Manual command completed: ${execInfo.commandLine}, Duration: ${duration}ms, Lines: ${execInfo.outputLines}, Exit code: ${exitCode}`);

				// Clean up
				activeExecutions.delete(executionId);
			}
		});

		if (onDidEndExecution) {
			context.subscriptions.push(onDidEndExecution);
			Logger.info("Global terminal execution end monitoring enabled");
		}

	} catch (error) {
		Logger.error("Failed to set up global terminal monitoring", error);
		outputChannel.appendLine(`[ERROR] Failed to set up global terminal monitoring: ${error}`);
	}
}

/**
 * Generate a unique ID for terminal execution tracking
 */
function generateExecutionId(e: any): string {
	const terminalId = e.terminal?.processId || e.terminal?.name || 'unknown';
	const timestamp = Date.now();
	const commandHash = e.execution?.commandLine?.value ?
		e.execution.commandLine.value.substring(0, 20).replace(/\s+/g, '_') : 'unknown';
	return `${terminalId}_${timestamp}_${commandHash}`;
}
