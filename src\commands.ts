import * as vscode from "vscode";
import { TerminalManager } from "./terminal/TerminalManager";
import { Logger } from "./services/logging/Logger";

export function registerCommands(context: vscode.ExtensionContext, terminalManager: TerminalManager) {
	// Execute command in new terminal
	const executeCommandCommand = vscode.commands.registerCommand(
		"terminalManager.executeCommand",
		async () => {
			try {
				// Get command from user
				const command = await vscode.window.showInputBox({
					prompt: "Enter the command to execute",
					placeHolder: "e.g., npm install, ls, dir, python script.py",
					validateInput: (value) => {
						if (!value || value.trim() === "") {
							return "Command cannot be empty";
						}
						return null;
					}
				});

				if (!command) {
					return;
				}

				// Get working directory
				const workspaceFolders = vscode.workspace.workspaceFolders;
				const defaultCwd = workspaceFolders ? workspaceFolders[0].uri.fsPath : require("process").cwd();

				const cwd = await vscode.window.showInputBox({
					prompt: "Enter the working directory",
					value: defaultCwd,
					validateInput: (value) => {
						if (!value || value.trim() === "") {
							return "Working directory cannot be empty";
						}
						return null;
					}
				});

				if (!cwd) {
					return;
				}

				Logger.info(`Executing command: ${command} in directory: ${cwd}`);

				// Create new terminal and execute command
				const terminalInfo = await terminalManager.getOrCreateTerminal(cwd.trim());
				terminalInfo.terminal.show();

				// Execute the command and capture output
				const terminalProcess = terminalManager.runCommand(terminalInfo, command.trim());

				// Show progress and capture output
				let fullOutput = "";
				let hasOutput = false;

				vscode.window.withProgress({
					location: vscode.ProgressLocation.Notification,
					title: `Executing: ${command}`,
					cancellable: true
				}, async (progress, token) => {

					terminalProcess.on("line", (line) => {
						hasOutput = true;
						fullOutput += line + "\n";
						progress.report({ message: line.substring(0, 50) + (line.length > 50 ? "..." : "") });

						// Print output to console and log
						console.log(`[Terminal Output] ${line}`);
						Logger.info(`Command output: ${line}`);
					});

					token.onCancellationRequested(() => {
						terminalProcess.continue();
						Logger.info("Command execution cancelled by user");
					});

					try {
						await terminalProcess;

						// Show completion message
						const message = `Command completed: ${command}`;
						vscode.window.showInformationMessage(message);
						Logger.info(message);

						// Display full output if available
						if (hasOutput && fullOutput.trim()) {
							await displayCommandOutput(command, fullOutput);
						} else {
							vscode.window.showInformationMessage("Command completed but no output was captured");
						}

					} catch (error) {
						const errorMessage = `Command failed: ${error}`;
						vscode.window.showErrorMessage(errorMessage);
						Logger.error(`Command failed: ${command}`, error);

						// Still try to display any captured output
						if (hasOutput && fullOutput.trim()) {
							await displayCommandOutput(command, fullOutput, true);
						}
					}
				});

			} catch (error) {
				const errorMessage = `Failed to execute command: ${error}`;
				vscode.window.showErrorMessage(errorMessage);
				Logger.error(errorMessage, error);
			}
		}
	);

	// Register the command
	context.subscriptions.push(executeCommandCommand);

	Logger.info("Commands registered successfully");
}

/**
 * Display command output in a new document
 */
async function displayCommandOutput(command: string, output: string, isError: boolean = false) {
	try {
		const timestamp = new Date().toLocaleString();
		const status = isError ? "FAILED" : "COMPLETED";

		const content = `Terminal Manager - Command Output
${"=".repeat(50)}
Command: ${command}
Status: ${status}
Timestamp: ${timestamp}
${"=".repeat(50)}

${output}

${"=".repeat(50)}
End of Output
`;

		// Create a new document with the output
		const doc = await vscode.workspace.openTextDocument({
			content: content,
			language: "plaintext"
		});

		// Show the document
		const editor = await vscode.window.showTextDocument(doc);

		// Show a notification with action buttons
		const action = await vscode.window.showInformationMessage(
			`Command output displayed in new tab`,
			"Copy to Clipboard",
			"Save to File"
		);

		if (action === "Copy to Clipboard") {
			await vscode.env.clipboard.writeText(output);
			vscode.window.showInformationMessage("Output copied to clipboard");
		} else if (action === "Save to File") {
			const uri = await vscode.window.showSaveDialog({
				defaultUri: vscode.Uri.file(`command-output-${Date.now()}.txt`),
				filters: {
					'Text files': ['txt'],
					'All files': ['*']
				}
			});

			if (uri) {
				await vscode.workspace.fs.writeFile(uri, Buffer.from(content, 'utf8'));
				vscode.window.showInformationMessage(`Output saved to ${uri.fsPath}`);
			}
		}

		Logger.info(`Command output displayed for: ${command}`);

	} catch (error) {
		Logger.error("Failed to display command output", error);
		vscode.window.showErrorMessage(`Failed to display output: ${error}`);
	}
}
