import * as assert from "assert";
import * as vscode from "vscode";
import { TerminalManager } from "../../terminal/TerminalManager";
import { TerminalRegistry } from "../../terminal/TerminalRegistry";
import { Logger } from "../../services/logging/Logger";

suite("Extension Test Suite", () => {
	vscode.window.showInformationMessage("Start all tests.");

	setup(() => {
		// Initialize logger for tests
		Logger.initialize("Terminal Manager Tests");
	});

	teardown(() => {
		// Clean up after each test
		Logger.dispose();
	});

	test("Extension should be present", () => {
		assert.ok(vscode.extensions.getExtension("terminal-manager"));
	});

	test("TerminalManager should be instantiable", () => {
		const terminalManager = new TerminalManager();
		assert.ok(terminalManager);
	});

	test("TerminalRegistry should create terminals", () => {
		const terminalInfo = TerminalRegistry.createTerminal();
		assert.ok(terminalInfo);
		assert.ok(terminalInfo.terminal);
		assert.strictEqual(terminalInfo.busy, false);
		assert.strictEqual(terminalInfo.lastCommand, "");
		assert.ok(terminalInfo.id > 0);
		
		// Clean up
		terminalInfo.terminal.dispose();
	});

	test("TerminalRegistry should track terminals", () => {
		const terminalInfo1 = TerminalRegistry.createTerminal();
		const terminalInfo2 = TerminalRegistry.createTerminal();
		
		const allTerminals = TerminalRegistry.getAllTerminals();
		assert.ok(allTerminals.length >= 2);
		
		const foundTerminal1 = TerminalRegistry.getTerminal(terminalInfo1.id);
		const foundTerminal2 = TerminalRegistry.getTerminal(terminalInfo2.id);
		
		assert.ok(foundTerminal1);
		assert.ok(foundTerminal2);
		assert.strictEqual(foundTerminal1.id, terminalInfo1.id);
		assert.strictEqual(foundTerminal2.id, terminalInfo2.id);
		
		// Clean up
		terminalInfo1.terminal.dispose();
		terminalInfo2.terminal.dispose();
	});

	test("Logger should log messages", () => {
		// Test that logger doesn't throw errors
		assert.doesNotThrow(() => {
			Logger.debug("Test debug message");
			Logger.info("Test info message");
			Logger.warn("Test warning message");
			Logger.error("Test error message");
		});
	});

	test("Commands should be registered", async () => {
		const commands = await vscode.commands.getCommands(true);
		
		const expectedCommands = [
			"terminalManager.createTerminal",
			"terminalManager.runCommand",
			"terminalManager.getOutput",
			"terminalManager.listTerminals",
			"terminalManager.clearTerminal"
		];
		
		for (const expectedCommand of expectedCommands) {
			assert.ok(
				commands.includes(expectedCommand),
				`Command ${expectedCommand} should be registered`
			);
		}
	});

	test("Configuration should have default values", () => {
		const config = vscode.workspace.getConfiguration("terminalManager");
		
		assert.strictEqual(config.get("autoCompile"), true);
		assert.strictEqual(config.get("autoReload"), true);
		assert.strictEqual(config.get("shellIntegrationTimeout"), 4000);
		assert.strictEqual(config.get("maxTerminals"), 10);
	});
});
