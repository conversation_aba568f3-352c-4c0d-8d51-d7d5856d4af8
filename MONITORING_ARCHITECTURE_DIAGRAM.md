# Terminal Manager - 监听架构可视化

## 🏗️ 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                    VSCode Extension Host                        │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │              Terminal Manager Extension                  │   │
│  │                                                         │   │
│  │  ┌─────────────────┐    ┌─────────────────────────────┐ │   │
│  │  │ registerCommands│    │ setupGlobalTerminalMonitoring│ │   │
│  │  │                 │    │                             │ │   │
│  │  │ ┌─executeCommand │    │ ┌─onDidStartExecution       │ │   │
│  │  │ └─testSendText  │    │ └─onDidEndExecution         │ │   │
│  │  └─────────────────┘    └─────────────────────────────┘ │   │
│  │                                                         │   │
│  │  ┌─────────────────────────────────────────────────────┐ │   │
│  │  │              TerminalRegistry                       │ │   │
│  │  │                                                     │ │   │
│  │  │ ┌─Terminal #1 (ID:1, Name:"Terminal Manager #1")   │ │   │
│  │  │ ├─Terminal #2 (ID:2, Name:"Terminal Manager #2")   │ │   │
│  │  │ └─Terminal #3 (ID:3, Name:"SendText Test Terminal")│ │   │
│  │  └─────────────────────────────────────────────────────┘ │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    VSCode Shell Integration                     │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐             │
│  │ Terminal #1 │  │ Terminal #2 │  │ Terminal #3 │             │
│  │             │  │             │  │             │             │
│  │ PowerShell  │  │ PowerShell  │  │ PowerShell  │             │
│  │             │  │             │  │             │             │
│  │ ┌─────────┐ │  │ ┌─────────┐ │  │ ┌─────────┐ │             │
│  │ │ Command │ │  │ │ Command │ │  │ │ Command │ │             │
│  │ │Execution│ │  │ │Execution│ │  │ │Execution│ │             │
│  │ └─────────┘ │  │ └─────────┘ │  │ └─────────┘ │             │
│  └─────────────┘  └─────────────┘  └─────────────┘             │
└─────────────────────────────────────────────────────────────────┘
```

## 🔄 事件流程图

```
用户操作/API调用
        │
        ▼
┌─────────────────┐
│   命令触发      │
│                 │
│ • 手动输入      │
│ • sendText()    │
│ • runCommand()  │
│ • 粘贴命令      │
└─────────────────┘
        │
        ▼
┌─────────────────┐
│ Shell Integration│
│   检测命令执行   │
└─────────────────┘
        │
        ▼
┌─────────────────┐
│ VSCode API 事件 │
│                 │
│ onDidStartTerminal│
│ ShellExecution   │
└─────────────────┘
        │
        ▼
┌─────────────────┐
│  我们的监听器    │
│                 │
│ • 识别终端      │
│ • 生成执行ID    │
│ • 设置输出流    │
└─────────────────┘
        │
        ▼
┌─────────────────┐
│   输出处理      │
│                 │
│ • 异步流迭代    │
│ • 行分割处理    │
│ • 格式化显示    │
└─────────────────┘
        │
        ▼
┌─────────────────┐
│  OUTPUT面板     │
│                 │
│ [T1:001] output │
│ [T2:001] output │
│ [T3:001] output │
└─────────────────┘
```

## 🧵 线程和异步处理模型

```
主线程 (Event Loop)
│
├─ 监听器注册 (同步)
│  ├─ onDidStartTerminalShellExecution
│  └─ onDidEndTerminalShellExecution
│
├─ 事件回调 (同步)
│  ├─ 终端识别
│  ├─ 执行ID生成
│  └─ 输出流设置
│
└─ 异步任务队列
   │
   ├─ Terminal #1 输出流处理 (异步)
   │  └─ for await (const data of stream1) { ... }
   │
   ├─ Terminal #2 输出流处理 (异步)
   │  └─ for await (const data of stream2) { ... }
   │
   └─ Terminal #3 输出流处理 (异步)
      └─ for await (const data of stream3) { ... }

注意：所有异步任务都在同一个线程中执行，但不会相互阻塞
```

## 🎯 终端识别机制详解

```
命令执行事件 (e)
│
├─ e.terminal (VSCode Terminal 实例)
│  ├─ processId: 12345
│  ├─ name: "Terminal Manager #1"
│  └─ shellIntegration.cwd: "C:\project"
│
├─ e.execution (命令执行实例)
│  ├─ commandLine.value: "echo test"
│  └─ read(): AsyncIterable<string>
│
└─ 我们的识别过程
   │
   ├─ 步骤1: 通过 e.terminal 查找 TerminalRegistry
   │  └─ TerminalRegistry.getTerminalByVSCodeTerminal(e.terminal)
   │
   ├─ 步骤2: 获取终端信息
   │  ├─ terminalId: 1
   │  ├─ terminalName: "Terminal Manager #1"
   │  └─ terminalCwd: "C:\project"
   │
   └─ 步骤3: 生成执行ID
      └─ generateExecutionId(e) → "12345_1638360000000_echo_test"
```

## 📊 数据结构关系图

```
activeExecutions Map
│
├─ Key: "12345_1638360000000_echo_test"
│  └─ Value: {
│       execution: VSCodeExecutionObject,
│       startTime: Date,
│       commandLine: "echo test",
│       outputLines: 0
│     }
│
├─ Key: "12346_1638360001000_npm_install"
│  └─ Value: {
│       execution: VSCodeExecutionObject,
│       startTime: Date,
│       commandLine: "npm install",
│       outputLines: 15
│     }
│
└─ Key: "12347_1638360002000_git_status"
   └─ Value: {
        execution: VSCodeExecutionObject,
        startTime: Date,
        commandLine: "git status",
        outputLines: 8
      }

TerminalRegistry.terminals Array
│
├─ [0]: {
│    terminal: VSCodeTerminalObject1,
│    id: 1,
│    name: "Terminal Manager #1",
│    cwd: "C:\project",
│    busy: false,
│    createdAt: Date
│  }
│
├─ [1]: {
│    terminal: VSCodeTerminalObject2,
│    id: 2,
│    name: "Terminal Manager #2",
│    cwd: "C:\project",
│    busy: true,
│    createdAt: Date
│  }
│
└─ [2]: {
     terminal: VSCodeTerminalObject3,
     id: 3,
     name: "SendText Test Terminal",
     cwd: "C:\project",
     busy: false,
     createdAt: Date
   }
```

## 🔄 输出流处理详解

```
e.execution.read() 返回 AsyncIterable<string>
│
├─ 数据块1: "Hello\nWorld\n"
│  │
│  ├─ 分割: ["Hello", "World", ""]
│  │
│  ├─ 处理: 
│  │  ├─ "Hello" → [T1:001] Hello
│  │  └─ "World" → [T1:002] World
│  │
│  └─ 输出到 OUTPUT 面板
│
├─ 数据块2: "Line 3\nPartial"
│  │
│  ├─ 分割: ["Line 3", "Partial"]
│  │
│  ├─ 处理:
│  │  ├─ "Line 3" → [T1:003] Line 3
│  │  └─ "Partial" → [T1:004] Partial
│  │
│  └─ 输出到 OUTPUT 面板
│
└─ 数据块3: " line\nEnd\n"
   │
   ├─ 分割: [" line", "End", ""]
   │
   ├─ 处理:
   │  ├─ " line" → [T1:005]  line
   │  └─ "End" → [T1:006] End
   │
   └─ 输出到 OUTPUT 面板

注意：每个数据块的处理都是异步的，不会阻塞其他终端的处理
```

## 🎯 关键技术要点

### 1. 事件驱动 vs 轮询
```
❌ 轮询模式 (我们没有使用)
setInterval(() => {
    checkTerminalOutput();  // 定期检查
}, 1000);

✅ 事件驱动模式 (我们使用的)
vscode.window.onDidStartTerminalShellExecution((e) => {
    // VSCode 主动通知我们
});
```

### 2. 同步 vs 异步
```
❌ 同步处理 (会阻塞)
const output = getFullOutput();  // 等待命令完成
processOutput(output);

✅ 异步流处理 (不阻塞)
for await (const data of stream) {
    processChunk(data);  // 实时处理
}
```

### 3. 单一监听器 vs 多个监听器
```
✅ 单一全局监听器
onDidStartTerminalShellExecution((e) => {
    // 处理所有终端的所有命令
});

❌ 每个终端单独监听器 (复杂且低效)
terminals.forEach(terminal => {
    terminal.onCommand(...);  // 不存在这样的API
});
```

这个架构设计既高效又强大，能够处理复杂的多终端并发场景！🎊
