# Terminal Manager Extension Demo

This document demonstrates the key features of the Terminal Manager extension.

## Quick Start

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Compile the Extension**
   ```bash
   npm run compile
   ```

3. **Run in Development Mode**
   - Press `F5` to launch a new VSCode window with the extension loaded
   - Or use the "Run Extension" configuration in the Debug panel

## Core Features Demo

### 1. Terminal Management

**Create a New Terminal:**
- Open Command Palette (`Ctrl+Shift+P`)
- Run: `Terminal Manager: Create New Terminal`
- Enter working directory (defaults to workspace root)
- Terminal will be created and focused

**Alternative:** Use keyboard shortcut `Ctrl+Shift+T`

### 2. Command Execution

**Run Commands with Real-time Output:**
- Open Command Palette (`Ctrl+Shift+P`)
- Run: `Terminal Manager: Run Command in Terminal`
- Enter command (e.g., `npm install`, `dir`, `ls -la`)
- Enter working directory
- Watch real-time progress with cancellation support

**Alternative:** Use keyboard shortcut `Ctrl+Shift+R`

### 3. Terminal Output Management

**Get Terminal Output:**
- Open Command Palette (`Ctrl+Shift+P`)
- Run: `Terminal Manager: Get Terminal Output`
- Output will be displayed in a new document

**List All Terminals:**
- Open Command Palette (`Ctrl+Shift+P`)
- Run: `Terminal Manager: List All Terminals`
- Select a terminal to focus it
- See status (busy/available) and last command

### 4. Auto-Compilation (Development Feature)

The extension automatically compiles TypeScript files when they change:

1. **Enable Auto-Compilation:**
   ```json
   {
     "terminalManager.autoCompile": true
   }
   ```

2. **Test Auto-Compilation:**
   - Make a change to any `.ts` file in the `src` directory
   - Save the file
   - Watch the compilation status in the status bar
   - Check for compilation errors in the Problems panel

### 5. Hot-Reload (Development Feature)

In development mode, the extension automatically reloads when source files change:

1. **Enable Auto-Reload:**
   ```json
   {
     "terminalManager.autoReload": true
   }
   ```

2. **Test Auto-Reload:**
   - Make a change to any source file
   - Save the file
   - Extension will prompt to reload
   - Choose "Reload Now" or wait for automatic reload

## Configuration Options

Add these to your VSCode settings:

```json
{
  "terminalManager.autoCompile": true,
  "terminalManager.autoReload": true,
  "terminalManager.shellIntegrationTimeout": 4000,
  "terminalManager.maxTerminals": 10
}
```

## Example Use Cases

### 1. Development Workflow
```bash
# Create terminal in project root
Terminal Manager: Create New Terminal

# Install dependencies
Terminal Manager: Run Command in Terminal
> npm install

# Start development server
Terminal Manager: Run Command in Terminal
> npm run dev

# Run tests in another terminal
Terminal Manager: Create New Terminal
Terminal Manager: Run Command in Terminal
> npm test
```

### 2. Multi-Project Management
```bash
# Terminal for frontend
Terminal Manager: Create New Terminal
> /path/to/frontend

# Terminal for backend
Terminal Manager: Create New Terminal
> /path/to/backend

# Terminal for database
Terminal Manager: Create New Terminal
> /path/to/database
```

### 3. Build and Deploy
```bash
# Build project
Terminal Manager: Run Command in Terminal
> npm run build

# Check build output
Terminal Manager: Get Terminal Output

# Deploy
Terminal Manager: Run Command in Terminal
> npm run deploy
```

## Advanced Features

### 1. Shell Integration
- Automatic detection of command completion
- Real-time output streaming
- Error handling and reporting
- Command history tracking

### 2. Terminal Registry
- Centralized terminal management
- Automatic cleanup of closed terminals
- Terminal reuse based on working directory
- Busy state tracking

### 3. Development Tools
- TypeScript compilation monitoring
- File change detection
- Extension hot-reload
- Comprehensive logging

## Troubleshooting

### Common Issues

1. **Shell Integration Not Working:**
   - Ensure you're using a supported shell (PowerShell on Windows, bash/zsh on Unix)
   - Check the shell integration timeout setting
   - Restart VSCode if needed

2. **Auto-Compilation Failing:**
   - Check TypeScript configuration
   - Verify file permissions
   - Check the output channel for errors

3. **Commands Not Responding:**
   - Check if terminal is busy
   - Use "List All Terminals" to see terminal status
   - Create a new terminal if needed

### Debug Information

Enable debug logging by setting the log level in development mode:
- Extension logs appear in the "Terminal Manager" output channel
- Check the Developer Console for additional information

## Testing

Run the test suite:
```bash
npm test
```

This will:
- Compile the TypeScript code
- Run ESLint for code quality
- Execute unit tests
- Generate coverage reports

## Building for Production

Create a VSIX package:
```bash
npm run package
```

This creates a `.vsix` file that can be installed in VSCode using:
- Command Palette > "Extensions: Install from VSIX..."
- Or `code --install-extension terminal-manager-0.1.0.vsix`
