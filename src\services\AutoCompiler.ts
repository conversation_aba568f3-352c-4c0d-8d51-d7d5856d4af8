import * as vscode from "vscode";
import * as chokidar from "chokidar";
import * as path from "path";
import { Logger } from "./logging/Logger";

export class AutoCompiler implements vscode.Disposable {
	private watcher: chokidar.FSWatcher | undefined;
	private compileTimeout: NodeJS.Timeout | undefined;
	private isCompiling = false;
	private readonly debounceDelay = 1000; // 1 second debounce

	constructor(private context: vscode.ExtensionContext) {
		this.initialize();
	}

	private async initialize() {
		try {
			const workspaceFolders = vscode.workspace.workspaceFolders;
			if (!workspaceFolders || workspaceFolders.length === 0) {
				Logger.warn("No workspace folders found, auto-compilation disabled");
				return;
			}

			const rootPath = workspaceFolders[0].uri.fsPath;
			const srcPath = path.join(rootPath, "src");

			// Watch TypeScript files in src directory
			this.watcher = chokidar.watch("**/*.ts", {
				cwd: srcPath,
				ignored: [
					"**/*.test.ts",
					"**/*.spec.ts",
					"**/node_modules/**",
					"**/out/**",
					"**/.git/**"
				],
				ignoreInitial: true,
				persistent: true
			});

			this.watcher.on("change", (filePath) => {
				this.scheduleCompilation(filePath);
			});

			this.watcher.on("add", (filePath) => {
				this.scheduleCompilation(filePath);
			});

			this.watcher.on("unlink", (filePath) => {
				this.scheduleCompilation(filePath);
			});

			this.watcher.on("error", (error) => {
				Logger.error("File watcher error", error);
			});

			Logger.info(`Auto-compiler initialized, watching: ${srcPath}`);

		} catch (error) {
			Logger.error("Failed to initialize auto-compiler", error);
		}
	}

	private scheduleCompilation(filePath: string) {
		if (this.isCompiling) {
			Logger.debug(`Compilation already in progress, skipping for: ${filePath}`);
			return;
		}

		// Clear existing timeout
		if (this.compileTimeout) {
			clearTimeout(this.compileTimeout);
		}

		// Schedule compilation with debounce
		this.compileTimeout = setTimeout(() => {
			this.compile(filePath);
		}, this.debounceDelay);

		Logger.debug(`Scheduled compilation for: ${filePath}`);
	}

	private async compile(triggerFile: string) {
		if (this.isCompiling) {
			return;
		}

		this.isCompiling = true;
		Logger.info(`Starting compilation triggered by: ${triggerFile}`);

		try {
			// Show compilation progress
			await vscode.window.withProgress({
				location: vscode.ProgressLocation.Window,
				title: "Compiling TypeScript...",
				cancellable: false
			}, async (progress) => {
				progress.report({ message: "Running TypeScript compiler..." });

				// Run TypeScript compilation
				const result = await this.runTypeScriptCompiler();
				
				if (result.success) {
					Logger.info("Compilation completed successfully");
					this.showCompilationStatus("✅ Compilation successful", false);
				} else {
					Logger.error("Compilation failed", result.errors);
					this.showCompilationStatus("❌ Compilation failed", true);
					this.showCompilationErrors(result.errors);
				}
			});

		} catch (error) {
			Logger.error("Compilation process failed", error);
			this.showCompilationStatus("❌ Compilation error", true);
			vscode.window.showErrorMessage(`Compilation error: ${error}`);
		} finally {
			this.isCompiling = false;
		}
	}

	private async runTypeScriptCompiler(): Promise<{ success: boolean; errors: string[] }> {
		return new Promise((resolve) => {
			const workspaceFolders = vscode.workspace.workspaceFolders;
			if (!workspaceFolders) {
				resolve({ success: false, errors: ["No workspace folder found"] });
				return;
			}

			const rootPath = workspaceFolders[0].uri.fsPath;
			const terminal = vscode.window.createTerminal({
				name: "TypeScript Compiler",
				cwd: rootPath,
				hideFromUser: true
			});

			let output = "";
			let hasErrors = false;

			// Listen for terminal output (this is a simplified approach)
			// In a real implementation, you might want to use the terminal API more directly
			
			terminal.sendText("npx tsc --noEmit", true);

			// Wait for compilation to complete
			setTimeout(() => {
				terminal.dispose();
				
				// Check if there were any TypeScript errors by examining the problems panel
				const diagnostics = vscode.languages.getDiagnostics();
				const tsErrors: string[] = [];
				
				for (const [uri, diagnosticArray] of diagnostics) {
					if (uri.fsPath.endsWith(".ts")) {
						for (const diagnostic of diagnosticArray) {
							if (diagnostic.severity === vscode.DiagnosticSeverity.Error) {
								hasErrors = true;
								tsErrors.push(`${uri.fsPath}: ${diagnostic.message}`);
							}
						}
					}
				}

				resolve({
					success: !hasErrors,
					errors: tsErrors
				});
			}, 3000); // Wait 3 seconds for compilation
		});
	}

	private showCompilationStatus(message: string, isError: boolean) {
		// Show status in status bar
		const statusBarItem = vscode.window.createStatusBarItem(
			vscode.StatusBarAlignment.Left,
			100
		);
		
		statusBarItem.text = message;
		statusBarItem.color = isError ? new vscode.ThemeColor("errorForeground") : undefined;
		statusBarItem.show();

		// Hide after 3 seconds
		setTimeout(() => {
			statusBarItem.dispose();
		}, 3000);
	}

	private showCompilationErrors(errors: string[]) {
		if (errors.length === 0) {
			return;
		}

		const errorMessage = `TypeScript compilation failed with ${errors.length} error(s)`;
		vscode.window.showErrorMessage(errorMessage, "Show Details").then((selection) => {
			if (selection === "Show Details") {
				// Show errors in output channel
				const outputChannel = vscode.window.createOutputChannel("TypeScript Compilation Errors");
				outputChannel.clear();
				outputChannel.appendLine("TypeScript Compilation Errors:");
				outputChannel.appendLine("=" .repeat(50));
				
				errors.forEach((error, index) => {
					outputChannel.appendLine(`${index + 1}. ${error}`);
				});
				
				outputChannel.show();
			}
		});
	}

	dispose() {
		if (this.compileTimeout) {
			clearTimeout(this.compileTimeout);
		}
		
		if (this.watcher) {
			this.watcher.close();
		}
		
		Logger.info("Auto-compiler disposed");
	}
}
