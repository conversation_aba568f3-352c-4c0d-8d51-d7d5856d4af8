import * as vscode from "vscode";
export interface TerminalInfo {
    terminal: vscode.Terminal;
    busy: boolean;
    lastCommand: string;
    id: number;
    name: string;
    cwd: string;
    createdAt: Date;
    pendingCwdChange?: string;
    cwdResolved?: {
        resolve: () => void;
        reject: (error: Error) => void;
    };
}
export declare class TerminalRegistry {
    private static terminals;
    private static nextTerminalId;
    static createTerminal(cwd?: string | vscode.Uri | undefined, customName?: string): TerminalInfo;
    static getTerminal(id: number): TerminalInfo | undefined;
    static updateTerminal(id: number, updates: Partial<TerminalInfo>): void;
    static removeTerminal(id: number): void;
    static getAllTerminals(): TerminalInfo[];
    static getTerminalByVSCodeTerminal(vsCodeTerminal: vscode.Terminal): TerminalInfo | undefined;
    static getTerminalsByStatus(busy: boolean): TerminalInfo[];
    static getTerminalCount(): number;
    static createNewTerminal(cwd?: string, customName?: string): TerminalInfo;
    private static isTerminalClosed;
}
//# sourceMappingURL=TerminalRegistry.d.ts.map