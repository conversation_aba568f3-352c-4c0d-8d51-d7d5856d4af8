{"events": {"query-expfeature": {"owner": "sbatten", "comment": "Logs queries to the experiment service by feature for metric calculations", "abexp.queriedfeature": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The experimental feature being queried", "endPoint": "none"}}, "extensionGallery:install": {"owner": "sandy081", "success": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "duration": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "durationsinceupdate": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "errorcode": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "recommendationreason": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "verificationstatus": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "source": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "id": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "name": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "extensionversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "galleryid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "publisherid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "publishername": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "publisherdisplayname": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "isprereleaseversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "dependencies": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "issigned": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "index": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "querysource": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "queryactivityid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "extensionGallery:uninstall": {"owner": "sandy081", "success": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "duration": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "errorcode": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "id": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "name": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "extensionversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "galleryid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "publisherid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "publishername": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "publisherdisplayname": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "isprereleaseversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "dependencies": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "issigned": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "index": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "querysource": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "queryactivityid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "extensionGallery:update": {"owner": "sandy081", "success": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "duration": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "errorcode": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "verificationstatus": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "source": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "id": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "name": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "extensionversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "galleryid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "publisherid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "publishername": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "publisherdisplayname": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "isprereleaseversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "dependencies": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "issigned": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "index": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "querysource": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "queryactivityid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "galleryService:downloadVSIX": {"owner": "sandy081", "duration": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "id": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "name": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "extensionversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "galleryid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "publisherid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "publishername": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "publisherdisplayname": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "isprereleaseversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "dependencies": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "issigned": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "index": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "querysource": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "queryactivityid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "startupTimeVaried": {"owner": "j<PERSON>ken", "ellapsed": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "islatestversion": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "didusecacheddata": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "windowkind": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "windowcount": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "viewletid": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "panelid": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "editorids": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timers.ellapsedappready": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "timers.ellapsednlsgeneration": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "timers.ellapsedloadmainbundle": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "timers.ellapsedrunmainbundle": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "timers.ellapsedcrashreporter": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "timers.ellapsedmainserver": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "timers.ellapsedwindowcreate": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "timers.ellapsedwindowload": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "timers.ellapsedwindowloadtorequire": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "timers.ellapsedwaitforwindowconfig": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "timers.ellapsedstorageinit": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "timers.ellapsedworkspaceserviceinit": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "timers.ellapsedsharedprocesconnected": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "timers.ellapsedrequireduserdatainit": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "timers.ellapsedotheruserdatainit": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "timers.ellapsedrequire": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "timers.ellapsedextensions": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "timers.ellapsedextensionsready": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "timers.ellapsedviewletrestore": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "timers.ellapsedpanelrestore": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "timers.ellapsededitorrestore": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "timers.ellapsedworkbenchcontributions": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "timers.ellapsedworkbench": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "platform": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "release": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "arch": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "totalmem": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "freemem": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "cpus.count": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "cpus.speed": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "cpus.model": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "initialstartup": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "hasaccessibilitysupport": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "isvmlikelyhood": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "emptyworkbench": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "loadavg": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "isarm64emulated": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "meminfo.workingsetsize": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "meminfo.privatebytes": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "meminfo.sharedbytes": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}}, "debugSessionStart": {"owner": "connor4312", "type": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "breakpointcount": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "exceptionbreakpoints": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "watchexpressionscount": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "extensionname": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "endPoint": "none"}, "isbuiltin": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "launchjsonexists": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}}, "debugSessionStop": {"owner": "connor4312", "type": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "success": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "sessionlengthinseconds": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "breakpointcount": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "watchexpressionscount": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}}, "languagePackSuggestion:popup": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "userreaction": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "language": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "debug/didViewMemory": {"owner": "connor4312", "debugtype": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "workspce.tags": {"owner": "lramos15", "workbench.filestoopenorcreate": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workbench.filestodiff": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workbench.filestomerge": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.id": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "workspace.roots": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.empty": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.grunt": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gulp": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.jake": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.tsconfig": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.jsconfig": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.config.xml": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.vsc.extension": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.asp<NUMBER>": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.sln": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.unity": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.express": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.sails": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.koa": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.hapi": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.socket.io": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.restify": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.next": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.nuxt": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@nestjs/core": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.strapi": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.gatsby": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.rnpm-plugin-windows": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.react": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@angular/core": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.vue": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@anthropic-ai/sdk": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@anthropic-ai/tokenizer": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@arizeai/openinference-instrumentation-langchain": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@arizeai/openinference-instrumentation-openai": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@aws-sdk-client-bedrock-runtime": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@aws-sdk/client-bedrock": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.aws-sdk": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.aws-amplify-sdk": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/ai": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/core": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/cosmos": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/event": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/identity": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/keyvault": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/search": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/storage": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@google-cloud/aiplatform": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.azure": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.azure-storage": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@google-cloud/common": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.firebase": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.heroku-cli": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@huggingface/inference": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@microsoft/teams-js": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@microsoft/office-js": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@microsoft/office-js-helpers": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@types/office-js": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@types/office-runtime": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.office-ui-fabric-react": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@uifabric/icons": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@uifabric/merge-styles": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@uifabric/styling": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@uifabric/experiments": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@uifabric/utilities": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@microsoft/rush": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.lerna": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.just-task": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.beachball": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.electron": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.playwright": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.playwright-cli": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@playwright/test": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.playwright-core": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.playwright-chromium": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.playwright-firefox": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.playwright-webkit": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.cypress": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.chroma": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.faiss": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.fireworks-js": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@datastax/astra-db-ts": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.humanloop": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.langchain": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@langchain/anthropic": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.langsmith": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.llamaindex": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@mistralai/mistralai": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.milvus": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.mongodb": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.neo4j-driver": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.ollama": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.onnxruntime-node": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.onnxruntime-web": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.openai": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.pinecone": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.postgresql": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.pg": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.qdrant": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.redis": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@supabase/supabase-js": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@tensorflow/tfjs": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@xenova/transformers": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.weaviate-client": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@zilliz/milvus2-sdk-node": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.nightwatch": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.protractor": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.puppeteer": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.selenium-webdriver": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.tika": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.webdriverio": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.gherkin": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/app-configuration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/cosmos-sign": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/cosmos-language-service": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/synapse-spark": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/synapse-monitoring": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/synapse-managed-private-endpoints": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/synapse-artifacts": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/synapse-access-control": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/ai-metrics-advisor": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure-rest/ai-anomaly-detector": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure-rest/ai-content-safety": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure-rest/ai-document-intelligence": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure-rest/ai-document-translator": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure-rest/ai-personalizer": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure-rest/ai-translation-text": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure-rest/ai-vision-image-analysis": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/ai-anomaly-detector": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/ai-form-recognizer": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/ai-language-conversations": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/ai-language-text": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/ai-text-analytics": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/arm-botservice": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/arm-cognitiveservices": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/arm-machinelearning": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/cognitiveservices-contentmoderator": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/cognitiveservices-customvision-prediction": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/cognitiveservices-customvision-training": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/cognitiveservices-face": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/cognitiveservices-translatortext": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.microsoft-cognitiveservices-speech-sdk": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/service-bus": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/keyvault-secrets": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/keyvault-keys": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/keyvault-certificates": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/keyvault-admin": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/digital-twins-core": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/cognitiveservices-anomalydetector": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/core-xml": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/core-tracing": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/core-paging": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/core-https": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/core-client": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/core-asynciterator-polyfill": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/core-arm": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/amqp-common": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/core-lro": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/logger": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/core-http": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/core-auth": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/core-amqp": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/abort-controller": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/eventgrid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/storage-file-datalake": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/search-documents": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/storage-file": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/storage-datalake": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/storage-queue": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/storage-file-share": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/storage-blob-changefeed": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/storage-blob": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/cognitiveservices-formrecognizer": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/cognitiveservices-textanalytics": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/event-processor-host": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/schema-registry-avro": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/schema-registry": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/eventhubs-checkpointstore-blob": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/event-hubs": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/communication-signaling": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/communication-calling": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/communication-sms": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/communication-common": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/communication-chat": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/communication-administration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/attestation": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/data-tables": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure-rest/ai-inference": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure-rest/arm-appservice": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/arm-appservice": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/arm-appcontainers": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/arm-rediscache": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/arm-redisenterprisecache": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/arm-apimanagement": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/arm-logic": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/arm-dashboard": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/arm-signalr": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/arm-securitydevops": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/arm-labservices": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/web-pubsub": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/web-pubsub-client": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/web-pubsub-client-protobuf": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/web-pubsub-express": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/openai": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/arm-hybridkubernetes": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@azure/arm-kubernetesconfiguration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.react-native-macos": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.react-native-windows": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.npm.@google/generative-ai": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.bower": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.yeoman.code.ext": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.cordova.high": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.cordova.low": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.xamarin.android": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.xamarin.ios": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.android.cpp": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.reactnative": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.ionic": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": "true"}, "workspace.nativescript": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": "true"}, "workspace.java.pom": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.java.gradle": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.java.android": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.azure": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.javaee": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.jdbc": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.jpa": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.lombok": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.mockito": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.redis": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.springboot": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.sql": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.unittest": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.azure-cosmos": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.azure-storage": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.azure-servicebus": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.azure-eventhubs": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.langchain4j": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.springboot-ai": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.semantic-kernel": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.azure-ai-anomalydetector": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.azure-ai-formrecognizer": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.azure-ai-documentintelligence": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.azure-ai-translation-document": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.azure-ai-personalizer": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.azure-ai-translation-text": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.azure-ai-contentsafety": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.azure-ai-vision-imageanalysis": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.azure-ai-textanalytics": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.azure-search-documents": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.azure-ai-documenttranslator": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.azure-ai-vision-face": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.azure-ai-openai-assistants": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.azure-cognitiveservices": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.azure-cognitiveservices-speech": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.openai": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.azure-openai": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.azure-functions": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.quarkus": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.microprofile": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.micronaut": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.gradle.graalvm": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.azure": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.javaee": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.jdbc": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.jpa": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.lombok": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.mockito": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.redis": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.springboot": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.sql": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.unittest": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.azure-cosmos": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.azure-storage": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.azure-servicebus": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.azure-eventhubs": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.langchain4j": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.springboot-ai": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.semantic-kernel": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.azure-ai-anomalydetector": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.azure-ai-formrecognizer": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.azure-ai-documentintelligence": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.azure-ai-translation-document": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.azure-ai-personalizer": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.azure-ai-translation-text": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.azure-ai-contentsafety": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.azure-ai-vision-imageanalysis": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.azure-ai-textanalytics": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.azure-search-documents": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.azure-ai-documenttranslator": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.azure-ai-vision-face": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.azure-ai-openai-assistants": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.azure-cognitiveservices": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.azure-cognitiveservices-speech": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.openai": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.azure-openai": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.azure-functions": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.quarkus": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.microprofile": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.micronaut": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.pom.graalvm": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.requirements": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.requirements.star": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.pipfile": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.conda": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.setup": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.pyproject": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.manage": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.setupcfg": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.app": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.any-azure": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.pulumi-azure": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-ai": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-ai-inference": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-ai-language-conversations": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-ai-language-questionanswering": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-ai-ml": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-ai-contentsafety": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-ai-documentintelligence": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-ai-translation-text": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-ai-vision": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-cognitiveservices-language-luis": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-cognitiveservices-speech": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-cognitiveservices-vision-contentmoderator": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-cognitiveservices-vision-face": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-mgmt-cognitiveservices": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-mgmt-search": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-ai-translation-document": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-cognitiveservices": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-core": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-cosmos": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-devtools": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-elasticluster": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-event": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-eventgrid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-functions": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-graphrbac": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-identity": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-iothub-device-client": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-keyvault": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-loganalytics": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-mgmt": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-ml": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-monitor": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-mgmt-appcontainers": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-mgmt-redis": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-mgmt-redisenterprise": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-mgmt-apimanagement": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-mgmt-logic": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-appconfiguration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-appconfiguration-provider": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-mgmt-appconfiguration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-mgmt-dashboard": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-mgmt-signalr": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-messaging-webpubsubservice": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-mgmt-webpubsub": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-mgmt-securitydevops": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-mgmt-labservices": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-mgmt-web": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-search": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-servicebus": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-servicefabric": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-shell": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-storage": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-translator": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-mgmt-hybridkubernetes": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-mgmt-kubernetesconfiguration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.adal": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.pydocumentdb": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.botbuilder-core": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.botbuilder-schema": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.botframework-connector": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.playwright": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-synapse-nspkg": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-synapse-spark": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-synapse-artifacts": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-synapse-accesscontrol": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-synapse": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-cognitiveservices-vision-nspkg": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-cognitiveservices-search-nspkg": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-cognitiveservices-nspkg": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-cognitiveservices-language-nspkg": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-cognitiveservices-knowledge-nspkg": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-containerregistry": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-ai-metricsadvisor": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azureml-sdk": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-keyvault-nspkg": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-keyvault-secrets": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-keyvault-keys": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-keyvault-certificates": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-keyvault-administration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-digitaltwins-nspkg": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-digitaltwins-core": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-cognitiveservices-anomalydetector": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-ai-anomalydetector": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-applicationinsights": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-core-tracing-opentelemetry": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-core-tracing-opencensus": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-nspkg": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-common": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-storage-file-datalake": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-search-nspkg": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-search-documents": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-storage-nspkg": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-storage-file": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-storage-common": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-storage-queue": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-storage-file-share": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-storage-blob-changefeed": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-storage-blob": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-cognitiveservices-formrecognizer": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-ai-formrecognizer": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-ai-nspkg": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-cognitiveservices-language-textanalytics": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-ai-textanalytics": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-schemaregistry-avroserializer": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-schemaregistry": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-eventhub-checkpointstoreblob-aio": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-eventhub-checkpointstoreblob": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-eventhub": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-communication-nspkg": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-communication-sms": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-communication-chat": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-communication-administration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-security-attestation": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-communication-identity": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-communication-phonenumbers": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-communication-email": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-communication-rooms": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-communication-callautomation": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-confidentialledger": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-iot-deviceupdate": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-developer-loadtesting": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-monitor-query": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-monitor-ingestion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-schemaregistry-avroencoder": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-data-nspkg": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.azure-data-tables": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.arize": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.aporia": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.anthropic": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.deepchecks": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.fireworks-ai": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.transformers": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.humanloop": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.langchain": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.langchain-anthropic": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.langchain-fireworks": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.langchain-huggingface": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.llama-index": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.google-cloud-aiplatform": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.guidance": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.ollama": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.onnxruntime": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.openai": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.pymongo": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.pgvector": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.semantic-kernel": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.sentence-transformers": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.tika": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.trulens": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.trulens-eval": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.wandb": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.py.google-generativeai": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/storage/azblob": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/storage/azfile": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/storage/azqueue": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/storage/azdatalake": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/tracing/azotel": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/security/keyvault/azadmin": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/security/keyvault/azcertificates": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/security/keyvault/azkeys": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/security/keyvault/azsecrets": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/monitor/azquery": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/monitor/azingest": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/messaging/azeventhubs": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/messaging/azservicebus": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/data/azappconfig": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/data/azcosmos": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/data/aztables": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/containers/azcontainerregistry": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/ai/azopenai": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/azidentity": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/azcore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/iotfirmwaredefense/armiotfirmwaredefense": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/aad/armaad": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/addons/armaddons": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/advisor/armadvisor": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/agrifood/armagrifood": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/alertsmanagement/armalertsmanagement": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/analysisservices/armanalysisservices": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/apimanagement/armapimanagement": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/appcomplianceautomation/armappcomplianceautomation": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/appconfiguration/armappconfiguration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/appplatform/armappplatform": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/appservice/armappservice": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/applicationinsights/armapplicationinsights": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/azurearcdata/armazurearcdata": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/attestation/armattestation": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/authorization/armauthorization": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/automanage/armautomanage": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/automation/armautomation": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/azuredata/armazuredata": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/azurestackhci/armazurestackhci": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/avs/armavs": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/recoveryservices/armrecoveryservicesbackup": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/baremetalinfrastructure/armbaremetalinfrastructure": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/batch/armbatch": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/billing/armbilling": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/billingbenefits/armbillingbenefits": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/blockchain/armblockchain": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/blueprint/armblueprint": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/botservice/armbotservice": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/changeanalysis/armchangeanalysis": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/resources/armchanges": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/chaos/armchaos": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/search/armsearch": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/cognitiveservices/armcognitiveservices": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/commerce/armcommerce": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/communication/armcommunication": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/compute/armcompute": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/confidentialledger/armconfidentialledger": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/confluent/armconfluent": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/connectedvmware/armconnectedvmware": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/consumption/armconsumption": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/appcontainers/armappcontainers": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/containerinstance/armcontainerinstance": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/containerregistry/armcontainerregistry": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/containerservice/armcontainerservice": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/containerservicefleet/armcontainerservicefleet": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/cdn/armcdn": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/cosmos/armcosmos": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/cosmosforpostgresql/armcosmosforpostgresql": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/costmanagement/armcostmanagement": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/customproviders/armcustomproviders": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/customerinsights/armcustomerinsights": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/customerlockbox/armcustomerlockbox": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/databox/armdatabox": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/databoxedge/armdataboxedge": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/datacatalog/armdatacatalog": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/datafactory/armdatafactory": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/datalake-analytics/armdatalakeanalytics": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/datalake-store/armdatalakestore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/datamigration/armdatamigration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/dataprotection/armdataprotection": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/datashare/armdatashare": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/databricks/armdatabricks": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/datadog/armdatadog": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/delegatednetwork/armdelegatednetwork": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/deploymentmanager/armdeploymentmanager": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/resources/armdeploymentscripts": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/desktopvirtualization/armdesktopvirtualization": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/devcenter/armdevcenter": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/devhub/armdevhub": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/deviceprovisioningservices/armdeviceprovisioningservices": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/deviceupdate/armdeviceupdate": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/devops/armdevops": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/devtestlabs/armdevtestlabs": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/digitaltwins/armdigitaltwins": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/dns/armdns": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/dnsresolver/armdnsresolver": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/domainservices/armdomainservices": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/dynatrace/armdynatrace": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/edgeorder/armedgeorder": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/edgeorderpartner/armedgeorderpartner": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/education/armeducation": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/elastic/armelastic": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/elasticsan/armelasticsan": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/elasticsans/armelasticsans": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/engagementfabric/armengagementfabric": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/eventgrid/armeventgrid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/eventhub/armeventhub": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/extendedlocation/armextendedlocation": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/resources/armfeatures": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/fluidrelay/armfluidrelay": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/frontdoor/armfrontdoor": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/graphservices/armgraphservices": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/guestconfiguration/armguestconfiguration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/hanaonazure/armhanaonazure": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/hardwaresecuritymodules/armhardwaresecuritymodules": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/hdinsight/armhdinsight": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/healthbot/armhealthbot": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/healthcareapis/armhealthcareapis": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/hybridcompute/armhybridcompute": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/hybridconnectivity/armhybridconnectivity": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/hybridcontainerservice/armhybridcontainerservice": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/hybriddatamanager/armhybriddatamanager": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/hybridkubernetes/armhybridkubernetes": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/hybridnetwork/armhybridnetwork": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/iotcentral/armiotcentral": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/iothub/armiothub": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/iotsecurity/armiotsecurity": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/keyvault/armkeyvault": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/kubernetesconfiguration/armkubernetesconfiguration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/kusto/armkusto": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/labservices/armlabservices": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/resources/armlinks": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/loadtesting/armloadtesting": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/resources/armlocks": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/operationalinsights/armoperationalinsights": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/logic/armlogic": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/logz/armlogz": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/m365securityandcompliance/armm365securityandcompliance": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/machinelearning/armmachinelearning": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/machinelearningservices/armmachinelearningservices": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/maintenance/armmaintenance": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/resources/armmanagedapplications": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/solutions/armmanagedapplications": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/dashboard/armdashboard": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/managednetwork/armmanagednetwork": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/managednetworkfabric/armmanagednetworkfabric": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/msi/armmsi": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/managedservices/armmanagedservices": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/managementgroups/armmanagementgroups": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/managementpartner/armmanagementpartner": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/maps/armmaps": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/mariadb/armmariadb": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/marketplace/armmarketplace": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/marketplaceordering/armmarketplaceordering": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/mediaservices/armmediaservices": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/migrate/armmigrate": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/mixedreality/armmixedreality": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/mobilenetwork/armmobilenetwork": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/monitor/armmonitor": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/mysql/armmysql": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/mysql/armmysqlflexibleservers": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/netapp/armnetapp": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/network/armnetwork": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/networkcloud/armnetworkcloud": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/networkfunction/armnetworkfunction": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/newrelic/armnewrelicobservability": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/nginx/armnginx": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/notificationhubs/armnotificationhubs": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/oep/armoep": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/operationsmanagement/armoperationsmanagement": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/orbital/armorbital": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/paloaltonetworksngfw/armpanngfw": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/peering/armpeering": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/resources/armpolicy": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/policyinsights/armpolicyinsights": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/portal/armportal": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/postgresql/armpostgresql": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/postgresql/armpostgresqlflexibleservers": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/postgresqlhsc/armpostgresqlhsc": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/powerbiprivatelinks/armpowerbiprivatelinks": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/powerbidedicated/armpowerbidedicated": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/powerbiembedded/armpowerbiembedded": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/powerplatform/armpowerplatform": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/privatedns/armprivatedns": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/providerhub/armproviderhub": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/purview/armpurview": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/quantum/armquantum": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/liftrqumulo/armqumulo": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/quota/armquota": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/recoveryservices/armrecoveryservices": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/redhatopenshift/armredhatopenshift": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/redis/armredis": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/redisenterprise/armredisenterprise": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/relay/armrelay": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/reservations/armreservations": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/resourceconnector/armresourceconnector": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/resourcegraph/armresourcegraph": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/resourcehealth/armresourcehealth": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/resourcemover/armresourcemover": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/resources/armresources": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/saas/armsaas": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/scheduler/armscheduler": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/scvmm/armscvmm": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/security/armsecurity": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/securitydevops/armsecuritydevops": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/securityinsight/armsecurityinsight": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/securityinsights/armsecurityinsights": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/selfhelp/armselfhelp": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/serialconsole/armserialconsole": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/servicebus/armservicebus": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/servicefabric/armservicefabric": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/servicefabricmesh/armservicefabricmesh": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/servicelinker/armservicelinker": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/servicenetworking/armservicenetworking": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/signalr/armsignalr": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/recoveryservices/armrecoveryservicessiterecovery": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/sphere/armsphere": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/sql/armsql": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/sqlvirtualmachine/armsqlvirtualmachine": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/storage/armstorage": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/storagecache/armstoragecache": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/storageimportexport/armstorageimportexport": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/storagemover/armstoragemover": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/storagepool/armstoragepool": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/storagesync/armstoragesync": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/storsimple1200series/armstorsimple1200series": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/storsimple8000series/armstorsimple8000series": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/streamanalytics/armstreamanalytics": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/resources/armsubscriptions": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/subscription/armsubscription": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/support/armsupport": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/synapse/armsynapse": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/resources/armtemplatespecs": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/testbase/armtestbase": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/timeseriesinsights/armtimeseriesinsights": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/trafficmanager/armtrafficmanager": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/web/armweb": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/webpubsub/armwebpubsub": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/windowsesu/armwindowsesu": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/windowsiot/armwindowsiot": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/workloadmonitor/armworkloadmonitor": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "workspace.go.mod.github.com/azure/azure-sdk-for-go/sdk/resourcemanager/workloads/armworkloads": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}}, "workspace.remotes": {"owner": "lramos15", "domains": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "workspace.azure": {"owner": "lramos15", "node": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "java": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}}, "searchResultsFirstRender": {"owner": "rob<PERSON><PERSON>", "duration": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}}, "searchResultsFinished": {"owner": "rob<PERSON><PERSON>", "duration": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}}, "searchResultsShown": {"owner": "rob<PERSON><PERSON>", "count": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "filecount": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "duration": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "type": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "scheme": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "searchontypeenabled": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "options.isregexp": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "options.iswordmatch": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "options.wordseparators": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "options.ismultiline": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "options.iscasesensitive": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "options.issmartcase": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}}, "extensions:action:install": {"owner": "sandy081", "actionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "id": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "name": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "extensionversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "galleryid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "publisherid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "publishername": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "publisherdisplayname": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "isprereleaseversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "dependencies": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "issigned": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "index": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "querysource": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "queryactivityid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "extensionGallery:install:recommendations": {"owner": "sandy081", "recommendationreason": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "id": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "name": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "extensionversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "galleryid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "publisherid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "publishername": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "publisherdisplayname": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "isprereleaseversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "dependencies": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "issigned": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "index": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "querysource": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "queryactivityid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "extensionGallery:openExtension": {"owner": "sandy081", "recommendationreason": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "id": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "name": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "extensionversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "galleryid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "publisherid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "publishername": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "publisherdisplayname": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "isprereleaseversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "dependencies": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "issigned": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "index": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "querysource": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "queryactivityid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "terminalLatencyStats": {"owner": "<PERSON><PERSON><PERSON>", "min": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "max": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "median": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "count": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}, "predictionaccuracy": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none", "isMeasurement": true}}, "editorOpened": {"owner": "<PERSON><PERSON><PERSON>", "typeid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "target": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "resource.mimetype": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "resource.scheme": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "resource.ext": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "resource.path": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "resourcesecondary.mimetype": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "resourcesecondary.scheme": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "resourcesecondary.ext": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "resourcesecondary.path": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "workbenchActionExecuted": {"id": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The identifier of the action that was run.", "endPoint": "none"}, "from": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The name of the component the action was run from.", "endPoint": "none"}, "detail": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Optional details about how the action was run, e.g which keybinding was used.", "endPoint": "none"}, "owner": "<PERSON><PERSON><PERSON>", "comment": "Provides insight into actions that are executed within the workbench."}, "NotebookCellOutputRender": {"owner": "amunger", "comment": "Track performance data for output rendering", "outputsize": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Size of the output data buffer.", "isMeasurement": true, "endPoint": "none"}, "rendertime": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Time spent rendering output.", "isMeasurement": true, "endPoint": "none"}}, "openedChatCodeCitations": {"owner": "rob<PERSON><PERSON>", "comment": "Indicates when a user opens chat code citations"}, "chat/selectedTools": {"owner": "connor4312", "comment": "Details the capabilities of the MCP server", "enabled": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Number of enabled chat tools", "endPoint": "none"}, "total": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Number of total chat tools", "endPoint": "none"}}, "asyncTokenizationMismatchingTokens": {"owner": "hediet", "comment": "Used to see if async tokenization is bug-free"}, "terminal.suggest.acceptedCompletion": {"owner": "meganrogge", "comment": "This data is collected to understand the outcome of a terminal completion acceptance.", "kind": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The completion item's kind", "endPoint": "none"}, "outcome": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The outcome of the accepted completion", "endPoint": "none"}, "exitcode": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The exit code from the command", "endPoint": "none"}}, "terminal/quick-fix": {"owner": "meganrogge", "quickfixid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The quick fix ID", "endPoint": "none"}, "ranquickfix": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the quick fix was run", "endPoint": "none"}, "comment": "Terminal quick fixes"}, "terminal/openLink": {"owner": "tyriar", "comment": "When the user opens a link in the terminal", "linktype": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The type of link being opened", "endPoint": "none"}}, "testing.expandOlderResults": {"owner": "connor4312", "comment": "Records that test history was used"}, "inlineCompletionHover.shown": {"owner": "hediet", "comment": "This event tracks whenever an inline completion hover is shown."}, "Extension:ViewActivate": {"extensionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Id of the extension", "endPoint": "none"}, "id": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Id of the view", "endPoint": "none"}, "owner": "digitarald", "comment": "Helps to gain insights on what extension contributed views are most popular"}, "diffEditor.editorVisibleTime": {"owner": "hediet", "editorvisibletimems": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Indicates the time the diff editor was visible to the user", "endPoint": "none"}, "languageid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Indicates for which language the diff editor was shown", "endPoint": "none"}, "collapseunchangedregions": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Indicates whether unchanged regions were collapsed", "endPoint": "none"}, "comment": "This event gives insight about how long the diff editor was visible to the user."}, "automaticlanguagedetection.likelywrong": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Used to determine how often language detection is likely wrong.", "currentlanguageid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The language id we guessed.", "endPoint": "none"}, "nextlanguageid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The language id the user chose.", "endPoint": "none"}, "linecount": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The number of lines in the file.", "endPoint": "none"}, "modelpreference": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "What the user's model preference is.", "endPoint": "none"}}, "setUntitledDocumentLanguage": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Helps understand what the automatic language detection does for untitled files", "to": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Help understand effectiveness of automatic language detection", "endPoint": "none"}, "from": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Help understand effectiveness of automatic language detection", "endPoint": "none"}, "modelpreference": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Help understand effectiveness of automatic language detection", "endPoint": "none"}}, "workbenchEditorReopen": {"owner": "rebornix", "comment": "Identify how a document is reopened", "scheme": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "File system provider scheme for the resource", "endPoint": "none"}, "ext": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "File extension for the resource", "endPoint": "none"}, "from": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The editor view type the resource is switched from", "endPoint": "none"}, "to": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The editor view type the resource is switched to", "endPoint": "none"}}, "diffEditor.computeDiff": {"owner": "hediet", "timems": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To understand if the new diff algorithm is slower/faster than the old one", "endPoint": "none"}, "timedout": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To understand how often the new diff algorithm times out", "endPoint": "none"}, "detectedmoves": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To understand how often the new diff algorithm detects moves", "endPoint": "none"}, "comment": "This event gives insight about the performance of the new diff algorithm."}, "terminal/shellIntegrationActivationSucceeded": {"owner": "meganrogge", "comment": "Indicates shell integration was activated"}, "terminal/shellIntegrationActivationTimeout": {"owner": "meganrogge", "comment": "Indicates shell integration activation timeout"}, "bracketPairColorizerTwoUsage": {"owner": "hediet", "nativecolorizationenabled": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether or not built-in bracket pair colorization is being used", "endPoint": "none"}, "comment": "We use this to understand how many users have the bracket pair colorizer extension installed (and how many of them have native bracket pair colorization enabled), as the extension does not do anything if native bracket pair colorization is enabled."}, "ManageExtensionClick": {"extensionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The extension the user went to manage.", "endPoint": "none"}, "owner": "rzhao271", "comment": "Event used to gain insights into when users interact with an extension management setting"}, "DismissExtensionClick": {"extensionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The extension the user went to manage.", "endPoint": "none"}, "owner": "rzhao271", "comment": "Event used to gain insights into when users interact with an extension management setting"}, "settingsEditor.settingModified": {"key": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The setting that is being modified.", "endPoint": "none"}, "groupid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the setting is from the local search or remote search provider, if applicable.", "endPoint": "none"}, "nlpindex": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The index of the setting in the remote search provider results, if applicable.", "endPoint": "none"}, "displayindex": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The index of the setting in the combined search results, if applicable.", "endPoint": "none"}, "showconfiguredonly": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the user is in the modified view, which shows configured settings only.", "endPoint": "none"}, "isreset": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Identifies whether a setting was reset to its default value.", "endPoint": "none"}, "target": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The scope of the setting, such as user or workspace.", "endPoint": "none"}, "owner": "rzhao271", "comment": "Event emitted when the user modifies a setting in the Settings editor"}, "languageModelToolInvoked": {"result": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether invoking the LanguageModelTool resulted in an error.", "endPoint": "none"}, "chatsessionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The ID of the chat session that the tool was used within, if applicable.", "endPoint": "none"}, "toolid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The ID of the tool used.", "endPoint": "none"}, "toolextensionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The extension that contributed the tool.", "endPoint": "none"}, "toolsourcekind": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The source (mcp/extension/internal) of the tool.", "endPoint": "none"}, "owner": "rob<PERSON><PERSON>", "comment": "Provides insight into the usage of language model tools."}, "chatEditing/workingSetSize": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Information about the working set size in a chat editing request", "originalsize": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The number of files that the user tried to attach in their editing request.", "endPoint": "none"}, "actualsize": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The number of files that were actually sent in their editing request.", "endPoint": "none"}}, "chatStatus.settingChanged": {"owner": "bpasero", "comment": "Provides insight into chat settings changed from the chat status entry.", "settingidentifier": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The identifier of the setting that changed.", "endPoint": "none"}, "settingmode": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The optional editor language for which the setting changed.", "endPoint": "none"}, "settingenablement": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the setting got enabled or disabled.", "endPoint": "none"}}, "commandCenter.chatInstall": {"owner": "bpasero", "comment": "Provides insight into chat installation.", "installresult": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the extension was installed successfully, cancelled or failed to install.", "endPoint": "none"}, "installduration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The duration it took to install the extension.", "endPoint": "none"}, "signuperrorcode": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The error code in case of an error signing up.", "endPoint": "none"}}, "copilot.attachImage": {"currentmodel": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The model at the point of attaching the image.", "endPoint": "none"}, "supportsvision": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the current model supports vision or not.", "endPoint": "none"}, "owner": "justschen", "comment": "Event used to gain insights when images are attached, and if the model supported vision or not."}, "chatSessionStoreError": {"owner": "rob<PERSON><PERSON>", "comment": "Detect issues related to managing chat sessions", "reason": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Info about the error that occurred", "endPoint": "none"}, "fileoperationreason": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "An error code from the file service", "endPoint": "none"}}, "interactiveSessionVote": {"direction": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the user voted up or down.", "endPoint": "none"}, "agentid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The ID of the chat agent that this vote is for.", "endPoint": "none"}, "command": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The name of the slash command that this vote is for.", "endPoint": "none"}, "reason": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The reason selected by the user for voting down.", "endPoint": "none"}, "owner": "rob<PERSON><PERSON>", "comment": "Provides insight into the performance of Chat agents."}, "interactiveSessionCopy": {"copykind": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "How the copy was initiated.", "endPoint": "none"}, "agentid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The ID of the chat agent that the copy acted on.", "endPoint": "none"}, "command": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The name of the slash command the copy acted on.", "endPoint": "none"}, "owner": "rob<PERSON><PERSON>", "comment": "Provides insight into the usage of Chat features."}, "interactiveSessionInsert": {"newfile": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the code was inserted into a new untitled file.", "endPoint": "none"}, "agentid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The ID of the chat agent that this insertion is for.", "endPoint": "none"}, "command": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The name of the slash command that this insertion is for.", "endPoint": "none"}, "owner": "rob<PERSON><PERSON>", "comment": "Provides insight into the usage of Chat features."}, "interactiveSessionApply": {"newfile": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the code was inserted into a new untitled file.", "endPoint": "none"}, "agentid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The ID of the chat agent that this insertion is for.", "endPoint": "none"}, "command": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The name of the slash command that this insertion is for.", "endPoint": "none"}, "codemapper": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The code mapper that wa used to compute the edit.", "endPoint": "none"}, "editsproposed": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether there was a change proposed to the user.", "endPoint": "none"}, "owner": "a<PERSON><PERSON><PERSON>", "comment": "Provides insight into the usage of Chat features."}, "interactiveSessionRunInTerminal": {"languageid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The language of the code that was run in the terminal.", "endPoint": "none"}, "agentid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The ID of the related chat agent.", "endPoint": "none"}, "command": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The name of the related slash command.", "endPoint": "none"}, "owner": "rob<PERSON><PERSON>", "comment": "Provides insight into the usage of Chat features."}, "chatFollowupClicked": {"agentid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The ID of the related chat agent.", "endPoint": "none"}, "command": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The name of the related slash command.", "endPoint": "none"}, "owner": "rob<PERSON><PERSON>", "comment": "Provides insight into the usage of Chat features."}, "chatFollowupsRetrieved": {"agentid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The ID of the related chat agent.", "endPoint": "none"}, "command": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The name of the related slash command.", "endPoint": "none"}, "numfollowups": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The number of followup prompts returned by the agent.", "endPoint": "none"}, "owner": "rob<PERSON><PERSON>", "comment": "Provides insight into the usage of Chat features."}, "interactiveSessionProviderInvoked": {"timetofirstprogress": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The time in milliseconds from invoking the provider to getting the first data.", "endPoint": "none"}, "totaltime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The total time it took to run the provider's `provideResponseWithProgress`.", "endPoint": "none"}, "result": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether invoking the ChatProvider resulted in an error.", "endPoint": "none"}, "requesttype": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The type of request that the user made.", "endPoint": "none"}, "chatsessionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "A random ID for the session.", "endPoint": "none"}, "agent": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The type of agent used.", "endPoint": "none"}, "agentextensionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The extension that contributed the agent.", "endPoint": "none"}, "slashcommand": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The type of slashCommand used.", "endPoint": "none"}, "location": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The location at which chat request was made.", "endPoint": "none"}, "citations": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The number of public code citations that were returned with the response.", "endPoint": "none"}, "numcodeblocks": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The number of code blocks in the response.", "endPoint": "none"}, "isparticipantdetected": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the participant was automatically detected.", "endPoint": "none"}, "enablecommanddetection": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether participation detection was disabled for this invocation.", "endPoint": "none"}, "attachmentkinds": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The types of variables/attachments that the user included with their query.", "endPoint": "none"}, "owner": "rob<PERSON><PERSON>", "comment": "Provides insight into the performance of Chat agents."}, "chatInstallEntitlement": {"tid": {"classification": "EndUserPseudonymizedInformation", "purpose": "BusinessInsight", "comment": "The anonymized analytics id returned by the service", "endpoint": "GoogleAnalyticsId", "endPoint": "none"}, "entitlement": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Flag indicating the chat entitlement state", "endPoint": "none"}, "quotachat": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The number of chat requests available to the user", "endPoint": "none"}, "quotapremiumchat": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The number of premium chat requests available to the user", "endPoint": "none"}, "quotacompletions": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The number of code completions available to the user", "endPoint": "none"}, "quotaresetdate": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The date the quota will reset", "endPoint": "none"}, "owner": "bpasero", "comment": "Reporting chat entitlements"}, "workspaceTrustDisabled": {"owner": "sbatten", "comment": "Logged when workspace trust is disabled", "reason": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The reason workspace trust is disabled. e.g. cli or setting", "endPoint": "none"}}, "workspaceTrustFolderCounts": {"owner": "sbatten", "comment": "Information about the workspaces trusted on the machine", "trustedfolderscount": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The number of trusted folders on the machine", "endPoint": "none"}}, "workspaceTrustStateChanged": {"owner": "sbatten", "comment": "Logged when the workspace transitions between trusted and restricted modes", "workspaceid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "An id of the workspace", "endPoint": "none"}, "istrusted": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "true if the workspace is trusted", "endPoint": "none"}}, "workspaceFolderDepthBelowTrustedFolder": {"owner": "sbatten", "comment": "Some metrics on the trusted workspaces folder structure", "trustedfolderdepth": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The number of directories deep of the trusted path", "endPoint": "none"}, "workspacefolderdepth": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The number of directories deep of the workspace path", "endPoint": "none"}, "delta": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The difference between the trusted path and the workspace path directories depth", "endPoint": "none"}}, "releaseNotesSettingAction": {"owner": "alexr00", "comment": "Used to understand if the action to update settings from the release notes is used.", "settingid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The id of the setting that was clicked on in the release notes", "endPoint": "none"}}, "mergeEditor.opened": {"owner": "hediet", "conflictcount": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To understand how many conflicts typically occur", "endPoint": "none"}, "combinableconflictcount": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To evaluate how useful the smart-merge feature is", "endPoint": "none"}, "basevisible": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To understand how many users use the base view to solve a conflict", "endPoint": "none"}, "iscolumnview": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To gain insight which layout should be default", "endPoint": "none"}, "basetop": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To gain insight which layout should be default for the base view", "endPoint": "none"}, "comment": "This event tracks when a user opens a 3 way merge editor. The associated data helps to fine-tune the merge editor."}, "mergeEditor.layoutChanged": {"owner": "hediet", "basevisible": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To understand how many users use the base view to solve a conflict", "endPoint": "none"}, "iscolumnview": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To gain insight which layout should be default", "endPoint": "none"}, "basetop": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To gain insight which layout should be default for the base view", "endPoint": "none"}, "comment": "This event tracks when a user changes the layout of the 3 way merge editor. This is useful to understand what layout should be default."}, "mergeEditor.closed": {"owner": "hediet", "conflictcount": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To understand how many conflicts typically occur", "endPoint": "none"}, "combinableconflictcount": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To evaluate how useful the smart-merge feature is", "endPoint": "none"}, "durationopenedsecs": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Indicates how long the merge editor was open before it was closed. This can be compared with the inline experience to investigate time savings.", "endPoint": "none"}, "remainingconflictcount": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Indicates how many conflicts were skipped. Should be zero for a successful merge.", "endPoint": "none"}, "accepted": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Indicates if the user completed the merge successfully or just closed the editor", "endPoint": "none"}, "conflictsresolvedwithbase": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To understand how many conflicts are resolved with base", "endPoint": "none"}, "conflictsresolvedwithinput1": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To understand how many conflicts are resolved with input1", "endPoint": "none"}, "conflictsresolvedwithinput2": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To understand how many conflicts are resolved with input2", "endPoint": "none"}, "conflictsresolvedwithsmartcombination": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To understand how many conflicts are resolved with smart combination", "endPoint": "none"}, "manuallysolvedconflictcountthatequalnone": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Indicates how many conflicts were solved manually that are not recognized by the merge editor.", "endPoint": "none"}, "manuallysolvedconflictcountthatequalsmartcombine": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Indicates how many conflicts were solved manually that equal the smart combination of the inputs.", "endPoint": "none"}, "manuallysolvedconflictcountthatequalinput1": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Indicates how many conflicts were solved manually that equal just input 1", "endPoint": "none"}, "manuallysolvedconflictcountthatequalinput2": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Indicates how many conflicts were solved manually that equal just input 2", "endPoint": "none"}, "manuallysolvedconflictcountthatequalnoneandstartedwithbase": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Indicates how many manually solved conflicts that are not recognized started with base", "endPoint": "none"}, "manuallysolvedconflictcountthatequalnoneandstartedwithinput1": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Indicates how many manually solved conflicts that are not recognized started with input1", "endPoint": "none"}, "manuallysolvedconflictcountthatequalnoneandstartedwithinput2": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Indicates how many manually solved conflicts that are not recognized started with input2", "endPoint": "none"}, "manuallysolvedconflictcountthatequalnoneandstartedwithbothnonsmart": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Indicates how many manually solved conflicts that are not recognized started with both (non-smart combination)", "endPoint": "none"}, "manuallysolvedconflictcountthatequalnoneandstartedwithbothsmart": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Indicates how many manually solved conflicts that are not recognized started with both (smart-combination)", "endPoint": "none"}, "comment": "This event tracks when a user closes a merge editor. It also tracks how the user solved the merge conflicts. This data can be used to improve the UX of the merge editor. This event will be fired rarely (less than 200k per week)"}, "mergeEditor.action.accept": {"owner": "hediet", "otheraccepted": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Indicates if the user already accepted the other side", "endPoint": "none"}, "isinput1": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Indicates if the user accepted input 1 or input 2", "endPoint": "none"}, "comment": "This event tracks when a user accepts one side of a conflict."}, "mergeEditor.action.smartCombination": {"owner": "hediet", "otheraccepted": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Indicates if the user immediately clicks on accept both or only after the other side has been accepted", "endPoint": "none"}, "comment": "This event tracks when the user clicks on \\\"Accept Both\\\"."}, "mergeEditor.action.remove": {"owner": "hediet", "otheraccepted": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Indicates if the user accepted the other side", "endPoint": "none"}, "isinput1": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Indicates if the user accepted input 1 or input 2", "endPoint": "none"}, "comment": "This event tracks when a user un-accepts one side of a conflict."}, "mergeEditor.action.resetToBase": {"owner": "hediet", "comment": "This event tracks when the user invokes \\\"Reset To Base\\\"."}, "mergeEditor.action.goToNextConflict": {"owner": "hediet", "comment": "This event tracks when the user navigates to the next conflict\\\"."}, "mergeEditor.action.goToPreviousConflict": {"owner": "hediet", "comment": "This event tracks when the user navigates to the previous conflict\\\"."}, "mergeEditor.action.conflictCounterClicked": {"owner": "hediet", "comment": "This event tracks when the user clicks on the conflict counter to navigate to the next conflict."}, "terminal/createInstance": {"owner": "tyriar", "comment": "Track details about terminal creation, such as the shell type", "shelltype": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The detected shell type for the terminal.", "endPoint": "none"}, "prompttype": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The detected prompt type for the terminal.", "endPoint": "none"}, "iscustomptyimplementation": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the terminal was using a custom PTY implementation.", "endPoint": "none"}, "isextensionownedterminal": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the terminal was created by an extension.", "endPoint": "none"}, "isloginshell": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the arguments contain -l or --login.", "endPoint": "none"}, "isreconnect": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the terminal is reconnecting to an existing instance.", "endPoint": "none"}, "shellintegrationquality": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The shell integration quality (rich=2, basic=1 or none=0).", "endPoint": "none"}, "shellintegrationinjected": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the shell integration script was injected.", "endPoint": "none"}, "shellintegrationinjectionfailurereason": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Info about shell integration injection.", "endPoint": "none"}}, "terminal/shellIntegrationActivationFailureCustomArgs": {"owner": "meganrogge", "comment": "Indicates shell integration was not activated because of custom args"}, "terminal/shellIntegrationFailureProcessExit": {"owner": "meganrogge", "comment": "Indicates the process exited when created with shell integration args"}, "searchEditor/saveSearchResults": {"owner": "rob<PERSON><PERSON>", "comment": "Fired when a search editor is saved"}, "searchEditor/openNewSearchEditor": {"owner": "rob<PERSON><PERSON>", "comment": "Fired when a search editor is opened"}, "searchEditor/createEditorFromSearchResult": {"owner": "rob<PERSON><PERSON>", "comment": "Fired when a search editor is opened from the search view"}, "speechToTextSession": {"owner": "bpasero", "comment": "An event that fires when a speech to text session is created", "context": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Context of the session.", "endPoint": "none"}, "sessionduration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Duration of the session.", "endPoint": "none"}, "sessionrecognized": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "If speech was recognized.", "endPoint": "none"}, "sessionerror": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "If speech resulted in error.", "endPoint": "none"}, "sessioncontentlength": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Length of the recognized text.", "endPoint": "none"}, "sessionlanguage": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Configured language for the session.", "endPoint": "none"}}, "textToSpeechSession": {"owner": "bpasero", "comment": "An event that fires when a text to speech session is created", "context": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Context of the session.", "endPoint": "none"}, "sessionduration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Duration of the session.", "endPoint": "none"}, "sessionerror": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "If speech resulted in error.", "endPoint": "none"}, "sessionlanguage": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Configured language for the session.", "endPoint": "none"}}, "keywordRecognition": {"owner": "bpasero", "comment": "An event that fires when a speech keyword detection is started", "keywordrecognized": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "If the keyword was recognized.", "endPoint": "none"}}, "gettingStarted.didAutoOpenWalkthrough": {"owner": "lramos15", "comment": "When a walkthrough is opened upon extension installation", "id": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "owner": "lramos15", "comment": "Used to understand what walkthroughs are consulted most frequently", "endPoint": "none"}}, "gettingStarted.experimentCohort": {"owner": "bhavy<PERSON>", "comment": "Records which experiment cohort the user is in for getting started experience", "cohort": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The exact cohort number for the user", "endPoint": "none"}, "experimentgroup": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The experiment group the user is in", "endPoint": "none"}}, "gettingStarted.ActionExecuted": {"command": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "comment": "The command being executed on the getting started page.", "endPoint": "none"}, "walkthroughid": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "comment": "The walkthrough which the command is in", "endPoint": "none"}, "argument": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "comment": "The arguments being passed to the command", "endPoint": "none"}, "owner": "lramos15", "comment": "Help understand what actions are most commonly taken on the getting started page"}, "galleryService:updateMetadata": {"owner": "sandy081", "comment": "Report when a request is made to update metadata of an installed extension"}, "extensions:updatecheckonproductupdate": {"owner": "sandy081", "comment": "Report when update check is triggered on product update"}, "installedExtensions": {"owner": "digitarald", "comment": "Helps to understand which extensions are the most actively used.", "extensionids": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "comment": "The list of extension ids that are installed.", "endPoint": "none"}, "count": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "comment": "The number of extensions that are installed.", "endPoint": "none"}}, "extensions:autorestart": {"owner": "sandy081", "comment": "Report when extensions are auto restarted", "count": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Number of extensions auto restarted", "endPoint": "none"}, "auto": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the restart was triggered automatically", "endPoint": "none"}}, "galleryService:checkingForUpdates": {"owner": "sandy081", "comment": "Report when a request is made to check for updates of extensions", "count": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Number of extensions to check update", "endPoint": "none"}}, "extensionsView:MarketplaceSearchFinished": {}, "extensions:runtimestate:action": {"owner": "sandy081", "comment": "Extension runtime state action event", "action": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Executed action", "endPoint": "none"}}, "extensionRecommendations:popup": {"owner": "sandy081", "comment": "Response information when an extension is recommended", "userreaction": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "User reaction after showing the recommendation prompt. Eg., install, cancel, show, neverShowAgain", "endPoint": "none"}, "extensionid": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "comment": "Id of the extension that is recommended", "endPoint": "none"}, "source": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The source from which this recommendation is coming from. Eg., file, exe.,", "endPoint": "none"}}, "extensionWorkspaceRecommendations:popup": {"owner": "sandy081", "comment": "Response information when a recommendation from workspace is recommended", "userreaction": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "User reaction after showing the recommendation prompt. Eg., install, cancel, show, neverShowAgain", "endPoint": "none"}}, "exthostunresponsive": {"owner": "j<PERSON>ken", "comment": "Profiling data that was collected while the extension host was unresponsive", "profilingsessionid": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Identifier of a profiling session", "endPoint": "none"}, "duration": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Duration for which the extension host was unresponsive", "endPoint": "none"}, "data": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Extensions ids and core parts that were active while the extension host was frozen", "endPoint": "none"}, "id": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Top extensions id that took most of the duration", "endPoint": "none"}}, "searchKeywordClick": {"owner": "osortega", "comment": "Fired when the user clicks on a keyword suggestion", "index": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "isMeasurement": true, "comment": "The index of the keyword clicked", "endPoint": "none"}, "maxkeywords": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "isMeasurement": true, "comment": "The total number of suggested keywords", "endPoint": "none"}}, "windowsEdition": {"owner": "sbatten", "comment": "Information about the Windows edition.", "edition": {"classification": "SystemMetaData", "purpose": "BusinessInsight", "comment": "The Windows edition.", "endPoint": "none"}}, "sync/showConflicts": {"owner": "sandy081", "comment": "Response information when conflict happens during settings sync", "source": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "settings sync resource. eg., settings, keybindings...", "endPoint": "none"}, "action": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "action taken while resolving conflicts. Eg: acceptLocal, acceptRemote", "endPoint": "none"}}, "interactiveEditor/session": {"owner": "j<PERSON>ken", "comment": "Data about an interaction editor session", "extension": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The extension providing the data", "endPoint": "none"}, "rounds": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Number of request that were made", "endPoint": "none"}, "undos": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Requests that have been undone", "endPoint": "none"}, "edits": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Did edits happen while the session was active", "endPoint": "none"}, "unstashed": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "How often did this session become stashed and resumed", "endPoint": "none"}, "finishedbyedit": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Did edits cause the session to terminate", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "When the session started", "endPoint": "none"}, "endtime": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "When the session ended", "endPoint": "none"}, "acceptedhunks": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Number of accepted hunks", "endPoint": "none"}, "discardedhunks": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Number of discarded hunks", "endPoint": "none"}, "responsetypes": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Comma separated list of response types like edits, message, mixed", "endPoint": "none"}}, "workspaceLoad": {"owner": "bpasero", "emptyworkbench": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether a folder or workspace is opened or not.", "endPoint": "none"}, "windowsize.innerheight": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The size of the window.", "endPoint": "none", "owner": "bpasero"}, "windowsize.innerwidth": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The size of the window.", "endPoint": "none", "owner": "bpasero"}, "windowsize.outerheight": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The size of the window.", "endPoint": "none", "owner": "bpasero"}, "windowsize.outerwidth": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The size of the window.", "endPoint": "none", "owner": "bpasero"}, "workbench.filestoopenorcreate": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Number of files that should open or be created.", "endPoint": "none"}, "workbench.filestodiff": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Number of files that should be compared.", "endPoint": "none"}, "workbench.filestomerge": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Number of files that should be merged.", "endPoint": "none"}, "customkeybindingscount": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Number of custom keybindings", "endPoint": "none"}, "theme": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The current theme of the window.", "endPoint": "none"}, "language": {"classification": "SystemMetaData", "purpose": "BusinessInsight", "comment": "The display language of the window.", "endPoint": "none"}, "pinnedviewlets": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The identifiers of views that are pinned.", "endPoint": "none"}, "restoredviewlet": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The identifier of the view that is restored.", "endPoint": "none"}, "restorededitors": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The number of editors that restored.", "endPoint": "none"}, "startupkind": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "How the window was opened, e.g via reload or not.", "endPoint": "none"}, "comment": "Metadata around the workspace that is being loaded into a window."}, "fileGet": {"owner": "<PERSON><PERSON><PERSON>", "comment": "Track when a file was read, for example from an editor.", "mimetype": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The language type of the file (for example XML).", "endPoint": "none"}, "ext": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The file extension of the file (for example xml).", "endPoint": "none"}, "path": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The path of the file as a hash.", "endPoint": "none"}, "reason": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The reason why a file is read or written. Allows to e.g. distinguish auto save from normal save.", "endPoint": "none"}, "allowlistedjson": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The name of the file but only if it matches some well known file names such as package.json or tsconfig.json.", "endPoint": "none"}}, "filePUT": {"owner": "<PERSON><PERSON><PERSON>", "comment": "Track when a file was written to, for example from an editor.", "mimetype": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The language type of the file (for example XML).", "endPoint": "none"}, "ext": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The file extension of the file (for example xml).", "endPoint": "none"}, "path": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The path of the file as a hash.", "endPoint": "none"}, "reason": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The reason why a file is read or written. Allows to e.g. distinguish auto save from normal save.", "endPoint": "none"}, "allowlistedjson": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The name of the file but only if it matches some well known file names such as package.json or tsconfig.json.", "endPoint": "none"}}, "workbench.activityBar.location": {"owner": "sandy081", "comment": "This is used to know where activity bar is shown in the workbench.", "settingvalue": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "value of the setting", "endPoint": "none"}, "source": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "source of the setting", "endPoint": "none"}}, "extensions.autoUpdate": {"owner": "sandy081", "comment": "This is used to know if extensions are getting auto updated or not", "settingvalue": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "value of the setting", "endPoint": "none"}, "source": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "source of the setting", "endPoint": "none"}}, "editor.stickyScroll.enabled": {"owner": "aiday-mar", "comment": "This is used to know if editor sticky scroll is enabled or not", "settingvalue": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "value of the setting", "endPoint": "none"}, "source": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "source of the setting", "endPoint": "none"}}, "typescript.experimental.expandableHover": {"owner": "aiday-mar", "comment": "This is used to know if the TypeScript expandbale hover is enabled or not", "settingvalue": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "value of the setting", "endPoint": "none"}, "source": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "source of the setting", "endPoint": "none"}}, "window.titleBarStyle": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "This is used to know if window title bar style is set to custom or not", "settingvalue": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "value of the setting", "endPoint": "none"}, "source": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "source of the setting", "endPoint": "none"}}, "extensions.verifySignature": {"owner": "sandy081", "comment": "This is used to know if extensions signature verification is enabled or not", "settingvalue": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "value of the setting", "endPoint": "none"}, "source": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "source of the setting", "endPoint": "none"}}, "window.newWindowProfile": {"owner": "sandy081", "comment": "This is used to know the new window profile that is being used", "settingvalue": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "if the profile is default or not", "endPoint": "none"}, "source": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "source of the setting", "endPoint": "none"}}, "extensions.autoRestart": {"owner": "sandy081", "comment": "This is used to know if extensions are getting auto restarted or not", "settingvalue": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "value of the setting", "endPoint": "none"}, "source": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "source of the setting", "endPoint": "none"}}, "continueOn.editSessions.pick.outcome": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Reporting the outcome of invoking the Continue On action.", "outcome": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The outcome of invoking continue edit session.", "endPoint": "none"}, "hashedid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The hash of the stored edit session id, for correlating success of stores and resumes.", "endPoint": "none"}}, "continueOn.editSessions.store": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Reporting when storing an edit session as part of the Continue On flow."}, "continueOn.editSessions.store.outcome": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Reporting the outcome of invoking the Continue On action.", "outcome": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The outcome of invoking continue edit session.", "endPoint": "none"}, "hashedid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The hash of the stored edit session id, for correlating success of stores and resumes.", "endPoint": "none"}}, "editSessions.store": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Reporting when the store edit session action is invoked."}, "editSessions.resume": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Reporting when an edit session is resumed from an edit session identifier.", "outcome": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The outcome of resuming the edit session.", "endPoint": "none"}, "hashedid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The hash of the stored edit session id, for correlating success of stores and resumes.", "endPoint": "none"}}, "editSessions.resume.outcome": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Reporting when an edit session is resumed from an edit session identifier.", "outcome": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The outcome of resuming the edit session.", "endPoint": "none"}, "hashedid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The hash of the stored edit session id, for correlating success of stores and resumes.", "endPoint": "none"}}, "editSessions.upload.failed": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Reporting when Continue On server request fails.", "reason": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The reason that the server request failed.", "endPoint": "none"}}, "continueOn.editSessions.canStore.outcome": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Reporting whether we can and should store edit session as part of Continue On.", "outcome": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The outcome of checking whether we can store an edit session as part of the Continue On flow.", "endPoint": "none"}}, "continueOn.openDestination.outcome": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Reporting the outcome of evaluating a selected Continue On destination option.", "selection": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The selected Continue On destination option.", "endPoint": "none"}, "outcome": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The outcome of evaluating the selected Continue On destination option.", "endPoint": "none"}}, "fileWatcherError": {"owner": "bpasero", "comment": "An event that fires when a watcher errors", "reason": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The watcher error reason.", "endPoint": "none"}}, "notebook/editorOpened": {"owner": "rebornix", "comment": "Identify the notebook editor view type", "scheme": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "File system provider scheme for the resource", "endPoint": "none"}, "ext": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "File extension for the resource", "endPoint": "none"}, "viewtype": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "View type of the notebook editor", "endPoint": "none"}, "isrepl": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the notebook editor is within a REPL editor", "endPoint": "none"}}, "notebook/editorOpenPerf": {"owner": "rebornix", "comment": "The notebook file open metrics. Used to get a better understanding of the performance of notebook file opening", "scheme": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "File system provider scheme for the notebook resource", "endPoint": "none"}, "ext": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "File extension for the notebook resource", "endPoint": "none"}, "viewtype": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The view type of the notebook editor", "endPoint": "none"}, "extensionactivated": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Extension activation time for the resource opening", "endPoint": "none"}, "inputloaded": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Editor Input loading time for the resource opening", "endPoint": "none"}, "webviewcommloaded": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Webview initialization time for the resource opening", "endPoint": "none"}, "custommarkdownloaded": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Custom markdown loading time for the resource opening", "endPoint": "none"}, "editorloaded": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Overall editor loading time for the resource opening", "endPoint": "none"}, "codecellcount": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Total number of code cell", "endPoint": "none"}, "mdcellcount": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Total number of markdown cell", "endPoint": "none"}, "outputcount": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Total number of cell outputs", "endPoint": "none"}, "outputbytes": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Total number of bytes for all outputs", "endPoint": "none"}, "codelength": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Length of text in all code cells", "endPoint": "none"}, "markdownlength": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Length of text in all markdown cells", "endPoint": "none"}, "notebookstatsloaded": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Time for generating the notebook level information for telemetry", "endPoint": "none"}}, "mcp/serverBootState": {"owner": "connor4312", "comment": "Details the capabilities of the MCP server", "state": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The server outcome", "endPoint": "none"}, "time": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Duration in milliseconds to reach that state", "endPoint": "none"}}, "mcp/serverBoot": {"owner": "connor4312", "comment": "Details the capabilities of the MCP server", "supportslogging": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the server supports logging", "endPoint": "none"}, "supportsprompts": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the server supports prompts", "endPoint": "none"}, "supportsresources": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the server supports resource", "endPoint": "none"}, "toolcount": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The number of tools the server advertises", "endPoint": "none"}, "servername": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The name of the MCP server", "endPoint": "none"}, "serverversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The version of the MCP server", "endPoint": "none"}}, "startupHeapStatistics": {"owner": "bpasero", "comment": "An event that reports startup heap statistics for performance analysis.", "heapused": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Used heap", "endPoint": "none"}, "heapgarbage": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Garbage heap", "endPoint": "none"}, "majorgcs": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Major GCs count", "endPoint": "none"}, "minorgcs": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Minor GCs count", "endPoint": "none"}, "gcsduration": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "GCs duration", "endPoint": "none"}}, "startup.resource.perf": {"owner": "j<PERSON>ken", "comment": "Resource performance numbers", "hosthash": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Hash of the hostname", "endPoint": "none"}, "name": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Resource basename", "endPoint": "none"}, "duration": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Resource duration", "endPoint": "none"}}, "performance.inputLatency": {"owner": "tyriar", "comment": "This is a set of samples of the time (in milliseconds) that various events took when typing in the editor", "keydown.average": {"owner": "tyriar", "comment": "The minimum time it took to execute.", "classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "keydown.max": {"owner": "tyriar", "comment": "The minimum time it took to execute.", "classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "keydown.min": {"owner": "tyriar", "comment": "The minimum time it took to execute.", "classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "input.average": {"owner": "tyriar", "comment": "The minimum time it took to execute.", "classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "input.max": {"owner": "tyriar", "comment": "The minimum time it took to execute.", "classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "input.min": {"owner": "tyriar", "comment": "The minimum time it took to execute.", "classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "render.average": {"owner": "tyriar", "comment": "The minimum time it took to execute.", "classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "render.max": {"owner": "tyriar", "comment": "The minimum time it took to execute.", "classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "render.min": {"owner": "tyriar", "comment": "The minimum time it took to execute.", "classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "total.average": {"owner": "tyriar", "comment": "The minimum time it took to execute.", "classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "total.max": {"owner": "tyriar", "comment": "The minimum time it took to execute.", "classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "total.min": {"owner": "tyriar", "comment": "The minimum time it took to execute.", "classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "samplecount": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The number of samples measured.", "endPoint": "none"}}, "mcp.addserver": {"owner": "digitarald", "comment": "Generic details for adding a new MCP server", "packagetype": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The type of MCP server package", "endPoint": "none"}}, "mcp.addserver.completed": {"owner": "digitarald", "comment": "Generic details for successfully adding model-assisted MCP server", "packagetype": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The type of MCP server package", "endPoint": "none"}, "servertype": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The type of MCP server", "endPoint": "none"}, "target": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The target of the MCP server configuration", "endPoint": "none"}}, "shareService.share": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Reporting which share provider is invoked.", "providerid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The id of the selected share provider.", "endPoint": "none"}}, "test.outcomes": {"owner": "connor4312", "comment": "Test outcome metrics. This helps us understand magnitude of feature use and how to build fix suggestions.", "failures": {"comment": "Number of test failures", "classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "passes": {"comment": "Number of test failures", "classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "controller": {"comment": "The test controller being used", "classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "remoteConnectionHealth": {"owner": "alex<PERSON><PERSON>", "comment": "The remote connection health has changed (round trip time)", "remotename": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The name of the resolver.", "endPoint": "none"}, "reconnectiontoken": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The identifier of the connection.", "endPoint": "none"}, "connectionhealth": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The health of the connection: good or poor.", "endPoint": "none"}}, "remoteConnectionSuccess": {"owner": "alex<PERSON><PERSON>", "comment": "The initial connection succeeded", "web": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Is web ui.", "endPoint": "none"}, "connectiontimems": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Time, in ms, until connected", "endPoint": "none"}, "remotename": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The name of the resolver.", "endPoint": "none"}}, "remoteConnectionFailure": {"owner": "alex<PERSON><PERSON>", "comment": "The initial connection failed", "web": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Is web ui.", "endPoint": "none"}, "remotename": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The name of the resolver.", "endPoint": "none"}, "connectiontimems": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Time, in ms, until connection failure", "endPoint": "none"}, "message": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Error message", "endPoint": "none"}}, "remoteConnectionLatency": {"owner": "connor4312", "comment": "The latency to the remote extension host", "web": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Whether this is running on web", "endPoint": "none"}, "remotename": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Anonymized remote name", "endPoint": "none"}, "latencyms": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Latency to the remote, in milliseconds", "endPoint": "none"}}, "remoteReconnectionReload": {"owner": "alex<PERSON><PERSON>", "comment": "The reload button in the builtin permanent reconnection failure dialog was pressed", "remotename": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The name of the resolver.", "endPoint": "none"}, "reconnectiontoken": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The identifier of the connection.", "endPoint": "none"}, "millissincelastincomingdata": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Elapsed time (in ms) since data was last received.", "endPoint": "none"}, "attempt": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The reconnection attempt counter.", "endPoint": "none"}}, "remoteConnectionLost": {"owner": "alex<PERSON><PERSON>", "comment": "The remote connection state is now `ConnectionLost`", "remotename": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The name of the resolver.", "endPoint": "none"}, "reconnectiontoken": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The identifier of the connection.", "endPoint": "none"}}, "remoteReconnectionRunning": {"owner": "alex<PERSON><PERSON>", "comment": "The remote connection state is now `ReconnectionRunning`", "remotename": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The name of the resolver.", "endPoint": "none"}, "reconnectiontoken": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The identifier of the connection.", "endPoint": "none"}, "millissincelastincomingdata": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Elapsed time (in ms) since data was last received.", "endPoint": "none"}, "attempt": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The reconnection attempt counter.", "endPoint": "none"}}, "remoteReconnectionPermanentFailure": {"owner": "alex<PERSON><PERSON>", "comment": "The remote connection state is now `ReconnectionPermanentFailure`", "remotename": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The name of the resolver.", "endPoint": "none"}, "reconnectiontoken": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The identifier of the connection.", "endPoint": "none"}, "millissincelastincomingdata": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Elapsed time (in ms) since data was last received.", "endPoint": "none"}, "attempt": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The reconnection attempt counter.", "endPoint": "none"}, "handled": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The error was handled by the resolver.", "endPoint": "none"}}, "remoteConnectionGain": {"owner": "alex<PERSON><PERSON>", "comment": "The remote connection state is now `ConnectionGain`", "remotename": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The name of the resolver.", "endPoint": "none"}, "reconnectiontoken": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The identifier of the connection.", "endPoint": "none"}, "millissincelastincomingdata": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Elapsed time (in ms) since data was last received.", "endPoint": "none"}, "attempt": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The reconnection attempt counter.", "endPoint": "none"}}, "userDataProfile.createFromTemplate": {"owner": "sandy081", "comment": "Report when profile is about to be created", "source": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Type of profile source", "endPoint": "none"}}, "profiles:count": {"owner": "sandy081", "comment": "Report the number of user profiles excluding the default profile", "count": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The number of user profiles excluding the default profile", "endPoint": "none"}}, "workspaceProfileInfo": {"owner": "sandy081", "comment": "Report profile information of the current workspace", "workspaceid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "A UUID given to a workspace to identify it.", "endPoint": "none"}, "defaultprofile": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the profile of the workspace is default or not.", "endPoint": "none"}}, "automaticlanguagedetection.stats": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Used to determine how definitive language detection is and how long it takes.", "languages": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The languages the model supports.", "endPoint": "none"}, "confidences": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The confidences of those languages.", "endPoint": "none"}, "timespent": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "How long the operation took.", "endPoint": "none"}}, "automaticlanguagedetection.perf": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Helps understand how effective language detection and how long it takes to run", "timespent": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The time it took to run language detection", "endPoint": "none"}, "detection": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The language that was detected", "endPoint": "none"}}, "startup.timer.mark": {"owner": "j<PERSON>ken", "comment": "Information about a performance marker", "source": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Where this marker was generated, e.g main, renderer, extension host", "endPoint": "none"}, "name": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The name of this marker (as defined in source code)", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The absolute timestamp (unix time)", "endPoint": "none"}}, "editor.tokenizedLine": {"owner": "hediet", "timems": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To understand how long it took to tokenize a random line", "endPoint": "none"}, "languageid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To relate the performance to the language", "endPoint": "none"}, "linelength": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To relate the performance to the line length", "endPoint": "none"}, "fromworker": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To figure out if this line was tokenized sync or async", "endPoint": "none"}, "sourceextensionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To figure out which extension contributed the grammar", "endPoint": "none"}, "israndomsample": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To figure out if this is a random sample or measured because of some other condition.", "endPoint": "none"}, "tokenizationsetting": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "To understand if the user has async tokenization enabled. 0=sync, 1=async, 2=verification", "endPoint": "none"}, "comment": "This event gives insight about the performance certain grammars."}, "tasClientReadTreatmentComplete": {"owner": "sbatten", "comment": "Logged when a treatment value is read from the experiment service", "treatmentvalue": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The value of the read treatment", "endPoint": "none"}, "treatmentname": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The name of the treatment that was read", "endPoint": "none"}}, "extensionHostStartup": {"owner": "alex<PERSON><PERSON>", "comment": "The startup state of the extension host", "time": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The time reported by Date.now().", "endPoint": "none"}, "action": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The action: starting, success or error.", "endPoint": "none"}, "kind": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The extension host kind: LocalProcess, LocalWebWorker or Remote.", "endPoint": "none"}, "errorname": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The error name.", "endPoint": "none"}, "errormessage": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The error message.", "endPoint": "none"}, "errorstack": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The error stack.", "endPoint": "none"}}, "extensionhost.incoming": {"owner": "j<PERSON>ken", "comment": "Insights about RPC message sizes", "type": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The type of the RPC message", "endPoint": "none"}, "length": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The byte-length of the RPC message", "endPoint": "none"}}, "extensionhost.outgoing": {"owner": "j<PERSON>ken", "comment": "Insights about RPC message sizes", "type": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The type of the RPC message", "endPoint": "none"}, "length": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The byte-length of the RPC message", "endPoint": "none"}}, "extensionsMessage": {"owner": "alex<PERSON><PERSON>", "comment": "A validation message for an extension", "type": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Severity of problem.", "endPoint": "none"}, "extensionid": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The identifier of the extension that has a problem.", "endPoint": "none"}, "extensionpointid": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The extension point that has a problem.", "endPoint": "none"}, "message": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The message of the problem.", "endPoint": "none"}}, "extensionActivationError": {"owner": "alex<PERSON><PERSON>", "comment": "An extension failed to activate", "extensionid": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The identifier of the extension.", "endPoint": "none"}, "error": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "comment": "The error message.", "endPoint": "none"}}, "extensionHostCrash": {"owner": "alex<PERSON><PERSON>", "comment": "The extension host has terminated unexpectedly", "code": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The exit code of the extension host process.", "endPoint": "none"}, "signal": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The signal that caused the extension host process to exit.", "endPoint": "none"}, "extensionids": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The list of loaded extensions.", "endPoint": "none"}}, "extensionHostCrashExtension": {"owner": "alex<PERSON><PERSON>", "comment": "The extension host has terminated unexpectedly", "code": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The exit code of the extension host process.", "endPoint": "none"}, "signal": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The signal that caused the extension host process to exit.", "endPoint": "none"}, "extensionid": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The identifier of the extension.", "endPoint": "none"}}, "cachedSearchComplete": {"owner": "rob<PERSON><PERSON>", "comment": "Fired when a file search is completed from previously cached results", "reason": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Indicates which extension or UI feature triggered this search", "endPoint": "none"}, "resultcount": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The number of search results", "endPoint": "none"}, "workspacefoldercount": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The number of folders in the workspace", "endPoint": "none"}, "endtoendtime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The total search time", "endPoint": "none"}, "sortingtime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The amount of time spent sorting results", "endPoint": "none"}, "cachewasresolved": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Whether the cache was already resolved when the search began", "endPoint": "none"}, "cachelookuptime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The amount of time spent looking up the cache to use for the search", "endPoint": "none"}, "cachefiltertime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The amount of time spent searching within the cache", "endPoint": "none"}, "cacheentrycount": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The number of entries in the searched-in cache", "endPoint": "none"}, "scheme": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The uri scheme of the folder searched in", "endPoint": "none"}}, "searchComplete": {"owner": "rob<PERSON><PERSON>", "comment": "Fired when a file search is completed", "reason": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Indicates which extension or UI feature triggered this search", "endPoint": "none"}, "resultcount": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The number of search results", "endPoint": "none"}, "workspacefoldercount": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The number of folders in the workspace", "endPoint": "none"}, "endtoendtime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The total search time", "endPoint": "none"}, "sortingtime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The amount of time spent sorting results", "endPoint": "none"}, "filewalktime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The amount of time spent walking file system", "endPoint": "none"}, "directorieswalked": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The number of directories walked", "endPoint": "none"}, "fileswalked": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The number of files walked", "endPoint": "none"}, "cmdtime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The amount of time spent running the search command", "endPoint": "none"}, "cmdresultcount": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The number of results returned from the search command", "endPoint": "none"}, "scheme": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The uri scheme of the folder searched in", "endPoint": "none"}}, "textSearchComplete": {"owner": "rob<PERSON><PERSON>", "comment": "Fired when a text search is completed", "reason": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Indicates which extension or UI feature triggered this search", "endPoint": "none"}, "workspacefoldercount": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The number of folders in the workspace", "endPoint": "none"}, "endtoendtime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The total search time", "endPoint": "none"}, "scheme": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The uri scheme of the folder searched in", "endPoint": "none"}, "error": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The type of the error, if any", "endPoint": "none"}}, "accessibility": {"owner": "<PERSON><PERSON><PERSON>", "comment": "Helps gain an understanding of when accessibility features are being used", "enabled": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether or not accessibility features are enabled", "endPoint": "none"}}, "auxiliaryWindowOpen": {"owner": "bpasero", "comment": "An event that fires when an auxiliary window is opened", "bounds": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Has window bounds provided.", "endPoint": "none"}}, "activatePlugin": {"owner": "j<PERSON>ken", "comment": "Data about how/why an extension was activated", "id": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "comment": "The identifier of an extension", "endPoint": "none"}, "name": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "comment": "The name of the extension", "endPoint": "none"}, "isbuiltin": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "If the extension is builtin or git installed", "endPoint": "none"}, "publisherdisplayname": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The publisher of the extension", "endPoint": "none"}, "themeid": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "comment": "The id of the theme that triggered the first extension use.", "endPoint": "none"}, "extensionversion": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "comment": "The version of the extension", "endPoint": "none"}, "activationevents": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "All activation events of the extension", "endPoint": "none"}, "reason": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The activation event", "endPoint": "none"}, "reasonid": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "comment": "The identifier of the activation event", "endPoint": "none"}}, "viewDescriptorService.moveViews": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Logged when views are moved from one view container to another", "viewcount": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The number of views moved", "endPoint": "none"}, "fromcontainer": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The starting view container of the moved views", "endPoint": "none"}, "tocontainer": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The destination view container of the moved views", "endPoint": "none"}, "fromlocation": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The location of the starting view container. e.g. Primary Side Bar", "endPoint": "none"}, "tolocation": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The location of the destination view container. e.g. Panel", "endPoint": "none"}}, "workspaceextension:uninstall": {"owner": "sandy081", "comment": "Uninstall workspace extension"}, "extensions:trustPublisher": {"owner": "sandy081", "comment": "Report the action taken by the user on the publisher trust dialog", "action": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The action taken by the user on the publisher trust dialog. Can be trust, learn more or cancel.", "endPoint": "none"}, "extensionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The identifiers of the extension for which the publisher trust dialog was shown.", "endPoint": "none"}}, "workspaceextension:install": {"owner": "sandy081", "comment": "Install workspace extension"}, "treeSitter.fullParse": {"owner": "alexr00", "comment": "Used to understand how long it takes to parse a tree-sitter tree", "languageid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The programming language ID.", "endPoint": "none"}, "time": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "isMeasurement": true, "comment": "The ms it took to parse", "endPoint": "none"}, "passes": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "isMeasurement": true, "comment": "The number of passes it took to parse", "endPoint": "none"}}, "treeSitter.incrementalParse": {"owner": "alexr00", "comment": "Used to understand how long it takes to parse a tree-sitter tree", "languageid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The programming language ID.", "endPoint": "none"}, "time": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "isMeasurement": true, "comment": "The ms it took to parse", "endPoint": "none"}, "passes": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "isMeasurement": true, "comment": "The number of passes it took to parse", "endPoint": "none"}}, "suggest.durations.json": {"owner": "j<PERSON>ken", "comment": "Completions performance numbers", "data": {"comment": "Durations per source and overall", "classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}}, "suggest.acceptedSuggestion": {"owner": "j<PERSON>ken", "comment": "Information accepting completion items", "extensionid": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "comment": "Extension contributing the completions item", "endPoint": "none"}, "providerid": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "comment": "Provider of the completions item", "endPoint": "none"}, "basenamehash": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "comment": "Hash of the basename of the file into which the completion was inserted", "endPoint": "none"}, "fileextension": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "File extension of the file into which the completion was inserted", "endPoint": "none"}, "languageid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Language type of the file into which the completion was inserted", "endPoint": "none"}, "kind": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The completion item kind", "endPoint": "none"}, "resolveinfo": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "If the item was inserted before resolving was done", "endPoint": "none"}, "resolveduration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "How long resolving took to finish", "endPoint": "none"}, "commandduration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "How long a completion item command took", "endPoint": "none"}, "additionaleditsasync": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Info about asynchronously applying additional edits", "endPoint": "none"}, "index": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The index of the completion item in the sorted list.", "endPoint": "none"}, "firstindex": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "When there are multiple completions, the index of the first instance.", "endPoint": "none"}}, "codeAction.applyCodeAction": {"codeactiontitle": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The display label of the applied code action", "endPoint": "none"}, "codeactionkind": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The kind (refactor, quickfix) of the applied code action", "endPoint": "none"}, "codeactionispreferred": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Was the code action marked as being a preferred action?", "endPoint": "none"}, "reason": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The kind of action used to trigger apply code action.", "endPoint": "none"}, "owner": "justschen", "comment": "Event used to gain insights into which code actions are being triggered"}, "editorAsyncPaste": {"duration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The duration of the paste operation.", "endPoint": "none"}, "owner": "aiday-mar", "comment": "Provides insight into the delay introduced by pasting async via keybindings."}, "unresponsive.sample": {"owner": "j<PERSON>ken", "comment": "A callstack that took a long time to execute", "selftime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Self time of the sample", "endPoint": "none"}, "totaltime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Total time of the sample", "endPoint": "none"}, "percentage": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Relative time (percentage) of the sample", "endPoint": "none"}, "perfbaseline": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Performance baseline for the machine", "endPoint": "none"}, "functionname": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The name of the sample", "endPoint": "none"}, "callers": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "comment": "The heaviest call trace into this sample", "endPoint": "none"}, "callersannotated": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The heaviest call trace into this sample annotated with respective costs", "endPoint": "none"}, "source": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The source - either renderer or an extension", "endPoint": "none"}}, "sync/error": {"owner": "sandy081", "comment": "Information about the error that occurred while syncing", "code": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "error code", "endPoint": "none"}, "service": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Settings Sync service for which this error has occurred", "endPoint": "none"}, "servercode": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Settings Sync service error code", "endPoint": "none"}, "url": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Settings Sync resource URL for which this error has occurred", "endPoint": "none"}, "resource": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Settings Sync resource for which this error has occurred", "endPoint": "none"}, "executionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Settings Sync execution id for which this error has occurred", "endPoint": "none"}}, "settingsSync:sync": {"owner": "sandy081", "comment": "Report when running a sync operation", "duration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Time taken to run sync operation", "endPoint": "none"}}, "workspace.stats": {"owner": "lramos15", "comment": "Metadata related to the workspace", "workspace.id": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "A UUID given to a workspace to identify it.", "endPoint": "none"}, "renderersessionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The ID of the session", "endPoint": "none"}}, "workspace.stats.file": {"owner": "lramos15", "comment": "Helps us gain insights into what type of files are being used in a workspace", "renderersessionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The ID of the session.", "endPoint": "none"}, "type": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The type of file", "endPoint": "none"}, "count": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "How many types of that file are present", "endPoint": "none"}}, "workspace.stats.launchConfigFile": {"owner": "lramos15", "comment": "Helps us gain insights into what type of files are being used in a workspace", "renderersessionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The ID of the session.", "endPoint": "none"}, "type": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The type of file", "endPoint": "none"}, "count": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "How many types of that file are present", "endPoint": "none"}}, "workspace.stats.configFiles": {"owner": "lramos15", "comment": "Helps us gain insights into what type of files are being used in a workspace", "renderersessionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The ID of the session.", "endPoint": "none"}, "type": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The type of file", "endPoint": "none"}, "count": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "How many types of that file are present", "endPoint": "none"}}, "workspace.stats.metadata": {"owner": "j<PERSON>ken", "comment": "Metadata about workspace metadata collection", "duration": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "How did it take to make workspace stats", "endPoint": "none"}, "reachedlimit": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Did making workspace stats reach its limits", "endPoint": "none"}, "filecount": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "How many files did workspace stats discover", "endPoint": "none"}, "readdircount": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "How many readdir call were needed", "endPoint": "none"}}, "utilityprocessv8error": {"processtype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The type of utility process to understand the origin of the crash better.", "endPoint": "none"}, "error": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The type of error from the utility process to understand the nature of the crash better.", "endPoint": "none"}, "location": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The source location that triggered the crash to understand the nature of the crash better.", "endPoint": "none"}, "addons": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The list of addons loaded in the utility process to understand the nature of the crash better", "endPoint": "none"}, "owner": "deepak1556", "comment": "Provides insight into V8 sandbox FATAL error caused by native addons."}, "utilityprocesscrash": {"type": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The type of utility process to understand the origin of the crash better.", "endPoint": "none"}, "reason": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The reason of the utility process crash to understand the nature of the crash better.", "endPoint": "none"}, "code": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The exit code of the utility process to understand the nature of the crash better", "endPoint": "none"}, "owner": "bpasero", "comment": "Provides insight into reasons the utility process crashed."}, "windowerror": {"type": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The type of window error to understand the nature of the error better.", "endPoint": "none"}, "reason": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The reason of the window error to understand the nature of the error better.", "endPoint": "none"}, "code": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The exit code of the window process to understand the nature of the error better", "endPoint": "none"}, "owner": "bpasero", "comment": "Provides insight into reasons the vscode window had an error."}, "extensionsignature:verification": {"owner": "sandy081", "comment": "Extension signature verification event", "extensionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "extension identifier", "endPoint": "none"}, "extensionversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "extension version", "endPoint": "none"}, "code": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "result code of the verification", "endPoint": "none"}, "internalcode": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "isMeasurement": true, "comment": "internal code of the verification", "endPoint": "none"}, "duration": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "isMeasurement": true, "comment": "amount of time taken to verify the signature", "endPoint": "none"}, "didexecute": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "whether the verification was executed", "endPoint": "none"}, "clienttargetplatform": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "target platform of the client", "endPoint": "none"}}, "extension:extract": {"owner": "sandy081", "comment": "Update metadata error", "extensionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "extension identifier", "endPoint": "none"}, "code": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "error code", "endPoint": "none"}, "isprofile": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Is writing into profile", "endPoint": "none"}}, "extensiongallery:downloadvsix:retry": {"owner": "sandy081", "comment": "Event reporting the retry of downloading", "extensionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Extension Id", "endPoint": "none"}, "attempts": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "isMeasurement": true, "comment": "Number of Attempts", "endPoint": "none"}}, "extensiongallery:downloadsigzip:retry": {"owner": "sandy081", "comment": "Event reporting the retry of downloading", "extensionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Extension Id", "endPoint": "none"}, "attempts": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "isMeasurement": true, "comment": "Number of Attempts", "endPoint": "none"}}, "exeExtensionRecommendations:alreadyInstalled": {"owner": "sandy081", "comment": "Information about executable based extension recommendation", "extensionid": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "comment": "id of the recommended extension", "endPoint": "none"}, "exename": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "comment": "name of the executable for which extension is being recommended", "endPoint": "none"}}, "exeExtensionRecommendations:notInstalled": {"owner": "sandy081", "comment": "Information about executable based extension recommendation", "extensionid": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "comment": "id of the recommended extension", "endPoint": "none"}, "exename": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "comment": "name of the executable for which extension is being recommended", "endPoint": "none"}}, "galleryService:additionalQueryByName": {"owner": "sandy081", "comment": "Report the query to the Marketplace for fetching extensions by name", "count": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Number of extensions to fetch", "endPoint": "none"}}, "galleryService:fallbacktounpkg": {"owner": "sandy081", "comment": "Report the fallback to the unpkg service for getting latest extension", "extension": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Extension id", "endPoint": "none"}, "prerelease": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Get pre-release version", "endPoint": "none"}, "compatible": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Get compatible version", "endPoint": "none"}}, "galleryService:fallbacktoquery": {"owner": "sandy081", "comment": "Report the fallback to the Marketplace query for fetching extensions", "extension": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Extension id", "endPoint": "none"}, "prerelease": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Get pre-release version", "endPoint": "none"}, "compatible": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Get compatible version", "endPoint": "none"}, "fromfallback": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "From fallback", "endPoint": "none"}}, "galleryService:engineFallback": {"owner": "sandy081", "comment": "Fallback request when engine is not found in properties of an extension version", "extension": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "extension name", "endPoint": "none"}, "extensionversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "version", "endPoint": "none"}}, "galleryService:additionalQuery": {"owner": "sandy081", "comment": "Response information about the additional query to the Marketplace for fetching all versions to get release version", "duration": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "isMeasurement": true, "comment": "Amount of time taken by the additional query", "endPoint": "none"}, "count": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Total number of extensions returned by this additional query", "endPoint": "none"}}, "galleryService:query": {"owner": "sandy081", "comment": "Information about Marketplace query and its response", "filtertypes": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Filter types used in the query.", "endPoint": "none"}, "flags": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Flags passed in the query.", "endPoint": "none"}, "sortby": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "sorted by option passed in the query", "endPoint": "none"}, "sortorder": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "sort order option passed in the query", "endPoint": "none"}, "pagenumber": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "requested page number in the query", "endPoint": "none"}, "duration": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "isMeasurement": true, "comment": "amount of time taken by the query request", "endPoint": "none"}, "success": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "whether the query request is success or not", "endPoint": "none"}, "requestbodysize": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "size of the request body", "endPoint": "none"}, "responsebodysize": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "size of the response body", "endPoint": "none"}, "statuscode": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "status code of the response", "endPoint": "none"}, "errorcode": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "error code of the response", "endPoint": "none"}, "count": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "total number of extensions matching the query", "endPoint": "none"}, "source": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "source that requested this query, eg., recommendations, viewlet", "endPoint": "none"}, "searchtextlength": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "length of the search text in the query", "endPoint": "none"}, "server": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "server that handled the query", "endPoint": "none"}, "endtoendid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "end to end operation id", "endPoint": "none"}, "activityid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "activity id", "endPoint": "none"}}, "galleryService:getLatest": {"owner": "sandy081", "comment": "Report the query to the Marketplace for fetching latest version of an extension", "host": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The host of the end point", "endPoint": "none"}, "extension": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The identifier of the extension", "endPoint": "none"}, "duration": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "isMeasurement": true, "comment": "Duration in ms for the query", "endPoint": "none"}, "errorcode": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The error code in case of error", "endPoint": "none"}, "server": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The server of the end point", "endPoint": "none"}, "activityid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The activity ID of the request", "endPoint": "none"}, "endtoendid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The end-to-end ID of the request", "endPoint": "none"}}, "galleryService:cdnFallback": {"owner": "sandy081", "comment": "Fallback request information when the primary asset request to CDN fails", "extension": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "extension name", "endPoint": "none"}, "assettype": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "asset that failed", "endPoint": "none"}, "message": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "error message", "endPoint": "none"}, "extensionversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "version", "endPoint": "none"}, "server": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "server that handled the query", "endPoint": "none"}, "endtoendid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "end to end operation id", "endPoint": "none"}, "activityid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "activity id", "endPoint": "none"}}, "signal.played": {"owner": "hediet", "signal": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The signal that was played.", "endPoint": "none"}, "source": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The source that triggered the signal (e.g. \\\"diffEditorNavigation\\\").", "endPoint": "none"}, "isscreenreaderoptimized": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the user is using a screen reader", "endPoint": "none"}, "comment": "This data is collected to understand how signals are used and if more signals should be added."}, "remoteTunnel.enablement": {"owner": "a<PERSON><PERSON><PERSON>", "comment": "Reporting when Remote Tunnel access is turned on or off", "enabled": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Flag indicating if Remote Tunnel Access is enabled or not", "endPoint": "none"}, "service": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Flag indicating if Remote Tunnel Access is installed as a service", "endPoint": "none"}}, "update:error": {"owner": "joa<PERSON>reno", "messagehash": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The hash of the error message.", "endPoint": "none"}, "comment": "This is used to know how often VS Code updates have failed."}, "update:downloaded": {"owner": "joa<PERSON>reno", "newversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The version number of the new VS Code that has been downloaded.", "endPoint": "none"}, "comment": "This is used to know how often VS Code has successfully downloaded the update."}, "clientPlatformInfo": {"platformid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "A string identifying the operating system without any version information.", "endPoint": "none"}, "platformversionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "A string identifying the operating system version excluding any name information or release code.", "endPoint": "none"}, "platformidlike": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "A string identifying the operating system the current OS derivate is closely related to.", "endPoint": "none"}, "desktopenvironment": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "A string identifying the desktop environment the user is using.", "endPoint": "none"}, "displayprotocol": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "A string identifying the users display protocol type.", "endPoint": "none"}, "codedisplayprotocol": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "A string identifying the vscode display protocol type.", "endPoint": "none"}, "owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Provides insight into the distro and desktop environment information on Linux."}, "api/scm/createSourceControl": {"owner": "joa<PERSON>reno", "extensionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The ID of the extension contributing to the Source Control API.", "endPoint": "none"}, "comment": "This is used to know what extensions contribute to the Source Control API."}, "shimming.open": {"owner": "j<PERSON>ken", "comment": "Know when the open-shim was used", "extension": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The extension is question", "endPoint": "none"}}, "shimming.open.call.noForward": {"owner": "j<PERSON>ken", "comment": "Know when the open-shim was used", "extension": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The extension is question", "endPoint": "none"}}, "extensionActivationTimes": {"owner": "j<PERSON>ken", "comment": "Timestamps for extension activation", "outcome": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Did extension activation succeed or fail", "endPoint": "none"}, "id": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "comment": "The identifier of an extension", "endPoint": "none"}, "name": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "comment": "The name of the extension", "endPoint": "none"}, "extensionversion": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "comment": "The version of the extension", "endPoint": "none"}, "publisherdisplayname": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The publisher of the extension", "endPoint": "none"}, "activationevents": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "All activation events of the extension", "endPoint": "none"}, "isbuiltin": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "If the extension is builtin or git installed", "endPoint": "none"}, "reason": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The activation event", "endPoint": "none"}, "reasonid": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "comment": "The identifier of the activation event", "endPoint": "none"}, "startup": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Activation occurred during startup", "endPoint": "none"}, "codeloadingtime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Time it took to load the extension's code", "endPoint": "none"}, "activatecalltime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Time it took to call activate", "endPoint": "none"}, "activateresolvedtime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Time it took for async-activation to finish", "endPoint": "none"}}, "debugProtocolMessageError": {"from": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The type of the debug adapter that the event is from.", "endPoint": "none"}, "type": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The type of the event that was malformed.", "endPoint": "none"}, "owner": "rob<PERSON><PERSON>", "comment": "Sent to collect details about misbehaving debug extensions."}, "Extension:ActionExecuted": {"extensionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The id of the extension handling the command, informing which extensions provide most-used functionality.", "endPoint": "none"}, "id": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The id of the command, to understand which specific extension features are most popular.", "endPoint": "none"}, "duration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The duration of the command execution, to detect performance issues", "endPoint": "none"}, "owner": "digitarald", "comment": "Used to gain insight on the most popular commands used from extensions"}, "extHostDeprecatedApiUsage": {"extensionid": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The id of the extension that is using the deprecated API", "endPoint": "none"}, "apiid": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The id of the deprecated API", "endPoint": "none"}, "owner": "mjbvz", "comment": "Helps us gain insights on extensions using deprecated API so we can assist in migration to new API"}, "webviews:createWebviewView": {"extensionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Id of the extension", "endPoint": "none"}, "id": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Id of the view", "endPoint": "none"}, "owner": "digitarald", "comment": "Helps to gain insights on what extension contributed views are most popular"}, "authentication.providerNotDeclared": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "An authentication provider was not declared in the Extension Manifest.", "id": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The provider id.", "endPoint": "none"}}, "authentication.clientIdUsage": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Used to see which extensions are using the VSCode client id override", "extensionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The extension id.", "endPoint": "none"}}, "authentication.providerUsage": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Used to see which extensions are using which providers", "extensionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The extension id.", "endPoint": "none"}, "providerid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The provider id.", "endPoint": "none"}}, "fetchFeatureUse": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Data about fetch API use", "url": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the url property was used.", "endPoint": "none"}, "typeproperty": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the type property was used.", "endPoint": "none"}, "data": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether a data URL was used.", "endPoint": "none"}, "blob": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether a blob URL was used.", "endPoint": "none"}, "integrity": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the integrity property was used.", "endPoint": "none"}, "manualredirect": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether a manual redirect was used.", "endPoint": "none"}}, "proxyAuthenticationRequest": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Data about proxy authentication requests", "authenticationtype": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "comment": "Type of the authentication requested", "endPoint": "none"}, "extensionhosttype": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Type of the extension host", "endPoint": "none"}}, "serverStart": {"owner": "alex<PERSON><PERSON>", "comment": "The server has started up", "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The time the server started at.", "endPoint": "none"}, "startedtime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The time the server began listening for connections.", "endPoint": "none"}, "codeloadedtime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The time which the code loaded on the server", "endPoint": "none"}, "readytime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The time when the server was completely ready", "endPoint": "none"}}, "serverPlatformInfo": {"platformid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "A string identifying the operating system without any version information.", "endPoint": "none"}, "platformversionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "A string identifying the operating system version excluding any name information or release code.", "endPoint": "none"}, "platformidlike": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "A string identifying the operating system the current OS derivate is closely related to.", "endPoint": "none"}, "owner": "deepak1556", "comment": "Provides insight into the distro information on Linux."}, "startupLayout": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Information about the layout of the workbench during statup", "activitybarvisible": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether or the not the activity bar is visible", "endPoint": "none"}, "sidebarvisible": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether or the not the primary side bar is visible", "endPoint": "none"}, "auxiliarybarvisible": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether or the not the secondary side bar is visible", "endPoint": "none"}, "panelvisible": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether or the not the panel is visible", "endPoint": "none"}, "statusbarvisible": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether or the not the status bar is visible", "endPoint": "none"}, "sidebarposition": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the primary side bar is on the left or right", "endPoint": "none"}, "panelposition": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the panel is on the top, bottom, left, or right", "endPoint": "none"}}, "editorActionInvoked": {"owner": "alex<PERSON><PERSON>", "comment": "An editor action has been invoked.", "name": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The label of the action that was invoked.", "endPoint": "none"}, "id": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The identifier of the action that was invoked.", "endPoint": "none"}}, "UnhandledError": {"owner": "l<PERSON>os<PERSON>, sbatten", "comment": "Whenever an error in VS Code is thrown.", "callstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "comment": "The callstack of the error.", "endPoint": "none"}, "msg": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "comment": "The message of the error. Normally the first line int the callstack.", "endPoint": "none"}, "file": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "comment": "The file the error originated from.", "endPoint": "none"}, "line": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "comment": "The line the error originate on.", "endPoint": "none"}, "column": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "comment": "The column of the line which the error orginated on.", "endPoint": "none"}, "uncaught_error_name": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "comment": "If the error is uncaught what is the error type", "endPoint": "none"}, "uncaught_error_msg": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "comment": "If the error is uncaught this is just msg but for uncaught errors.", "endPoint": "none"}, "count": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "comment": "How many times this error has been thrown", "endPoint": "none"}}, "notebook/SaveError": {"owner": "amunger", "comment": "Detect if we are having issues saving a notebook on the Extension Host", "isremote": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Whether the save is happening on a remote file system", "endPoint": "none"}, "isipynbworkerserializer": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Whether the IPynb files are serialized in workers", "endPoint": "none"}, "error": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Info about the error that occurred", "endPoint": "none"}}}, "commonProperties": {"abexp.assignmentcontext": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "common.firstsessiondate": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "common.lastsessiondate": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "common.isnewsession": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "common.remoteauthority": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "common.machineid": {"classification": "EndUserPseudonymizedInformation", "purpose": "FeatureInsight", "endPoint": "MacAddressHash"}, "sessionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "commithash": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "version": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "common.platform": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "common.product": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "common.useragent": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "common.istouchdevice": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "common.msftinternal": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "timestamp": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "common.timesincesessionstart": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "common.sequence": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "common.version.shell": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "common.version.renderer": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "common.cli": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "pluginhosttelemetry": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "common.sqmid": {"classification": "EndUserPseudonymizedInformation", "purpose": "BusinessInsight", "endPoint": "SqmMachineId"}, "common.devdeviceid": {"classification": "EndUserPseudonymizedInformation", "purpose": "BusinessInsight", "endPoint": "SqmMachineId"}, "common.platformversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "common.nodeplatform": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "common.nodearch": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "common.snap": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "common.platformdetail": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}}