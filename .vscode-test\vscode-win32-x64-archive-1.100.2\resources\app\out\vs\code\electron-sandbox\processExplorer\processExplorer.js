/*!--------------------------------------------------------
 * Copyright (C) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/(function(){const a=window.vscode,d=a.process;async function p(t,e){const o=await f();e?.beforeImport?.(o);const{enableDeveloperKeybindings:s,removeDeveloperKeybindingsAfterLoad:l,developerDeveloperKeybindingsDisposable:c,forceDisableShowDevtoolsOnError:i}=u(o,e);w(o);const n=new URL(`${v(o.appRoot,{isWindows:d.platform==="win32",scheme:"vscode-file",fallbackAuthority:"vscode-app"})}/out/`);globalThis._VSCODE_FILE_ROOT=n.toString(),b(o,n);try{const r=await import(new URL(`${t}.js`,n).href);return c&&l&&c(),{result:r,configuration:o}}catch(r){throw g(r,s&&!i),r}}async function f(){const t=setTimeout(()=>{console.error("[resolve window config] Could not resolve window configuration within 10 seconds, but will continue to wait...")},1e4);performance.mark("code/willWaitForWindowConfig");const e=await a.context.resolveConfiguration();return performance.mark("code/didWaitForWindowConfig"),clearTimeout(t),e}function u(t,e){const{forceEnableDeveloperKeybindings:o,disallowReloadKeybinding:s,removeDeveloperKeybindingsAfterLoad:l,forceDisableShowDevtoolsOnError:c}=typeof e?.configureDeveloperSettings=="function"?e.configureDeveloperSettings(t):{forceEnableDeveloperKeybindings:!1,disallowReloadKeybinding:!1,removeDeveloperKeybindingsAfterLoad:!1,forceDisableShowDevtoolsOnError:!1},n=!!(!!d.env.VSCODE_DEV||o);let r;return n&&(r=m(s)),{enableDeveloperKeybindings:n,removeDeveloperKeybindingsAfterLoad:l,developerDeveloperKeybindingsDisposable:r,forceDisableShowDevtoolsOnError:c}}function m(t){const e=a.ipcRenderer,o=function(n){return[n.ctrlKey?"ctrl-":"",n.metaKey?"meta-":"",n.altKey?"alt-":"",n.shiftKey?"shift-":"",n.keyCode].join("")},s=d.platform==="darwin"?"meta-alt-73":"ctrl-shift-73",l="123",c=d.platform==="darwin"?"meta-82":"ctrl-82";let i=function(n){const r=o(n);r===s||r===l?e.send("vscode:toggleDevTools"):r===c&&!t&&e.send("vscode:reloadWindow")};return window.addEventListener("keydown",i),function(){i&&(window.removeEventListener("keydown",i),i=void 0)}}function w(t){globalThis._VSCODE_NLS_MESSAGES=t.nls.messages,globalThis._VSCODE_NLS_LANGUAGE=t.nls.language;let e=t.nls.language||"en";e==="zh-tw"?e="zh-Hant":e==="zh-cn"&&(e="zh-Hans"),window.document.documentElement.setAttribute("lang",e)}function g(t,e){e&&a.ipcRenderer.send("vscode:openDevTools"),console.error(`[uncaught exception]: ${t}`),t&&typeof t!="string"&&t.stack&&console.error(t.stack)}function v(t,e){let o=t.replace(/\\/g,"/");o.length>0&&o.charAt(0)!=="/"&&(o=`/${o}`);let s;return e.isWindows&&o.startsWith("//")?s=encodeURI(`${e.scheme||"file"}:${o}`):s=encodeURI(`${e.scheme||"file"}://${e.fallbackAuthority||""}${o}`),s.replace(/#/g,"%23")}function b(t,e){if(Array.isArray(t.cssModules)&&t.cssModules.length>0){performance.mark("code/willAddCssLoader");const o=document.createElement("style");o.type="text/css",o.media="screen",o.id="vscode-css-loading",document.head.appendChild(o),globalThis._VSCODE_CSS_LOAD=function(n){o.textContent+=`@import url(${n});
`};const s={imports:{}};for(const n of t.cssModules){const r=new URL(n,e).href,y=`globalThis._VSCODE_CSS_LOAD('${r}');
`,D=new Blob([y],{type:"application/javascript"});s.imports[r]=URL.createObjectURL(D)}const l=window.trustedTypes?.createPolicy("vscode-bootstrapImportMap",{createScript(n){return n}}),c=JSON.stringify(s,void 0,2),i=document.createElement("script");i.type="importmap",i.setAttribute("nonce","0c6a828f1297"),i.textContent=l?.createScript(c)??c,document.head.appendChild(i),performance.mark("code/didAddCssLoader")}}globalThis.MonacoBootstrapWindow={load:p}})(),async function(){const a=window.MonacoBootstrapWindow,{result:d,configuration:p}=await a.load("vs/code/electron-sandbox/processExplorer/processExplorerMain",{configureDeveloperSettings:function(){return{forceEnableDeveloperKeybindings:!0}}});d.startup(p)}();

//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/848b80aeb52026648a8ff9f7c45a9b0a80641e2e/core/vs/code/electron-sandbox/processExplorer/processExplorer.js.map
