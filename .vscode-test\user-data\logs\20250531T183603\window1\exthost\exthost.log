2025-05-31 18:36:06.752 [info] Extension host with pid 53604 started
2025-05-31 18:36:06.807 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-05-31 18:36:07.011 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-05-31 18:36:07.042 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-05-31 18:36:07.156 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-05-31 18:36:07.621 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.openRepositoryInParentFolders', provide the URI of a resource or 'null' for any resource.
2025-05-31 18:36:07.622 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.showProgress', provide the URI of a resource or 'null' for any resource.
2025-05-31 18:36:07.658 [info] Eager extensions activated
2025-05-31 18:36:07.785 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-05-31 18:36:07.794 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-05-31 18:36:07.869 [info] ExtensionService#_doActivateExtension undefined_publisher.terminal-manager, startup: false, activationEvent: 'onStartupFinished'
2025-05-31 18:36:08.142 [info] Extension host terminating: renderer closed the MessagePort
2025-05-31 18:36:08.183 [info] Extension host with pid 53604 exiting with code 0
