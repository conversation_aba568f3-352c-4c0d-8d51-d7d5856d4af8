# Terminal Manager

A powerful VSCode extension for advanced terminal management and command execution with automatic compilation and hot-reload capabilities.

## Features

### Core Command Execution
- **Unified Command Interface**: Single command entry point via Command Palette
- **Smart Terminal Creation**: Automatically creates terminals for command execution
- **Real-time Output Capture**: Live monitoring and recording of command output
- **Shell Integration**: Leverages VSCode's shell integration for reliable command execution

### Output Management
- **Multi-layer Output Recording**: Console logging, VSCode output channel, and document display
- **Formatted Output Display**: Structured presentation with timestamps and status
- **Interactive Output Actions**: Copy to clipboard or save to file
- **Progress Monitoring**: Real-time progress indicators with cancellation support

### Development Features
- **Auto-Compilation**: Automatically compiles TypeScript files on changes
- **Hot-Reload**: Automatically reloads the extension when source files change (development mode)
- **File Watching**: Monitors source files for changes with intelligent debouncing
- **Comprehensive Logging**: Detailed logging for debugging and monitoring

### User Experience
- **Command Palette Integration**: Access via `Ctrl+Shift+P` → `Terminal Manager: Execute Command`
- **Input Validation**: Smart validation for commands and directories
- **Error Handling**: Graceful error handling with informative messages
- **Customizable Settings**: Configurable options for all features

## Installation

### From Source
1. Clone this repository
2. Run `npm install` to install dependencies
3. Run `npm run compile` to build the extension
4. Press `F5` to launch a new VSCode window with the extension loaded

### Package Installation
1. Run `npm run package` to create a VSIX file
2. Install the VSIX file in VSCode using "Install from VSIX..."

## Usage

### Command Execution

| Command | Access Method | Description |
|---------|---------------|-------------|
| `Terminal Manager: Execute Command` | `Ctrl+Shift+P` | Execute any command with full output capture |

### How to Use

1. **Open Command Palette**: Press `Ctrl+Shift+P` (Windows/Linux) or `Cmd+Shift+P` (Mac)
2. **Select Command**: Type `Terminal Manager: Execute Command` or just `execute command`
3. **Enter Command**: Input the command you want to execute (e.g., `npm install`, `ls`, `python script.py`)
4. **Choose Directory**: Specify the working directory (defaults to workspace root)
5. **Monitor Execution**: Watch real-time progress and output
6. **View Results**: Complete output is displayed in a new document with options to copy or save

### Configuration

```json
{
  "terminalManager.autoCompile": true,
  "terminalManager.autoReload": true,
  "terminalManager.shellIntegrationTimeout": 4000,
  "terminalManager.maxTerminals": 10
}
```

#### Settings

- **`autoCompile`**: Enable automatic TypeScript compilation on file changes
- **`autoReload`**: Enable automatic extension reload in development mode
- **`shellIntegrationTimeout`**: Timeout for shell integration in milliseconds
- **`maxTerminals`**: Maximum number of terminals to keep active

## Development

### Project Structure

```
src/
├── terminal/           # Core terminal management
│   ├── TerminalManager.ts
│   ├── TerminalProcess.ts
│   ├── TerminalRegistry.ts
│   ├── ansiUtils.ts
│   └── get-latest-output.ts
├── services/           # Additional services
│   ├── logging/
│   │   └── Logger.ts
│   ├── AutoCompiler.ts
│   └── FileWatcher.ts
├── utils/              # Utility functions
│   └── path.ts
├── extension.ts        # Main extension entry point
├── commands.ts         # Command registration
└── test/              # Test files
```

### Building

```bash
# Install dependencies
npm install

# Compile TypeScript
npm run compile

# Watch for changes
npm run watch

# Run tests
npm test

# Lint code
npm run lint

# Package extension
npm run package
```

### Testing

The extension includes comprehensive tests covering:
- Core terminal management functionality
- Command registration and execution
- Configuration handling
- Error scenarios

Run tests with:
```bash
npm test
```

### Development Mode

When running in development mode (F5), the extension automatically:
- Enables debug logging
- Watches for file changes
- Reloads the extension when source files change
- Compiles TypeScript automatically

## Architecture

### Terminal Management
The extension uses a sophisticated terminal management system:

1. **TerminalRegistry**: Centralized registry for all terminal instances
2. **TerminalManager**: High-level API for terminal operations
3. **TerminalProcess**: Event-driven process execution with Promise integration

### Auto-Compilation
The AutoCompiler service:
- Watches TypeScript files for changes
- Debounces compilation requests
- Provides visual feedback
- Integrates with VSCode's problem reporting

### File Watching
The FileWatcher service:
- Monitors source file changes
- Triggers extension reloads in development mode
- Provides user control over reload behavior
- Handles configuration changes

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

MIT License - see LICENSE file for details

## Changelog

### 0.1.0
- Initial release
- Core terminal management functionality
- Auto-compilation support
- Hot-reload in development mode
- Comprehensive command set
- Full test coverage
