# Terminal Manager 使用指南

## 🎉 问题已解决！

您遇到的模块解析错误已经成功修复。扩展现在可以正常运行了！

## ✅ 修复内容

1. **模块路径问题**：将TypeScript路径映射改为相对路径导入
2. **编译配置**：优化了TypeScript编译配置
3. **代码风格**：自动修复了所有ESLint警告

## 🚀 如何使用扩展

### 1. 启动开发环境

```bash
# 安装依赖（如果还没有安装）
npm install

# 启动开发模式
npm run dev
```

### 2. 在VSCode中测试扩展

1. 按 `F5` 键启动扩展开发窗口
2. 在新窗口中，扩展会自动激活
3. 查看输出面板中的"Terminal Manager"频道，确认激活成功

### 3. 使用核心功能

#### 创建终端
- 打开命令面板 (`Ctrl+Shift+P`)
- 运行：`Terminal Manager: Create New Terminal`
- 或使用快捷键：`Ctrl+Shift+T`

#### 执行命令
- 打开命令面板 (`Ctrl+Shift+P`)
- 运行：`Terminal Manager: Run Command in Terminal`
- 或使用快捷键：`Ctrl+Shift+R`

#### 其他功能
- `Terminal Manager: Get Terminal Output` - 获取终端输出
- `Terminal Manager: List All Terminals` - 列出所有终端
- `Terminal Manager: Clear Terminal` - 清除终端

## 🔧 开发功能

### 自动编译
- 修改任何 `.ts` 文件时自动编译
- 编译状态显示在状态栏
- 编译错误显示在问题面板

### 热重载
- 在开发模式下，修改源文件会自动重载扩展
- 可以选择立即重载或禁用自动重载

### 配置选项
在VSCode设置中添加：

```json
{
  "terminalManager.autoCompile": true,
  "terminalManager.autoReload": true,
  "terminalManager.shellIntegrationTimeout": 4000,
  "terminalManager.maxTerminals": 10
}
```

## 📊 测试结果

```
✅ 编译成功
✅ 所有测试通过
✅ 扩展正常激活
✅ 命令注册成功
✅ 无ESLint错误
```

## 🎯 核心功能验证

基于您原有的terminal目录功能：

1. **TerminalManager** ✅ - 终端创建和管理
2. **TerminalProcess** ✅ - 命令执行和输出处理
3. **TerminalRegistry** ✅ - 终端实例注册表
4. **ansiUtils** ✅ - ANSI转义序列处理
5. **get-latest-output** ✅ - 获取最新输出

## 🛠️ 可用的npm脚本

```bash
npm run compile      # 编译TypeScript
npm run watch        # 监听模式编译
npm run test         # 运行测试
npm run lint         # 代码检查
npm run lint:fix     # 自动修复代码风格
npm run dev          # 启动开发环境
npm run clean        # 清理编译输出
npm run rebuild      # 清理并重新编译
npm run package      # 打包扩展
```

## 🎉 成功！

您的VSCode Terminal Manager扩展现在已经完全可用了！

- ✅ 核心终端管理功能正常工作
- ✅ 自动编译功能已启用
- ✅ 文件重载功能已启用
- ✅ 所有命令都已注册
- ✅ 测试全部通过

您可以开始使用扩展的所有功能，或者继续开发新的特性。如果需要打包发布，运行 `npm run package` 即可生成 `.vsix` 文件。
