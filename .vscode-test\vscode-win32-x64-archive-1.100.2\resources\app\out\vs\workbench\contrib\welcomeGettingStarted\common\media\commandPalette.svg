<svg viewBox="0 0 520 260" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill="var(--vscode-editor-background, #1e1e1e)" d="M0 0h520v260H0z"/><g clip-path="url(#a)"><path fill="var(--vscode-editorGroupHeader-tabsBackground, #252526)" d="M0 0h520v39H0z"/><g clip-path="url(#b)"><path fill="var(--vscode-tab-activeBackground, #1e1e1e)" d="M0 0h115.654v39.3H0z"/><rect x="13.474" y="15.72" width="88.705" height="7.86" rx="3.93" fill="var(--vscode-tab-unfocusedInactiveForeground, #454545)"/></g></g><g filter="url(#c)"><path fill="var(--vscode-quickInput-background, #252526)" d="M48 20h425v182H48z"/><path fill="var(--vscode-input-background, #3c3c3c)" d="M55.5 27.5h410v25h-410z"/><path d="M61.168 43.324v1.384l6.297-2.748v-1.2l-6.297-2.736v1.396l4.71 1.95v.063l-4.71 1.891ZM73.666 45h1.365v-5.783h1.524v-1.091h-1.543v-.68c0-.698.311-1.047 1.073-1.047.216 0 .4.013.527.032v-1.029a5.038 5.038 0 0 0-.813-.063c-1.434 0-2.133.673-2.133 2.044v.742H72.53v1.092h1.136V45Zm5.142-8.125a.863.863 0 0 0 .863-.857.864.864 0 0 0-1.726 0c0 .47.387.857.863.857ZM78.128 45h1.36v-6.894h-1.36V45Zm3.429 0h1.364v-9.598h-1.364V45Zm7.947-1.847c-.254.558-.819.863-1.644.863-1.092 0-1.796-.787-1.841-2.038v-.063H90.9v-.47c0-2.158-1.161-3.472-3.085-3.472-1.948 0-3.186 1.403-3.186 3.593 0 2.203 1.212 3.567 3.193 3.567 1.58 0 2.697-.761 2.996-1.98h-1.314Zm-1.695-4.069c1.01 0 1.67.73 1.701 1.847H86.02c.076-1.11.78-1.847 1.79-1.847Z" fill="var(--vscode-input-foreground, #cccccc)"/><path stroke="var(--vscode-focusBorder, #0078d4)" d="M55.5 27.5h410v25h-410z"/><rect x="60" y="66" width="38" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity=".25"/><rect x="135" y="66" width="105" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity=".25"/><rect x="102" y="66" width="29" height="6" rx="3" fill="var(--vscode-pickerGroup-foreground)"/><path fill="var(--vscode-quickInputList-focusBackground, #062F4A)" d="M48 82h425v24H48z"/><path d="M63.028 99v-3.606h3.726v-1.517h-3.726v-2.456h4.081v-1.58h-5.998V99h1.917Zm6.608-8.061c.571 0 1.04-.458 1.04-1.029 0-.578-.469-1.034-1.04-1.034-.572 0-1.048.456-1.048 1.034a1.04 1.04 0 0 0 1.048 1.029ZM68.709 99h1.847v-6.989H68.71V99Zm3.783 0h1.847v-9.661h-1.847V99Zm8.297-1.974c-.197.476-.699.743-1.429.743-.965 0-1.574-.667-1.6-1.733v-.096h4.806v-.552c0-2.209-1.219-3.53-3.282-3.53-2.082 0-3.364 1.41-3.364 3.682 0 2.26 1.257 3.606 3.39 3.606 1.713 0 2.926-.819 3.199-2.12h-1.72Zm-1.499-3.79c.864 0 1.422.597 1.46 1.555h-2.983c.064-.939.667-1.555 1.523-1.555Z" fill="var(--vscode-list-focusHighlightForeground)"/><path d="M85.416 94.652c.686 0 1.124-.457 1.124-1.08 0-.621-.438-1.072-1.124-1.072-.68 0-1.123.45-1.123 1.073 0 .622.444 1.079 1.123 1.079Zm0 4.507c.686 0 1.124-.457 1.124-1.08 0-.621-.438-1.072-1.124-1.072-.68 0-1.123.45-1.123 1.073 0 .622.444 1.079 1.123 1.079ZM93.763 99v-6.652h.102L98.568 99h1.283v-9.16h-1.384v6.665h-.102l-4.703-6.665h-1.283V99h1.384Zm12.765-1.847c-.254.558-.819.863-1.644.863-1.092 0-1.796-.787-1.841-2.038v-.063h4.882v-.47c0-2.158-1.162-3.472-3.085-3.472-1.949 0-3.187 1.403-3.187 3.593 0 2.203 1.213 3.567 3.193 3.567 1.581 0 2.698-.761 2.996-1.98h-1.314Zm-1.695-4.069c1.01 0 1.67.73 1.702 1.847h-3.492c.077-1.11.781-1.847 1.79-1.847Zm13.68-.978h-1.365l-1.231 5.32h-.108l-1.422-5.32h-1.308l-1.422 5.32h-.101l-1.238-5.32h-1.384L110.838 99h1.403l1.422-5.142h.108L115.199 99h1.416l1.898-6.894Zm6.531-2.266h-1.421v6.005c0 1.98 1.415 3.377 3.707 3.377 2.304 0 3.713-1.396 3.713-3.377V89.84h-1.422v5.891c0 1.308-.831 2.228-2.291 2.228-1.454 0-2.286-.92-2.286-2.228v-5.89Zm8.132 9.16h1.365v-4.043c0-1.111.641-1.797 1.65-1.797s1.492.559 1.492 1.701V99h1.364v-4.462c0-1.644-.85-2.565-2.393-2.565-1.041 0-1.726.463-2.063 1.225h-.101v-1.092h-1.314V99Zm8.277-8.633v1.758h-1.098v1.092h1.098v3.968c0 1.326.603 1.86 2.12 1.86.267 0 .521-.032.743-.07v-1.086c-.191.02-.311.032-.521.032-.679 0-.977-.324-.977-1.067v-3.637h1.498v-1.091h-1.498v-1.759h-1.365Zm5.173.508a.863.863 0 0 0 .864-.857.864.864 0 0 0-1.727 0c0 .47.387.857.863.857ZM145.947 99h1.359v-6.894h-1.359V99Zm3.847-8.633v1.758h-1.098v1.092h1.098v3.968c0 1.326.603 1.86 2.12 1.86.267 0 .521-.032.743-.07v-1.086c-.191.02-.311.032-.521.032-.679 0-.977-.324-.977-1.067v-3.637h1.498v-1.091h-1.498v-1.759h-1.365ZM154.326 99h1.365v-9.598h-1.365V99Zm7.947-1.847c-.253.558-.818.863-1.644.863-1.091 0-1.796-.787-1.84-2.038v-.063h4.881v-.47c0-2.158-1.162-3.472-3.085-3.472-1.949 0-3.187 1.403-3.187 3.593 0 2.203 1.213 3.567 3.193 3.567 1.581 0 2.698-.761 2.996-1.98h-1.314Zm-1.694-4.069c1.009 0 1.669.73 1.701 1.847h-3.491c.076-1.11.78-1.847 1.79-1.847Zm7.255 6.03c.952 0 1.758-.45 2.171-1.206h.108V99h1.307v-9.598h-1.364v3.796h-.102c-.374-.755-1.174-1.212-2.12-1.212-1.746 0-2.869 1.39-2.869 3.56 0 2.184 1.111 3.568 2.869 3.568Zm.387-5.96c1.143 0 1.86.927 1.86 2.4 0 1.485-.711 2.392-1.86 2.392-1.155 0-1.847-.895-1.847-2.393 0-1.492.698-2.4 1.847-2.4ZM178.606 99v-3.834h3.936v-1.2h-3.936v-2.9h4.297V89.84h-5.719V99h1.422Zm6.69-8.125a.864.864 0 0 0 .864-.857.864.864 0 0 0-1.727 0c0 .47.387.857.863.857ZM184.617 99h1.359v-6.894h-1.359V99Zm3.428 0h1.365v-9.598h-1.365V99Zm7.947-1.847c-.254.558-.819.863-1.644.863-1.092 0-1.796-.787-1.841-2.038v-.063h4.882v-.47c0-2.158-1.162-3.472-3.085-3.472-1.949 0-3.187 1.403-3.187 3.593 0 2.203 1.213 3.567 3.193 3.567 1.581 0 2.698-.761 2.996-1.98h-1.314Zm-1.695-4.069c1.01 0 1.67.73 1.702 1.847h-3.492c.076-1.11.781-1.847 1.79-1.847Z" fill="var(--vscode-quickInputList-focusForeground, #E3E3E3)"/><rect x="60" y="116" width="62.5" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity=".25"/><rect x="126.5" y="116" width="29" height="6" rx="3" fill="var(--vscode-pickerGroup-foreground)"/><rect x="159.5" y="116" width="62.5" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity=".25"/><rect x="60" y="138" width="41" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity=".25"/><rect x="105" y="138" width="69" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity=".25"/><rect x="178" y="138" width="26" height="6" rx="3" fill="var(--vscode-pickerGroup-foreground)"/><rect x="60" y="160" width="26" height="6" rx="3" fill="var(--vscode-pickerGroup-foreground)"/><rect x="90" y="160" width="164" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity=".25"/><rect x="60" y="182" width="46" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity=".25"/><rect x="110" y="182" width="29" height="6" rx="3" fill="var(--vscode-pickerGroup-foreground)"/><rect x="143" y="182" width="46" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity=".25"/></g><defs><clipPath id="a"><path fill="#fff" d="M0 0h520v220H0z"/></clipPath><clipPath id="b"><path fill="#fff" d="M0 0h115.654v39.3H0z"/></clipPath><filter id="c" x="36" y="10" width="449" height="206" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="2"/><feGaussianBlur stdDeviation="6"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.6 0"/><feBlend in2="BackgroundImageFix" result="effect1_dropShadow"/><feBlend in="SourceGraphic" in2="effect1_dropShadow" result="shape"/></filter></defs></svg>