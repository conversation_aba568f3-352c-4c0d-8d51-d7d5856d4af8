import * as vscode from "vscode";
import { Logger, LogLevel } from "./services/logging/Logger";
import { TerminalManager } from "./terminal/TerminalManager";
import { AutoCompiler } from "./services/AutoCompiler";
import { FileWatcher } from "./services/FileWatcher";
import { registerCommands } from "./commands";

let terminalManager: TerminalManager;
let autoCompiler: AutoCompiler;
let fileWatcher: FileWatcher;

export function activate(context: vscode.ExtensionContext) {
	// Initialize logger
	Logger.initialize("Terminal Manager");
	
	// Set log level based on development mode
	const isDevelopment = context.extensionMode === vscode.ExtensionMode.Development;
	Logger.setLogLevel(isDevelopment ? LogLevel.DEBUG : LogLevel.INFO);
	
	Logger.info("Terminal Manager extension is activating...");

	try {
		// Initialize core services
		terminalManager = new TerminalManager();
		
		// Initialize auto-compilation service
		const config = vscode.workspace.getConfiguration("terminalManager");
		const autoCompileEnabled = config.get<boolean>("autoCompile", true);
		
		if (autoCompileEnabled) {
			autoCompiler = new AutoCompiler(context);
			context.subscriptions.push(autoCompiler);
		}

		// Initialize file watcher for auto-reload
		const autoReloadEnabled = config.get<boolean>("autoReload", true);
		
		if (autoReloadEnabled && isDevelopment) {
			fileWatcher = new FileWatcher(context);
			context.subscriptions.push(fileWatcher);
		}

		// Register commands
		registerCommands(context, terminalManager);

		// Listen for configuration changes
		const configChangeDisposable = vscode.workspace.onDidChangeConfiguration((e) => {
			if (e.affectsConfiguration("terminalManager")) {
				handleConfigurationChange(context);
			}
		});
		context.subscriptions.push(configChangeDisposable);

		Logger.info("Terminal Manager extension activated successfully");
		
		// Show welcome message in development mode
		if (isDevelopment) {
			vscode.window.showInformationMessage("Terminal Manager extension is now active in development mode!");
		}

	} catch (error) {
		Logger.error("Failed to activate Terminal Manager extension", error);
		vscode.window.showErrorMessage(`Failed to activate Terminal Manager: ${error}`);
	}
}

export function deactivate() {
	Logger.info("Terminal Manager extension is deactivating...");
	
	try {
		// Clean up resources
		if (autoCompiler) {
			autoCompiler.dispose();
		}
		
		if (fileWatcher) {
			fileWatcher.dispose();
		}
		
		Logger.info("Terminal Manager extension deactivated successfully");
	} catch (error) {
		Logger.error("Error during deactivation", error);
	} finally {
		Logger.dispose();
	}
}

function handleConfigurationChange(context: vscode.ExtensionContext) {
	const config = vscode.workspace.getConfiguration("terminalManager");
	const autoCompileEnabled = config.get<boolean>("autoCompile", true);
	const autoReloadEnabled = config.get<boolean>("autoReload", true);
	const isDevelopment = context.extensionMode === vscode.ExtensionMode.Development;

	// Handle auto-compile setting change
	if (autoCompileEnabled && !autoCompiler) {
		autoCompiler = new AutoCompiler(context);
		context.subscriptions.push(autoCompiler);
		Logger.info("Auto-compilation enabled");
	} else if (!autoCompileEnabled && autoCompiler) {
		autoCompiler.dispose();
		Logger.info("Auto-compilation disabled");
	}

	// Handle auto-reload setting change (only in development mode)
	if (autoReloadEnabled && isDevelopment && !fileWatcher) {
		fileWatcher = new FileWatcher(context);
		context.subscriptions.push(fileWatcher);
		Logger.info("Auto-reload enabled");
	} else if ((!autoReloadEnabled || !isDevelopment) && fileWatcher) {
		fileWatcher.dispose();
		Logger.info("Auto-reload disabled");
	}
}
