{"version": 3, "file": "AutoCompiler.js", "sourceRoot": "", "sources": ["../../src/services/AutoCompiler.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,mDAAqC;AACrC,2CAA6B;AAC7B,6CAA0C;AAE1C,MAAa,YAAY;IAMxB,YAAoB,OAAgC;QAAhC,YAAO,GAAP,OAAO,CAAyB;QAH5C,gBAAW,GAAG,KAAK,CAAC;QACX,kBAAa,GAAG,IAAI,CAAC,CAAC,oBAAoB;QAG1D,IAAI,CAAC,UAAU,EAAE,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,UAAU;QACvB,IAAI;YACH,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;YAC3D,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;gBACvD,eAAM,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;gBACrE,OAAO;aACP;YAED,MAAM,QAAQ,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;YAChD,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAE3C,0CAA0C;YAC1C,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE;gBACxC,GAAG,EAAE,OAAO;gBACZ,OAAO,EAAE;oBACR,cAAc;oBACd,cAAc;oBACd,oBAAoB;oBACpB,WAAW;oBACX,YAAY;iBACZ;gBACD,aAAa,EAAE,IAAI;gBACnB,UAAU,EAAE,IAAI;aAChB,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,EAAE;gBACtC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,EAAE;gBACnC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,EAAE;gBACtC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAClC,eAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,wCAAwC,OAAO,EAAE,CAAC,CAAC;SAE/D;QAAC,OAAO,KAAK,EAAE;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;SAC1D;IACF,CAAC;IAEO,mBAAmB,CAAC,QAAgB;QAC3C,IAAI,IAAI,CAAC,WAAW,EAAE;YACrB,eAAM,CAAC,KAAK,CAAC,kDAAkD,QAAQ,EAAE,CAAC,CAAC;YAC3E,OAAO;SACP;QAED,yBAAyB;QACzB,IAAI,IAAI,CAAC,cAAc,EAAE;YACxB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAClC;QAED,qCAAqC;QACrC,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,GAAG,EAAE;YACrC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACxB,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAEvB,eAAM,CAAC,KAAK,CAAC,8BAA8B,QAAQ,EAAE,CAAC,CAAC;IACxD,CAAC;IAEO,KAAK,CAAC,OAAO,CAAC,WAAmB;QACxC,IAAI,IAAI,CAAC,WAAW,EAAE;YACrB,OAAO;SACP;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,eAAM,CAAC,IAAI,CAAC,sCAAsC,WAAW,EAAE,CAAC,CAAC;QAEjE,IAAI;YACH,4BAA4B;YAC5B,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAChC,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,MAAM;gBACxC,KAAK,EAAE,yBAAyB;gBAChC,WAAW,EAAE,KAAK;aAClB,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;gBACrB,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC,CAAC;gBAE/D,6BAA6B;gBAC7B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAElD,IAAI,MAAM,CAAC,OAAO,EAAE;oBACnB,eAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;oBAClD,IAAI,CAAC,qBAAqB,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;iBAC9D;qBAAM;oBACN,eAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;oBAClD,IAAI,CAAC,qBAAqB,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;oBACzD,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;iBAC1C;YACF,CAAC,CAAC,CAAC;SAEH;QAAC,OAAO,KAAK,EAAE;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAClD,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,sBAAsB,KAAK,EAAE,CAAC,CAAC;SAC9D;gBAAS;YACT,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;SACzB;IACF,CAAC;IAEO,KAAK,CAAC,qBAAqB;QAClC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC9B,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;YAC3D,IAAI,CAAC,gBAAgB,EAAE;gBACtB,OAAO,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,2BAA2B,CAAC,EAAE,CAAC,CAAC;gBACnE,OAAO;aACP;YAED,MAAM,QAAQ,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;YAChD,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;gBAC7C,IAAI,EAAE,qBAAqB;gBAC3B,GAAG,EAAE,QAAQ;gBACb,YAAY,EAAE,IAAI;aAClB,CAAC,CAAC;YAEH,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,SAAS,GAAG,KAAK,CAAC;YAEtB,6DAA6D;YAC7D,iFAAiF;YAEjF,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;YAE5C,mCAAmC;YACnC,UAAU,CAAC,GAAG,EAAE;gBACf,QAAQ,CAAC,OAAO,EAAE,CAAC;gBAEnB,4EAA4E;gBAC5E,MAAM,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;gBACtD,MAAM,QAAQ,GAAa,EAAE,CAAC;gBAE9B,KAAK,MAAM,CAAC,GAAG,EAAE,eAAe,CAAC,IAAI,WAAW,EAAE;oBACjD,IAAI,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;wBAC/B,KAAK,MAAM,UAAU,IAAI,eAAe,EAAE;4BACzC,IAAI,UAAU,CAAC,QAAQ,KAAK,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAAE;gCAC5D,SAAS,GAAG,IAAI,CAAC;gCACjB,QAAQ,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,KAAK,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;6BACtD;yBACD;qBACD;iBACD;gBAED,OAAO,CAAC;oBACP,OAAO,EAAE,CAAC,SAAS;oBACnB,MAAM,EAAE,QAAQ;iBAChB,CAAC,CAAC;YACJ,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,iCAAiC;QAC5C,CAAC,CAAC,CAAC;IACJ,CAAC;IAEO,qBAAqB,CAAC,OAAe,EAAE,OAAgB;QAC9D,4BAA4B;QAC5B,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CACtD,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAC9B,GAAG,CACH,CAAC;QAEF,aAAa,CAAC,IAAI,GAAG,OAAO,CAAC;QAC7B,aAAa,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QACrF,aAAa,CAAC,IAAI,EAAE,CAAC;QAErB,uBAAuB;QACvB,UAAU,CAAC,GAAG,EAAE;YACf,aAAa,CAAC,OAAO,EAAE,CAAC;QACzB,CAAC,EAAE,IAAI,CAAC,CAAC;IACV,CAAC;IAEO,qBAAqB,CAAC,MAAgB;QAC7C,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,OAAO;SACP;QAED,MAAM,YAAY,GAAG,sCAAsC,MAAM,CAAC,MAAM,WAAW,CAAC;QACpF,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE;YAC/E,IAAI,SAAS,KAAK,cAAc,EAAE;gBACjC,gCAAgC;gBAChC,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,+BAA+B,CAAC,CAAC;gBACzF,aAAa,CAAC,KAAK,EAAE,CAAC;gBACtB,aAAa,CAAC,UAAU,CAAC,gCAAgC,CAAC,CAAC;gBAC3D,aAAa,CAAC,UAAU,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;gBAE1C,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;oBAC/B,aAAa,CAAC,UAAU,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,KAAK,EAAE,CAAC,CAAC;gBACpD,CAAC,CAAC,CAAC;gBAEH,aAAa,CAAC,IAAI,EAAE,CAAC;aACrB;QACF,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,OAAO;QACN,IAAI,IAAI,CAAC,cAAc,EAAE;YACxB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAClC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;SACrB;QAED,eAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;IACvC,CAAC;CACD;AAzND,oCAyNC"}