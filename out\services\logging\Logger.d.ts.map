{"version": 3, "file": "Logger.d.ts", "sourceRoot": "", "sources": ["../../../src/services/logging/Logger.ts"], "names": [], "mappings": "AAEA,oBAAY,QAAQ;IACnB,KAAK,IAAI;IACT,IAAI,IAAI;IACR,IAAI,IAAI;IACR,KAAK,IAAI;CACT;AAED,qBAAa,MAAM;IAClB,OAAO,CAAC,MAAM,CAAC,aAAa,CAAmC;IAC/D,OAAO,CAAC,MAAM,CAAC,QAAQ,CAA2B;IAElD;;;OAGG;IACH,MAAM,CAAC,UAAU,CAAC,WAAW,GAAE,MAA2B,GAAG,IAAI;IAIjE;;;OAGG;IACH,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,GAAG,IAAI;IAIzC;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI;IAI/C;;;;OAIG;IACH,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI;IAI9C;;;;OAIG;IACH,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI;IAI9C;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI;IAI/C;;OAEG;IACH,MAAM,CAAC,IAAI,IAAI,IAAI;IAInB;;OAEG;IACH,MAAM,CAAC,IAAI,IAAI,IAAI;IAInB;;OAEG;IACH,MAAM,CAAC,OAAO,IAAI,IAAI;IAKtB;;;;;OAKG;IACH,OAAO,CAAC,MAAM,CAAC,GAAG;CA+BlB"}