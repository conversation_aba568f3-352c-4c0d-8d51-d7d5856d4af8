{"version": 3, "file": "TerminalManager.js", "sourceRoot": "", "sources": ["../../src/terminal/TerminalManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,4DAAkC;AAClC,+CAAiC;AACjC,wCAA8C;AAC9C,uDAAgG;AAChG,yDAAoE;AA8EpE,MAAa,eAAe;IAM3B;QALQ,gBAAW,GAAgB,IAAI,GAAG,EAAE,CAAC;QACrC,cAAS,GAAiC,IAAI,GAAG,EAAE,CAAC;QACpD,gBAAW,GAAwB,EAAE,CAAC;QACtC,4BAAuB,GAAW,IAAI,CAAC;QAG9C,IAAI,UAAyC,CAAC;QAC9C,IAAI;YACH,UAAU,GAAI,MAAM,CAAC,MAAwB,CAAC,gCAAgC,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;gBAC5F,yHAAyH;gBACzH,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;YACtB,CAAC,CAAC,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACf,0EAA0E;SAC1E;QACD,IAAI,UAAU,EAAE;YACf,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SAClC;QAED,kEAAkE;QAClE,IAAI;YACH,MAAM,qBAAqB,GAAG,MAAM,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACjF,MAAM,YAAY,GAAG,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;gBAC/D,IAAI,YAAY,IAAI,YAAY,CAAC,gBAAgB,IAAI,YAAY,CAAC,WAAW,EAAE;oBAC9E,2DAA2D;oBAC3D,IAAI,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,EAAE;wBAC7C,MAAM,QAAQ,GAAG,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC;wBAClD,YAAY,CAAC,gBAAgB,GAAG,SAAS,CAAC;wBAC1C,YAAY,CAAC,WAAW,GAAG,SAAS,CAAC;wBACrC,QAAQ,EAAE,CAAC;qBACX;iBACD;YACF,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;SAC7C;QAAC,OAAO,KAAK,EAAE;YACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;SAClE;IACF,CAAC;IAED,qDAAqD;IAC7C,0BAA0B,CAAC,QAAyB;QAC3D,MAAM,SAAS,GAAG,mCAAgB,CAAC,eAAe,EAAE,CAAC;QACrD,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;IACvD,CAAC;IAED,+DAA+D;IACvD,qBAAqB,CAAC,YAA0B;QACvD,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE;YACnC,OAAO,KAAK,CAAC;SACb;QAED,MAAM,UAAU,GAAG,YAAY,CAAC,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE,MAAM,CAAC;QACvE,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC;QAExE,IAAI,CAAC,UAAU,EAAE;YAChB,OAAO,KAAK,CAAC;SACb;QAED,OAAO,IAAA,oBAAa,EAAC,UAAU,EAAE,SAAS,CAAC,CAAC;IAC7C,CAAC;IAED,UAAU,CAAC,YAA0B,EAAE,OAAe;QACrD,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC;QACzB,YAAY,CAAC,WAAW,GAAG,OAAO,CAAC;QACnC,MAAM,OAAO,GAAG,IAAI,iCAAe,EAAE,CAAC;QACtC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QAE7C,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE;YAC9B,YAAY,CAAC,IAAI,GAAG,KAAK,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,+HAA+H;QAC/H,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE,GAAG,EAAE;YACzC,OAAO,CAAC,GAAG,CAAC,8CAA8C,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC;YAC7E,yFAAyF;YACzF,mCAAgB,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YACjD,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YACzC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrD,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,EAAE;gBAC7B,OAAO,EAAE,CAAC;YACX,CAAC,CAAC,CAAC;YACH,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC/B,OAAO,CAAC,KAAK,CAAC,qBAAqB,YAAY,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC9D,MAAM,CAAC,KAAK,CAAC,CAAC;YACf,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,sEAAsE;QACtE,IAAI,YAAY,CAAC,QAAQ,CAAC,gBAAgB,EAAE;YAC3C,OAAO,CAAC,uBAAuB,GAAG,KAAK,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;SAC5C;aAAM;YACN,8DAA8D;YAC9D,OAAO,CAAC,GAAG,CACV,qEAAqE,YAAY,CAAC,EAAE,iBAAiB,IAAI,CAAC,uBAAuB,IAAI,CACrI,CAAC;YACF,IAAA,oBAAQ,EAAC,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,gBAAgB,KAAK,SAAS,EAAE;gBACpE,OAAO,EAAE,IAAI,CAAC,uBAAuB;aACrC,CAAC;iBACA,IAAI,CAAC,GAAG,EAAE;gBACV,OAAO,CAAC,GAAG,CACV,mEAAmE,YAAY,CAAC,EAAE,kBAAkB,CACpG,CAAC;YACH,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;gBACd,OAAO,CAAC,IAAI,CACX,6EAA6E,YAAY,CAAC,EAAE,KAAK,GAAG,CAAC,OAAO,EAAE,CAC9G,CAAC;YACH,CAAC,CAAC;iBACD,OAAO,CAAC,GAAG,EAAE;gBACb,OAAO,CAAC,GAAG,CAAC,yEAAyE,YAAY,CAAC,EAAE,GAAG,CAAC,CAAC;gBACzG,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;gBAC5D,IAAI,eAAe,IAAI,eAAe,CAAC,uBAAuB,EAAE;oBAC/D,eAAe,CAAC,uBAAuB,GAAG,KAAK,CAAC;oBAChD,eAAe,CAAC,GAAG,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;iBACpD;YACF,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,IAAA,8BAAY,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,GAAW;QACpC,MAAM,SAAS,GAAG,mCAAgB,CAAC,eAAe,EAAE,CAAC;QAErD,sEAAsE;QACtE,MAAM,gBAAgB,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE;YAC7C,IAAI,CAAC,CAAC,IAAI,EAAE;gBACX,OAAO,KAAK,CAAC;aACb;YACD,MAAM,WAAW,GAAG,CAAC,CAAC,QAAQ,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC,qEAAqE;YAC3H,IAAI,CAAC,WAAW,EAAE;gBACjB,OAAO,KAAK,CAAC;aACb;YACD,OAAO,IAAA,oBAAa,EAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QACH,IAAI,gBAAgB,EAAE;YACrB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;YAC1C,OAAO,gBAAgB,CAAC;SACxB;QAED,oEAAoE;QACpE,MAAM,iBAAiB,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QACzD,IAAI,iBAAiB,EAAE;YACtB,6CAA6C;YAC7C,MAAM,UAAU,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACxD,iBAAiB,CAAC,gBAAgB,GAAG,GAAG,CAAC;gBACzC,iBAAiB,CAAC,WAAW,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;YACrD,CAAC,CAAC,CAAC;YAEH,yCAAyC;YACzC,MAAM,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,OAAO,GAAG,GAAG,CAAC,CAAC;YAExD,8EAA8E;YAC9E,IAAI,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,EAAE;gBAClD,IAAI,iBAAiB,CAAC,WAAW,EAAE;oBAClC,iBAAiB,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;iBACxC;gBACD,iBAAiB,CAAC,gBAAgB,GAAG,SAAS,CAAC;gBAC/C,iBAAiB,CAAC,WAAW,GAAG,SAAS,CAAC;aAC1C;iBAAM;gBACN,IAAI;oBACH,wDAAwD;oBACxD,MAAM,OAAO,CAAC,IAAI,CAAC;wBAClB,UAAU;wBACV,IAAI,OAAO,CAAO,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,CAC/B,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,oCAAoC,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,CACpF;qBACD,CAAC,CAAC;iBACH;gBAAC,OAAO,GAAG,EAAE;oBACb,iCAAiC;oBACjC,iBAAiB,CAAC,gBAAgB,GAAG,SAAS,CAAC;oBAC/C,iBAAiB,CAAC,WAAW,GAAG,SAAS,CAAC;iBAC1C;aACD;YACD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAC3C,OAAO,iBAAiB,CAAC;SACzB;QAED,8CAA8C;QAC9C,MAAM,eAAe,GAAG,mCAAgB,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QAC7D,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;QACzC,OAAO,eAAe,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,GAAW,EAAE,UAAmB;QACvD,0DAA0D;QAC1D,MAAM,aAAa,GAAG,mCAAgB,CAAC,gBAAgB,EAAE,CAAC;QAC1D,MAAM,WAAW,GAAG,UAAU,IAAI,aAAa,aAAa,GAAG,CAAC,EAAE,CAAC;QACnE,MAAM,eAAe,GAAG,mCAAgB,CAAC,iBAAiB,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;QAC7E,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;QACzC,OAAO,eAAe,CAAC;IACxB,CAAC;IAED,YAAY,CAAC,IAAa;QACzB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;aACjC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,mCAAgB,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;aAC7C,MAAM,CAAC,CAAC,CAAC,EAAqB,EAAE,CAAC,CAAC,KAAK,SAAS,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC;aACpE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC1D,CAAC;IAED,oBAAoB,CAAC,UAAkB;QACtC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;YACtC,OAAO,EAAE,CAAC;SACV;QACD,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC/C,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACtD,CAAC;IAED,YAAY,CAAC,UAAkB;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC/C,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;IACxC,CAAC;IAED,UAAU;QACT,uCAAuC;QACvC,oFAAoF;QACpF,IAAI;QACJ,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;QAC/D,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IACvB,CAAC;IAED,0BAA0B,CAAC,OAAe;QACzC,IAAI,CAAC,uBAAuB,GAAG,OAAO,CAAC;IACxC,CAAC;CACD;AAvOD,0CAuOC"}