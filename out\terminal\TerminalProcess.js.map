{"version": 3, "file": "TerminalProcess.js", "sourceRoot": "", "sources": ["../../src/terminal/TerminalProcess.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AACtC,2CAAwC;AAExC,uDAAoD;AAUpD,uFAAuF;AACvF,MAAM,0BAA0B,GAAG,IAAK,CAAC;AACzC,MAAM,6BAA6B,GAAG,KAAM,CAAC;AAE7C,MAAa,eAAgB,SAAQ,qBAAmC;IAAxE;;QACC,4BAAuB,GAAY,IAAI,CAAC;QAChC,gBAAW,GAAY,IAAI,CAAC;QAC5B,WAAM,GAAW,EAAE,CAAC;QACpB,eAAU,GAAW,EAAE,CAAC;QACxB,uBAAkB,GAAW,CAAC,CAAC;QACvC,UAAK,GAAY,KAAK,CAAC;QACf,aAAQ,GAA0B,IAAI,CAAC;IAoQhD,CAAC;IAlQA,kBAAkB;IAClB,WAAW;IAEX,KAAK,CAAC,GAAG,CAAC,QAAyB,EAAE,OAAe;QACnD,IAAI,QAAQ,CAAC,gBAAgB,IAAI,QAAQ,CAAC,gBAAgB,CAAC,cAAc,EAAE;YAC1E,MAAM,SAAS,GAAG,QAAQ,CAAC,gBAAgB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACpE,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC;YAChC,8BAA8B;YAC9B,IAAI,YAAY,GAAG,IAAI,CAAC;YACxB,IAAI,mBAAmB,GAAG,KAAK,CAAC;YAChC,IAAI,gBAAgB,GAAG,KAAK,CAAC;YAC7B,IAAI,iBAAiC,CAAC;YAEtC,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC;YAC/C,MAAM,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;YAEzC,MAAM,SAAS,GAAG,GAAG,EAAE;gBACtB,4EAA4E;gBAC5E,oFAAoF;gBACpF,iFAAiF;gBACjF,2FAA2F;gBAC3F,2FAA2F;gBAC3F,4DAA4D;gBAC5D,8EAA8E;gBAC9E,sEAAsE;gBAEtE,eAAM,CAAC,KAAK,CACX,qGAAqG,CACrG,CAAC;gBACF,IAAI;oBACH,QAAQ,CAAC,OAAO,EAAE,CAAC;iBACnB;gBAAC,OAAO,GAAG,EAAE;oBACb,eAAM,CAAC,KAAK,CAAC,qDAAqD,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;iBACjF;gBACD,IAAI,CAAC,IAAI,CACR,OAAO,EACP,IAAI,KAAK,CAAC,+FAA+F,CAAC,CAC1G,CAAC;gBACF,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACvB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACvB,CAAC,CAAC;YAEF,iBAAiB,GAAG,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YAErD,IAAI,KAAK,EAAE,IAAI,IAAI,IAAI,MAAM,EAAE;gBAC9B,YAAY,CAAC,iBAAiB,CAAC,CAAC;gBAEhC,wCAAwC;gBACxC,IAAI,YAAY,EAAE;oBACjB;;sBAEE;oBAEF,8FAA8F;oBAC9F;;;;;;;sBAOE;oBACF,mNAAmN;oBACnN,mIAAmI;oBACnI,sEAAsE;oBACtE,MAAM,sBAAsB,GAAG,IAAI,CAAC,uBAAuB,CAC1D,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CACjD,CAAC,IAAI,EAAE,CAAC;oBAET,uHAAuH;oBACvH,qGAAqG;oBACrG,MAAM,mBAAmB,GAAG,0BAA0B,CAAC;oBACvD,MAAM,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;oBAChE,IAAI,SAAS,IAAI,SAAS,CAAC,KAAK,KAAK,SAAS,EAAE;wBAC/C,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;qBACzD;oBACD,oDAAoD;oBACpD,IAAI,sBAAsB,EAAE;wBAC3B,IAAI,GAAG,sBAAsB,GAAG,IAAI,GAAG,IAAI,CAAC;qBAC5C;oBACD,cAAc;oBACd,IAAI,GAAG,IAAA,qBAAS,EAAC,IAAI,CAAC,CAAC;oBACvB,yBAAyB;oBACzB,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC3C,2DAA2D;oBAC3D,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;wBACrB,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;qBACjD;oBACD,yEAAyE;oBACzE,qEAAqE;oBACrE,IACC,KAAK,CAAC,MAAM,GAAG,CAAC;wBAChB,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC;wBACpB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC3B,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACpD;wBACD,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;qBAC7B;oBACD,4FAA4F;oBAC5F,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;wBACrB,6FAA6F;wBAC7F,0DAA0D;wBAC1D,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC,CAAC;qBACvD;oBACD,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;wBACrB,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC,CAAC;qBACvD;oBACD,kBAAkB;oBAClB,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACxB,YAAY,GAAG,KAAK,CAAC;iBACrB;qBAAM;oBACN,IAAI,GAAG,IAAA,qBAAS,EAAC,IAAI,CAAC,CAAC;iBACvB;gBAED,wEAAwE;gBACxE,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;oBACnD,IAAI,IAAI,CAAC,QAAQ,EAAE;wBAClB,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;qBAC5B;oBACD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;oBACnB,MAAM;iBACN;gBAED,6EAA6E;gBAC7E,kDAAkD;gBAClD,IAAI,CAAC,mBAAmB,EAAE;oBACzB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBACtC,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE;4BACtC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;4BACnB,CAAC,EAAE,CAAC,CAAC,6BAA6B;yBAClC;6BAAM;4BACN,mBAAmB,GAAG,IAAI,CAAC;4BAC3B,MAAM;yBACN;qBACD;oBACD,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACxB;gBAED,wCAAwC;gBACxC,gEAAgE;gBAChE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;gBAClB,IAAI,IAAI,CAAC,QAAQ,EAAE;oBAClB,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;iBAC5B;gBACD,+JAA+J;gBAC/J,MAAM,gBAAgB,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;gBACxG,MAAM,gBAAgB,GAAG;oBACxB,UAAU;oBACV,SAAS;oBACT,QAAQ;oBACR,UAAU;oBACV,SAAS;oBACT,MAAM;oBACN,KAAK;oBACL,MAAM;oBACN,MAAM;oBACN,WAAW;oBACX,OAAO;oBACP,MAAM;iBACN,CAAC;gBACF,MAAM,WAAW,GAChB,gBAAgB,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;oBACpF,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;gBAC7F,IAAI,CAAC,QAAQ,GAAG,UAAU,CACzB,GAAG,EAAE;oBACJ,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;gBACpB,CAAC,EACD,WAAW,CAAC,CAAC,CAAC,6BAA6B,CAAC,CAAC,CAAC,0BAA0B,CACxE,CAAC;gBAEF,yNAAyN;gBACzN,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE;oBAClD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC,wDAAwD;oBAC/E,gBAAgB,GAAG,IAAI,CAAC;iBACxB;gBAED,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC;gBACxB,IAAI,IAAI,CAAC,WAAW,EAAE;oBACrB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;oBACrB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;iBACtE;aACD;YAED,IAAI,CAAC,8BAA8B,EAAE,CAAC;YAEtC,0PAA0P;YAC1P,IAAI,IAAI,CAAC,QAAQ,EAAE;gBAClB,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC5B;YACD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;YAEnB,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACvB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SACtB;aAAM;YACN,QAAQ,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACjC,oFAAoF;YACpF,sDAAsD;YACtD,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACvB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACtB,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAClC,qBAAqB;YACrB,6DAA6D;YAC7D,8GAA8G;YAC9G,yCAAyC;SACzC;IACF,CAAC;IAED,qFAAqF;IAC7E,SAAS,CAAC,KAAa;QAC9B,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC;QACrB,IAAI,YAAoB,CAAC;QACzB,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;YACzD,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,sBAAsB;YAC/E,wDAAwD;YACxD,6BAA6B;YAC7B,4BAA4B;YAC5B,IAAI;YACJ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YACxB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;SAClD;IACF,CAAC;IAEO,8BAA8B;QACrC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YACpC,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAClE,IAAI,eAAe,EAAE;gBACpB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;aACnC;YACD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;YACjB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;SACjD;IACF,CAAC;IAED,QAAQ;QACP,IAAI,CAAC,8BAA8B,EAAE,CAAC;QACtC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAChC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACvB,CAAC;IAED,oBAAoB;QACnB,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACnE,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QACjD,OAAO,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;IAED,sLAAsL;IACtL,sFAAsF;IACtF,uBAAuB,CAAC,MAAc;QACrC,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACrB,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACzC,sEAAsE;YACtE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;SAC7D;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;IACnC,CAAC;CACD;AA3QD,0CA2QC;AAID,gLAAgL;AAChL,SAAgB,YAAY,CAAC,OAAwB,EAAE,OAAsB;IAC5E,MAAM,sBAAsB,GAAG,CAAC,KAAK,IAAI,EAAE,GAAE,CAAC,CAAC,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC;IACxE,MAAM,WAAW,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC,GAAG,CACnD,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,wBAAwB,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAU,CACrG,CAAC;IACF,KAAK,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,WAAW,EAAE;QACjD,IAAI,UAAU,EAAE;YACf,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC7C,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE,GAAG,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC;SACpE;KACD;IACD,OAAO,OAAuC,CAAC;AAChD,CAAC;AAZD,oCAYC"}