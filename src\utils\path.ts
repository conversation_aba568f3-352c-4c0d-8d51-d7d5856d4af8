import * as path from "path";
import * as os from "os";

/**
 * Compare two paths for equality, handling platform-specific differences
 * @param path1 First path to compare
 * @param path2 Second path to compare
 * @returns true if paths are equal, false otherwise
 */
export function arePathsEqual(path1: string, path2: string): boolean {
	if (!path1 || !path2) {
		return false;
	}

	// Normalize paths to handle different separators and resolve relative paths
	const normalizedPath1 = path.resolve(path.normalize(path1));
	const normalizedPath2 = path.resolve(path.normalize(path2));

	// On Windows, paths are case-insensitive
	if (os.platform() === "win32") {
		return normalizedPath1.toLowerCase() === normalizedPath2.toLowerCase();
	}

	// On Unix-like systems, paths are case-sensitive
	return normalizedPath1 === normalizedPath2;
}

/**
 * Get the relative path from one directory to another
 * @param from Source directory
 * @param to Target directory
 * @returns Relative path from source to target
 */
export function getRelativePath(from: string, to: string): string {
	return path.relative(from, to);
}

/**
 * Check if a path is absolute
 * @param pathToCheck Path to check
 * @returns true if path is absolute, false otherwise
 */
export function isAbsolutePath(pathToCheck: string): boolean {
	return path.isAbsolute(pathToCheck);
}

/**
 * Join multiple path segments
 * @param segments Path segments to join
 * @returns Joined path
 */
export function joinPaths(...segments: string[]): string {
	return path.join(...segments);
}

/**
 * Get the directory name of a path
 * @param filePath File path
 * @returns Directory name
 */
export function getDirname(filePath: string): string {
	return path.dirname(filePath);
}

/**
 * Get the base name of a path
 * @param filePath File path
 * @param ext Optional extension to remove
 * @returns Base name
 */
export function getBasename(filePath: string, ext?: string): string {
	return path.basename(filePath, ext);
}

/**
 * Get the extension of a file path
 * @param filePath File path
 * @returns File extension including the dot
 */
export function getExtension(filePath: string): string {
	return path.extname(filePath);
}
