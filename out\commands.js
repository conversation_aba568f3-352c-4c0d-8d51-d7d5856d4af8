"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerCommands = void 0;
const vscode = __importStar(require("vscode"));
const TerminalRegistry_1 = require("./terminal/TerminalRegistry");
const Logger_1 = require("./services/logging/Logger");
function registerCommands(context, terminalManager) {
    // Create output channel for command results
    const outputChannel = vscode.window.createOutputChannel("Terminal Manager - Command Output");
    context.subscriptions.push(outputChannel);
    // Set up global terminal output monitoring
    setupGlobalTerminalMonitoring(outputChannel, context);
    // Execute command in new terminal
    const executeCommandCommand = vscode.commands.registerCommand("terminalManager.executeCommand", async () => {
        try {
            // Get command from user
            const command = await vscode.window.showInputBox({
                prompt: "Enter the command to execute",
                placeHolder: "e.g., npm install, ls, dir, python script.py",
                validateInput: (value) => {
                    if (!value || value.trim() === "") {
                        return "Command cannot be empty";
                    }
                    return null;
                }
            });
            if (!command) {
                Logger_1.Logger.debug("Command execution cancelled - no command provided");
                return;
            }
            // Use default working directory (workspace root or current directory)
            const workspaceFolders = vscode.workspace.workspaceFolders;
            const cwd = workspaceFolders ? workspaceFolders[0].uri.fsPath : require("process").cwd();
            Logger_1.Logger.info(`Executing command: ${command} in directory: ${cwd}`);
            Logger_1.Logger.debug(`Command details - Input: "${command}", Working Directory: "${cwd}"`);
            // Clear previous output and show the output channel
            outputChannel.clear();
            outputChannel.show(true);
            // Add header to output
            const timestamp = new Date().toLocaleString();
            outputChannel.appendLine("=".repeat(80));
            outputChannel.appendLine(`Terminal Manager - Extension Command Execution`);
            outputChannel.appendLine(`Timestamp: ${timestamp}`);
            outputChannel.appendLine(`Command: ${command}`);
            outputChannel.appendLine(`Working Directory: ${cwd}`);
            outputChannel.appendLine(`Source: Extension (via Command Palette)`);
            outputChannel.appendLine("=".repeat(80));
            outputChannel.appendLine("");
            // Add debug info to output
            outputChannel.appendLine(`[DEBUG] Creating new terminal for directory: ${cwd}`);
            Logger_1.Logger.debug(`Creating new terminal for directory: ${cwd}`);
            // Always create a new terminal for each command execution
            const terminalInfo = await terminalManager.createNewTerminal(cwd.trim());
            terminalInfo.terminal.show();
            outputChannel.appendLine(`[DEBUG] New terminal created with ID: ${terminalInfo.id}`);
            outputChannel.appendLine(`[DEBUG] Terminal name: ${terminalInfo.name}`);
            outputChannel.appendLine(`[DEBUG] Terminal working directory: ${terminalInfo.cwd}`);
            outputChannel.appendLine(`[DEBUG] Terminal busy status: ${terminalInfo.busy}`);
            Logger_1.Logger.debug(`New terminal created - ID: ${terminalInfo.id}, Name: ${terminalInfo.name}, Busy: ${terminalInfo.busy}`);
            // Execute the command and capture output
            outputChannel.appendLine(`[DEBUG] Starting command execution...`);
            Logger_1.Logger.debug(`Starting command execution: ${command}`);
            const terminalProcess = terminalManager.runCommand(terminalInfo, command.trim());
            // Show progress and capture output
            let fullOutput = "";
            let hasOutput = false;
            let lineCount = 0;
            vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: `Executing: ${command}`,
                cancellable: true
            }, async (progress, token) => {
                outputChannel.appendLine(`[DEBUG] Command process started, listening for output...`);
                Logger_1.Logger.debug("Command process started, setting up output listeners");
                terminalProcess.on("line", (line) => {
                    hasOutput = true;
                    lineCount++;
                    fullOutput += line + "\n";
                    // Update progress
                    progress.report({ message: line.substring(0, 50) + (line.length > 50 ? "..." : "") });
                    // Output to OUTPUT panel with terminal ID and line numbers
                    outputChannel.appendLine(`[T${terminalInfo.id}:${lineCount.toString().padStart(3, '0')}] ${line}`);
                    // Debug logging
                    Logger_1.Logger.debug(`Extension command output (Terminal ${terminalInfo.id}) line ${lineCount}: ${line.substring(0, 100)}${line.length > 100 ? '...' : ''}`);
                    // Console output for development
                    console.log(`[Terminal ${terminalInfo.id} Output ${lineCount}] ${line}`);
                });
                token.onCancellationRequested(() => {
                    outputChannel.appendLine(`[DEBUG] Command execution cancelled by user`);
                    Logger_1.Logger.info("Command execution cancelled by user");
                    terminalProcess.continue();
                });
                try {
                    outputChannel.appendLine(`[DEBUG] Waiting for command completion...`);
                    await terminalProcess;
                    // Command completed successfully
                    const completionTime = new Date().toLocaleString();
                    outputChannel.appendLine("");
                    outputChannel.appendLine("=".repeat(80));
                    outputChannel.appendLine(`[SUCCESS] Extension command completed successfully at ${completionTime}`);
                    outputChannel.appendLine(`[STATS] Terminal ID: ${terminalInfo.id}`);
                    outputChannel.appendLine(`[STATS] Terminal Name: ${terminalInfo.name}`);
                    outputChannel.appendLine(`[STATS] Total output lines: ${lineCount}`);
                    outputChannel.appendLine(`[STATS] Total output length: ${fullOutput.length} characters`);
                    outputChannel.appendLine("=".repeat(80));
                    const message = `Command completed on Terminal ${terminalInfo.id}: ${command} (${lineCount} lines of output)`;
                    vscode.window.showInformationMessage(message);
                    Logger_1.Logger.info(`Extension command completed successfully on Terminal ${terminalInfo.id}: ${command}, Lines: ${lineCount}, Length: ${fullOutput.length}`);
                    if (!hasOutput) {
                        outputChannel.appendLine(`[INFO] Command completed but no output was captured`);
                        Logger_1.Logger.debug("Command completed but no output was captured");
                    }
                }
                catch (error) {
                    // Command failed
                    const errorTime = new Date().toLocaleString();
                    outputChannel.appendLine("");
                    outputChannel.appendLine("=".repeat(80));
                    outputChannel.appendLine(`[ERROR] Extension command failed at ${errorTime}`);
                    outputChannel.appendLine(`[ERROR] Terminal ID: ${terminalInfo.id}`);
                    outputChannel.appendLine(`[ERROR] Terminal Name: ${terminalInfo.name}`);
                    outputChannel.appendLine(`[ERROR] Error details: ${error}`);
                    outputChannel.appendLine(`[STATS] Output lines before failure: ${lineCount}`);
                    outputChannel.appendLine("=".repeat(80));
                    const errorMessage = `Command failed on Terminal ${terminalInfo.id}: ${error}`;
                    vscode.window.showErrorMessage(errorMessage);
                    Logger_1.Logger.error(`Extension command failed on Terminal ${terminalInfo.id}: ${command}`, error);
                    Logger_1.Logger.debug(`Extension command failure details - Terminal: ${terminalInfo.id}, Lines captured: ${lineCount}, Error: ${error}`);
                }
            });
        }
        catch (error) {
            const errorMessage = `Failed to execute command: ${error}`;
            outputChannel.appendLine("");
            outputChannel.appendLine("=".repeat(80));
            outputChannel.appendLine(`[FATAL ERROR] Failed to execute command`);
            outputChannel.appendLine(`[FATAL ERROR] Error details: ${error}`);
            outputChannel.appendLine("=".repeat(80));
            vscode.window.showErrorMessage(errorMessage);
            Logger_1.Logger.error(errorMessage, error);
            Logger_1.Logger.debug(`Fatal error details: ${error}`);
        }
    });
    // Test command to verify sendText monitoring
    const testSendTextCommand = vscode.commands.registerCommand("terminalManager.testSendText", async () => {
        try {
            Logger_1.Logger.info("Testing sendText monitoring...");
            // Get or create a terminal
            const workspaceFolders = vscode.workspace.workspaceFolders;
            const cwd = workspaceFolders ? workspaceFolders[0].uri.fsPath : require("process").cwd();
            const terminalInfo = await terminalManager.createNewTerminal(cwd, "SendText Test Terminal");
            terminalInfo.terminal.show();
            // Add header to output
            outputChannel.appendLine("");
            outputChannel.appendLine("=".repeat(80));
            outputChannel.appendLine(`Terminal Manager - SendText Test`);
            outputChannel.appendLine(`Timestamp: ${new Date().toLocaleString()}`);
            outputChannel.appendLine(`Terminal ID: ${terminalInfo.id}`);
            outputChannel.appendLine(`Terminal Name: ${terminalInfo.name}`);
            outputChannel.appendLine(`Method: terminal.sendText()`);
            outputChannel.appendLine("=".repeat(80));
            outputChannel.appendLine("");
            // Wait a moment for terminal to be ready
            await new Promise(resolve => setTimeout(resolve, 1000));
            // Use sendText to execute a command
            const testCommand = 'echo "This is a sendText test command"';
            outputChannel.appendLine(`[DEBUG] Sending command via sendText: ${testCommand}`);
            Logger_1.Logger.debug(`Sending command via sendText: ${testCommand}`);
            terminalInfo.terminal.sendText(testCommand);
            vscode.window.showInformationMessage(`SendText test executed on Terminal ${terminalInfo.id}. Check OUTPUT panel to see if it's monitored.`);
        }
        catch (error) {
            const errorMessage = `Failed to test sendText: ${error}`;
            vscode.window.showErrorMessage(errorMessage);
            Logger_1.Logger.error(errorMessage, error);
        }
    });
    // Register the commands
    context.subscriptions.push(executeCommandCommand);
    context.subscriptions.push(testSendTextCommand);
    Logger_1.Logger.info("Commands registered successfully");
    Logger_1.Logger.debug("Output channel created and commands registered");
}
exports.registerCommands = registerCommands;
/**
 * Set up global terminal output monitoring to capture all terminal activity
 */
function setupGlobalTerminalMonitoring(outputChannel, context) {
    Logger_1.Logger.info("Setting up global terminal output monitoring...");
    // Track active terminal executions
    const activeExecutions = new Map();
    try {
        // Monitor shell execution start events
        const onDidStartExecution = vscode.window.onDidStartTerminalShellExecution?.((e) => {
            if (!e || !e.execution) {
                return;
            }
            const executionId = generateExecutionId(e);
            const startTime = new Date();
            // Try to find the terminal in our registry
            const terminalInfo = TerminalRegistry_1.TerminalRegistry.getTerminalByVSCodeTerminal(e.terminal);
            const terminalId = terminalInfo?.id || "Unknown";
            const terminalName = terminalInfo?.name || e.terminal.name || "Unnamed";
            const terminalCwd = terminalInfo?.cwd || e.terminal.shellIntegration?.cwd?.fsPath || "Unknown";
            Logger_1.Logger.debug(`Shell execution started: ${executionId} on terminal ${terminalId} (${terminalName})`);
            // Store execution info
            activeExecutions.set(executionId, {
                execution: e.execution,
                startTime,
                commandLine: e.execution.commandLine?.value || "Unknown command",
                outputLines: 0
            });
            // Add header for manual command execution
            outputChannel.appendLine("");
            outputChannel.appendLine("=".repeat(80));
            outputChannel.appendLine(`Terminal Manager - Manual Command Detected`);
            outputChannel.appendLine(`Timestamp: ${startTime.toLocaleString()}`);
            outputChannel.appendLine(`Command: ${e.execution.commandLine?.value || "Unknown command"}`);
            outputChannel.appendLine(`Terminal ID: ${terminalId}`);
            outputChannel.appendLine(`Terminal Name: ${terminalName}`);
            outputChannel.appendLine(`Working Directory: ${terminalCwd}`);
            outputChannel.appendLine(`Source: Manual (typed in terminal)`);
            outputChannel.appendLine("=".repeat(80));
            outputChannel.appendLine("");
            // Set up output stream monitoring
            try {
                const stream = e.execution.read();
                let lineCount = 0;
                // Process the async iterable stream
                (async () => {
                    try {
                        for await (const data of stream) {
                            if (data && typeof data === 'string') {
                                const lines = data.split('\n');
                                for (const line of lines) {
                                    if (line.trim()) {
                                        lineCount++;
                                        outputChannel.appendLine(`[T${terminalId}:${lineCount.toString().padStart(3, '0')}] ${line}`);
                                        Logger_1.Logger.debug(`Manual command output (Terminal ${terminalId}) line ${lineCount}: ${line.substring(0, 100)}${line.length > 100 ? '...' : ''}`);
                                    }
                                }
                                // Update execution info
                                const execInfo = activeExecutions.get(executionId);
                                if (execInfo) {
                                    execInfo.outputLines = lineCount;
                                }
                            }
                        }
                    }
                    catch (streamError) {
                        Logger_1.Logger.error(`Error reading terminal stream for ${executionId}`, streamError);
                        outputChannel.appendLine(`[ERROR] Failed to read terminal output: ${streamError}`);
                    }
                })();
            }
            catch (error) {
                Logger_1.Logger.error(`Error setting up stream monitoring for ${executionId}`, error);
                outputChannel.appendLine(`[ERROR] Failed to monitor command output: ${error}`);
            }
        });
        if (onDidStartExecution) {
            context.subscriptions.push(onDidStartExecution);
            Logger_1.Logger.info("Global terminal execution monitoring enabled");
        }
        else {
            Logger_1.Logger.warn("Terminal shell execution monitoring not available in this VSCode version");
        }
        // Monitor shell execution end events
        const onDidEndExecution = vscode.window.onDidEndTerminalShellExecution?.((e) => {
            if (!e || !e.execution) {
                return;
            }
            const executionId = generateExecutionId(e);
            const execInfo = activeExecutions.get(executionId);
            if (execInfo) {
                const endTime = new Date();
                const duration = endTime.getTime() - execInfo.startTime.getTime();
                const exitCode = e.exitCode;
                // Try to find the terminal info again for end event
                const terminalInfo = TerminalRegistry_1.TerminalRegistry.getTerminalByVSCodeTerminal(e.terminal);
                const terminalId = terminalInfo?.id || "Unknown";
                const terminalName = terminalInfo?.name || e.terminal.name || "Unnamed";
                outputChannel.appendLine("");
                outputChannel.appendLine("=".repeat(80));
                if (exitCode === 0) {
                    outputChannel.appendLine(`[SUCCESS] Manual command completed successfully at ${endTime.toLocaleString()}`);
                }
                else {
                    outputChannel.appendLine(`[ERROR] Manual command failed at ${endTime.toLocaleString()}`);
                    outputChannel.appendLine(`[ERROR] Exit code: ${exitCode}`);
                }
                outputChannel.appendLine(`[STATS] Terminal ID: ${terminalId}`);
                outputChannel.appendLine(`[STATS] Terminal Name: ${terminalName}`);
                outputChannel.appendLine(`[STATS] Duration: ${duration}ms`);
                outputChannel.appendLine(`[STATS] Output lines: ${execInfo.outputLines}`);
                outputChannel.appendLine(`[STATS] Command: ${execInfo.commandLine}`);
                outputChannel.appendLine("=".repeat(80));
                Logger_1.Logger.info(`Manual command completed on Terminal ${terminalId}: ${execInfo.commandLine}, Duration: ${duration}ms, Lines: ${execInfo.outputLines}, Exit code: ${exitCode}`);
                // Clean up
                activeExecutions.delete(executionId);
            }
        });
        if (onDidEndExecution) {
            context.subscriptions.push(onDidEndExecution);
            Logger_1.Logger.info("Global terminal execution end monitoring enabled");
        }
    }
    catch (error) {
        Logger_1.Logger.error("Failed to set up global terminal monitoring", error);
        outputChannel.appendLine(`[ERROR] Failed to set up global terminal monitoring: ${error}`);
    }
}
/**
 * Generate a unique ID for terminal execution tracking
 */
function generateExecutionId(e) {
    const terminalId = e.terminal?.processId || e.terminal?.name || 'unknown';
    const timestamp = Date.now();
    const commandHash = e.execution?.commandLine?.value ?
        e.execution.commandLine.value.substring(0, 20).replace(/\s+/g, '_') : 'unknown';
    return `${terminalId}_${timestamp}_${commandHash}`;
}
//# sourceMappingURL=commands.js.map