"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerCommands = void 0;
const vscode = __importStar(require("vscode"));
const Logger_1 = require("./services/logging/Logger");
function registerCommands(context, terminalManager) {
    // Create output channel for command results
    const outputChannel = vscode.window.createOutputChannel("Terminal Manager - Command Output");
    context.subscriptions.push(outputChannel);
    // Execute command in new terminal
    const executeCommandCommand = vscode.commands.registerCommand("terminalManager.executeCommand", async () => {
        try {
            // Get command from user
            const command = await vscode.window.showInputBox({
                prompt: "Enter the command to execute",
                placeHolder: "e.g., npm install, ls, dir, python script.py",
                validateInput: (value) => {
                    if (!value || value.trim() === "") {
                        return "Command cannot be empty";
                    }
                    return null;
                }
            });
            if (!command) {
                Logger_1.Logger.debug("Command execution cancelled - no command provided");
                return;
            }
            // Use default working directory (workspace root or current directory)
            const workspaceFolders = vscode.workspace.workspaceFolders;
            const cwd = workspaceFolders ? workspaceFolders[0].uri.fsPath : require("process").cwd();
            Logger_1.Logger.info(`Executing command: ${command} in directory: ${cwd}`);
            Logger_1.Logger.debug(`Command details - Input: "${command}", Working Directory: "${cwd}"`);
            // Clear previous output and show the output channel
            outputChannel.clear();
            outputChannel.show(true);
            // Add header to output
            const timestamp = new Date().toLocaleString();
            outputChannel.appendLine("=".repeat(80));
            outputChannel.appendLine(`Terminal Manager - Command Execution`);
            outputChannel.appendLine(`Timestamp: ${timestamp}`);
            outputChannel.appendLine(`Command: ${command}`);
            outputChannel.appendLine(`Working Directory: ${cwd}`);
            outputChannel.appendLine("=".repeat(80));
            outputChannel.appendLine("");
            // Add debug info to output
            outputChannel.appendLine(`[DEBUG] Creating terminal for directory: ${cwd}`);
            Logger_1.Logger.debug(`Creating terminal for directory: ${cwd}`);
            // Create new terminal and execute command
            const terminalInfo = await terminalManager.getOrCreateTerminal(cwd.trim());
            terminalInfo.terminal.show();
            outputChannel.appendLine(`[DEBUG] Terminal created with ID: ${terminalInfo.id}`);
            outputChannel.appendLine(`[DEBUG] Terminal busy status: ${terminalInfo.busy}`);
            Logger_1.Logger.debug(`Terminal created - ID: ${terminalInfo.id}, Busy: ${terminalInfo.busy}`);
            // Execute the command and capture output
            outputChannel.appendLine(`[DEBUG] Starting command execution...`);
            Logger_1.Logger.debug(`Starting command execution: ${command}`);
            const terminalProcess = terminalManager.runCommand(terminalInfo, command.trim());
            // Show progress and capture output
            let fullOutput = "";
            let hasOutput = false;
            let lineCount = 0;
            vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: `Executing: ${command}`,
                cancellable: true
            }, async (progress, token) => {
                outputChannel.appendLine(`[DEBUG] Command process started, listening for output...`);
                Logger_1.Logger.debug("Command process started, setting up output listeners");
                terminalProcess.on("line", (line) => {
                    hasOutput = true;
                    lineCount++;
                    fullOutput += line + "\n";
                    // Update progress
                    progress.report({ message: line.substring(0, 50) + (line.length > 50 ? "..." : "") });
                    // Output to OUTPUT panel with line numbers
                    outputChannel.appendLine(`[${lineCount.toString().padStart(3, '0')}] ${line}`);
                    // Debug logging
                    Logger_1.Logger.debug(`Output line ${lineCount}: ${line.substring(0, 100)}${line.length > 100 ? '...' : ''}`);
                    // Console output for development
                    console.log(`[Terminal Output ${lineCount}] ${line}`);
                });
                token.onCancellationRequested(() => {
                    outputChannel.appendLine(`[DEBUG] Command execution cancelled by user`);
                    Logger_1.Logger.info("Command execution cancelled by user");
                    terminalProcess.continue();
                });
                try {
                    outputChannel.appendLine(`[DEBUG] Waiting for command completion...`);
                    await terminalProcess;
                    // Command completed successfully
                    const completionTime = new Date().toLocaleString();
                    outputChannel.appendLine("");
                    outputChannel.appendLine("=".repeat(80));
                    outputChannel.appendLine(`[SUCCESS] Command completed successfully at ${completionTime}`);
                    outputChannel.appendLine(`[STATS] Total output lines: ${lineCount}`);
                    outputChannel.appendLine(`[STATS] Total output length: ${fullOutput.length} characters`);
                    outputChannel.appendLine("=".repeat(80));
                    const message = `Command completed: ${command} (${lineCount} lines of output)`;
                    vscode.window.showInformationMessage(message);
                    Logger_1.Logger.info(`Command completed successfully: ${command}, Lines: ${lineCount}, Length: ${fullOutput.length}`);
                    if (!hasOutput) {
                        outputChannel.appendLine(`[INFO] Command completed but no output was captured`);
                        Logger_1.Logger.debug("Command completed but no output was captured");
                    }
                }
                catch (error) {
                    // Command failed
                    const errorTime = new Date().toLocaleString();
                    outputChannel.appendLine("");
                    outputChannel.appendLine("=".repeat(80));
                    outputChannel.appendLine(`[ERROR] Command failed at ${errorTime}`);
                    outputChannel.appendLine(`[ERROR] Error details: ${error}`);
                    outputChannel.appendLine(`[STATS] Output lines before failure: ${lineCount}`);
                    outputChannel.appendLine("=".repeat(80));
                    const errorMessage = `Command failed: ${error}`;
                    vscode.window.showErrorMessage(errorMessage);
                    Logger_1.Logger.error(`Command failed: ${command}`, error);
                    Logger_1.Logger.debug(`Command failure details - Lines captured: ${lineCount}, Error: ${error}`);
                }
            });
        }
        catch (error) {
            const errorMessage = `Failed to execute command: ${error}`;
            outputChannel.appendLine("");
            outputChannel.appendLine("=".repeat(80));
            outputChannel.appendLine(`[FATAL ERROR] Failed to execute command`);
            outputChannel.appendLine(`[FATAL ERROR] Error details: ${error}`);
            outputChannel.appendLine("=".repeat(80));
            vscode.window.showErrorMessage(errorMessage);
            Logger_1.Logger.error(errorMessage, error);
            Logger_1.Logger.debug(`Fatal error details: ${error}`);
        }
    });
    // Register the command
    context.subscriptions.push(executeCommandCommand);
    Logger_1.Logger.info("Commands registered successfully");
    Logger_1.Logger.debug("Output channel created and command registered");
}
exports.registerCommands = registerCommands;
//# sourceMappingURL=commands.js.map