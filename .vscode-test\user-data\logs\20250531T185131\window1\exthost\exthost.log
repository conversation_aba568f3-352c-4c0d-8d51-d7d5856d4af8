2025-05-31 18:51:33.472 [info] Extension host with pid 66528 started
2025-05-31 18:51:33.496 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-05-31 18:51:33.546 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-05-31 18:51:33.550 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-05-31 18:51:33.583 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-05-31 18:51:33.725 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.openRepositoryInParentFolders', provide the URI of a resource or 'null' for any resource.
2025-05-31 18:51:33.725 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.showProgress', provide the URI of a resource or 'null' for any resource.
2025-05-31 18:51:33.742 [info] Eager extensions activated
2025-05-31 18:51:33.822 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-05-31 18:51:33.827 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-05-31 18:51:33.844 [info] ExtensionService#_doActivateExtension undefined_publisher.terminal-manager, startup: false, activationEvent: 'onStartupFinished'
2025-05-31 18:51:33.897 [info] Extension host terminating: renderer closed the MessagePort
2025-05-31 18:51:33.907 [info] Extension host with pid 66528 exiting with code 0
