import * as vscode from "vscode";
import { TerminalProcessResultPromise } from "./TerminalProcess";
import { TerminalInfo } from "./TerminalRegistry";
declare module "vscode" {
    interface Window {
        onDidStartTerminalShellExecution?: (listener: (e: any) => any, thisArgs?: any, disposables?: vscode.Disposable[]) => vscode.Disposable;
    }
}
export declare class TerminalManager {
    private terminalIds;
    private processes;
    private disposables;
    private shellIntegrationTimeout;
    constructor();
    private findTerminalInfoByTerminal;
    private isCwdMatchingExpected;
    runCommand(terminalInfo: TerminalInfo, command: string): TerminalProcessResultPromise;
    getOrCreateTerminal(cwd: string): Promise<TerminalInfo>;
    createNewTerminal(cwd: string, customName?: string): Promise<TerminalInfo>;
    getTerminals(busy: boolean): {
        id: number;
        lastCommand: string;
    }[];
    getUnretrievedOutput(terminalId: number): string;
    isProcessHot(terminalId: number): boolean;
    disposeAll(): void;
    setShellIntegrationTimeout(timeout: number): void;
}
//# sourceMappingURL=TerminalManager.d.ts.map