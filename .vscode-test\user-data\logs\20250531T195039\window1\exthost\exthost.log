2025-05-31 19:50:41.679 [info] Extension host with pid 82724 started
2025-05-31 19:50:41.777 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-05-31 19:50:41.910 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-05-31 19:50:41.919 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-05-31 19:50:41.991 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-05-31 19:50:42.170 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.openRepositoryInParentFolders', provide the URI of a resource or 'null' for any resource.
2025-05-31 19:50:42.170 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.showProgress', provide the URI of a resource or 'null' for any resource.
2025-05-31 19:50:42.204 [info] Eager extensions activated
2025-05-31 19:50:42.322 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-05-31 19:50:42.326 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-05-31 19:50:42.345 [info] ExtensionService#_doActivateExtension undefined_publisher.terminal-manager, startup: false, activationEvent: 'onStartupFinished'
2025-05-31 19:50:42.420 [info] Extension host terminating: renderer closed the MessagePort
2025-05-31 19:50:42.436 [info] Extension host with pid 82724 exiting with code 0
