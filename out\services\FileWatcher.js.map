{"version": 3, "file": "FileWatcher.js", "sourceRoot": "", "sources": ["../../src/services/FileWatcher.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,mDAAqC;AACrC,2CAA6B;AAC7B,6CAA0C;AAE1C,MAAa,WAAW;IAMvB,YAAoB,OAAgC;QAAhC,YAAO,GAAP,OAAO,CAAyB;QAHnC,kBAAa,GAAG,IAAI,CAAC,CAAC,qBAAqB;QACpD,gBAAW,GAAG,KAAK,CAAC;QAG3B,IAAI,CAAC,UAAU,EAAE,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,UAAU;QACvB,IAAI;YACH,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;YAC3D,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;gBACvD,eAAM,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;gBAChE,OAAO;aACP;YAED,MAAM,QAAQ,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;YAChD,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAE3C,iCAAiC;YACjC,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,mBAAmB,EAAE;gBAClD,GAAG,EAAE,OAAO;gBACZ,OAAO,EAAE;oBACR,cAAc;oBACd,cAAc;oBACd,oBAAoB;oBACpB,WAAW;oBACX,YAAY;oBACZ,gBAAgB;iBAChB;gBACD,aAAa,EAAE,IAAI;gBACnB,UAAU,EAAE,IAAI;aAChB,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,EAAE;gBACtC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,EAAE;gBACnC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,EAAE;gBACtC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAClC,eAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC;YAEH,iDAAiD;YACjD,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;YAC5D,MAAM,cAAc,GAAG,QAAQ,CAAC,KAAK,CAAC,eAAe,EAAE;gBACtD,aAAa,EAAE,IAAI;aACnB,CAAC,CAAC;YAEH,cAAc,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;gBAChC,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,uCAAuC,OAAO,EAAE,CAAC,CAAC;SAE9D;QAAC,OAAO,KAAK,EAAE;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;SACzD;IACF,CAAC;IAEO,cAAc,CAAC,QAAgB,EAAE,MAAc;QACtD,IAAI,IAAI,CAAC,WAAW,EAAE;YACrB,eAAM,CAAC,KAAK,CAAC,6CAA6C,QAAQ,EAAE,CAAC,CAAC;YACtE,OAAO;SACP;QAED,yBAAyB;QACzB,IAAI,IAAI,CAAC,aAAa,EAAE;YACvB,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACjC;QAED,gCAAgC;QAChC,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,GAAG,EAAE;YACpC,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACxC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAEvB,eAAM,CAAC,KAAK,CAAC,mCAAmC,QAAQ,KAAK,MAAM,GAAG,CAAC,CAAC;IACzE,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,WAAmB,EAAE,MAAc;QAChE,IAAI,IAAI,CAAC,WAAW,EAAE;YACrB,OAAO;SACP;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,eAAM,CAAC,IAAI,CAAC,qCAAqC,WAAW,KAAK,MAAM,GAAG,CAAC,CAAC;QAE5E,IAAI;YACH,2BAA2B;YAC3B,MAAM,aAAa,GAAG,QAAQ,MAAM,KAAK,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,0BAA0B,CAAC;YAE9F,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CACxD,aAAa,EACb,EAAE,KAAK,EAAE,KAAK,EAAE,EAChB,YAAY,EACZ,oBAAoB,CACpB,CAAC;YAEF,IAAI,MAAM,KAAK,YAAY,EAAE;gBAC5B,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;aAC3B;iBAAM,IAAI,MAAM,KAAK,oBAAoB,EAAE;gBAC3C,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;aAC/B;iBAAM;gBACN,yDAAyD;gBACzD,UAAU,CAAC,KAAK,IAAI,EAAE;oBACrB,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;gBAC5B,CAAC,EAAE,IAAI,CAAC,CAAC;aACT;SAED;QAAC,OAAO,KAAK,EAAE;YACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4BAA4B,KAAK,EAAE,CAAC,CAAC;SACpE;gBAAS;YACT,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;SACzB;IACF,CAAC;IAEO,KAAK,CAAC,aAAa;QAC1B,IAAI;YACH,eAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAE9C,gBAAgB;YAChB,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAChC,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;gBAC9C,KAAK,EAAE,wBAAwB;gBAC/B,WAAW,EAAE,KAAK;aAClB,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;gBACrB,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC,CAAC;gBAEtD,oCAAoC;gBACpC,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,CAAC,CAAC;YACvE,CAAC,CAAC,CAAC;SAEH;QAAC,OAAO,KAAK,EAAE;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;SACZ;IACF,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC9B,IAAI;YACH,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;YACpE,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,KAAK,EAAE,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;YAE/E,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yCAAyC,CAAC,CAAC;YAChF,eAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YAE5C,IAAI,CAAC,OAAO,EAAE,CAAC;SAEf;QAAC,OAAO,KAAK,EAAE;YACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,kCAAkC,KAAK,EAAE,CAAC,CAAC;SAC1E;IACF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QACjB,IAAI,IAAI,CAAC,WAAW,EAAE;YACrB,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,sCAAsC,CAAC,CAAC;YACzE,OAAO;SACP;QAED,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACpD,gDAAgD,EAChD,EAAE,KAAK,EAAE,IAAI,EAAE,EACf,QAAQ,EACR,QAAQ,CACR,CAAC;QAEF,IAAI,MAAM,KAAK,QAAQ,EAAE;YACxB,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;SAC3B;IACF,CAAC;IAED,OAAO;QACN,IAAI,IAAI,CAAC,aAAa,EAAE;YACvB,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACjC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;SACrB;QAED,eAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;IACtC,CAAC;CACD;AApMD,kCAoMC"}